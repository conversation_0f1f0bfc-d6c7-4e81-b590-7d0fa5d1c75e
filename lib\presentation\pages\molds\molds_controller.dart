import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_change_history_repository.dart';
import 'package:intl/intl.dart';

class MoldsController extends GetxController {
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldChangeHistoryRepository _historyRepository =
      Get.find<MoldChangeHistoryRepository>();

  final RxBool isLoading = false.obs;
  final RxBool isLoadingHistory = false.obs;
  final RxList<Mold> molds = <Mold>[].obs;
  final RxList<Mold> filteredMolds = <Mold>[].obs;
  final RxMap<String, Map<String, dynamic>> machinesInfo =
      <String, Map<String, dynamic>>{}.obs;

  // تاريخ استخدام الإسطمبة
  final RxList<Map<String, dynamic>> moldUsageHistory =
      <Map<String, dynamic>>[].obs;

  // إحصائيات الإسطمبات
  final RxMap<String, dynamic> moldStatistics = <String, dynamic>{}.obs;

  // حالة التصفية الحالية
  final RxString currentFilter = 'all'.obs;

  @override
  void onInit() {
    super.onInit();
    // تهيئة البيانات
    fetchMolds();
    fetchMachines();
    // إضافة بيانات تجريبية إذا كانت قائمة الإسطمبات فارغة
    _addDemoDataIfEmpty();

    // تعيين التصفية الافتراضية لعرض جميع الإسطمبات
    filterMolds('all');
  }

  // تصفية الإسطمبات حسب الحالة
  void filterMolds(String filter) {
    currentFilter.value = filter;
    switch (filter) {
      case 'available':
        filteredMolds.value =
            molds.where((m) => m.status == MoldStatus.available).toList();
        break;
      case 'inUse':
        filteredMolds.value =
            molds.where((m) => m.status == MoldStatus.inUse).toList();
        break;
      case 'maintenance':
        filteredMolds.value =
            molds.where((m) => m.status == MoldStatus.maintenance).toList();
        break;
      default:
        filteredMolds.value = molds.toList();
    }
  }

  // تحديث إحصائيات الإسطمبات
  void updateStatistics() {
    moldStatistics.value = {
      'total': molds.length,
      'available': getAvailableMoldsCount(),
      'inUse': getInUseMoldsCount(),
      'maintenance': getMaintenanceMoldsCount(),
      'totalProduction': calculateTotalProduction(),
      'averageUsageTime': calculateAverageUsageTime(),
    };
  }

  // حساب عدد الإسطمبات المتاحة
  int getAvailableMoldsCount() {
    return molds.where((m) => m.status == MoldStatus.available).length;
  }

  // حساب عدد الإسطمبات قيد الاستخدام
  int getInUseMoldsCount() {
    return molds.where((m) => m.status == MoldStatus.inUse).length;
  }

  // حساب عدد الإسطمبات في الصيانة
  int getMaintenanceMoldsCount() {
    return molds.where((m) => m.status == MoldStatus.maintenance).length;
  }

  // تنسيق التاريخ
  String formatDate(DateTime? date) {
    if (date == null) return 'غير محدد';
    return DateFormat('yyyy/MM/dd').format(date);
  }

  // الحصول على تاريخ آخر صيانة
  String getLastMaintenanceDate(String moldId) {
    final history =
        moldUsageHistory
            .where((h) => h['moldId'] == moldId && h['type'] == 'maintenance')
            .toList();
    if (history.isEmpty) return 'لا يوجد';
    return formatDate(DateTime.parse(history.last['date']));
  }

  // حساب إجمالي وقت الاستخدام
  String getTotalUsageTime(String moldId) {
    final history =
        moldUsageHistory
            .where((h) => h['moldId'] == moldId && h['type'] == 'usage')
            .toList();
    final totalHours = history.fold<double>(
      0,
      (sum, h) => sum + (h['duration'] ?? 0),
    );
    return '${totalHours.toStringAsFixed(1)} ساعة';
  }

  // حساب عدد القطع المنتجة
  String getTotalProducedParts(String moldId) {
    final history =
        moldUsageHistory
            .where((h) => h['moldId'] == moldId && h['type'] == 'production')
            .toList();
    final totalParts = history.fold<int>(
      0,
      (sum, h) => sum + ((h['quantity'] ?? 0) as int),
    );
    return totalParts.toString();
  }

  // حساب معدل الإنتاج
  String getProductionRate(String moldId) {
    final history =
        moldUsageHistory
            .where((h) => h['moldId'] == moldId && h['type'] == 'production')
            .toList();
    if (history.isEmpty) return '0 قطعة/ساعة';

    final totalParts = history.fold<int>(
      0,
      (sum, h) => sum + ((h['quantity'] ?? 0) as int),
    );
    final totalHours = history.fold<double>(
      0,
      (sum, h) => sum + (h['duration'] ?? 0),
    );

    if (totalHours == 0) return '0 قطعة/ساعة';
    final rate = (totalParts / totalHours).round();
    return '$rate قطعة/ساعة';
  }

  // إنشاء رسم بياني للاستخدام
  Widget buildUsageChart(String moldId) {
    // هنا يمكنك استخدام مكتبة fl_chart أو أي مكتبة أخرى لإنشاء الرسم البياني
    // يمكنك عرض معدلات الإنتاج أو أوقات الاستخدام على مدار الأيام/الأسابيع
    return Container(
      alignment: Alignment.center,
      child: const Text('الرسم البياني قيد التطوير'),
    );
  }

  // حساب إجمالي الإنتاج لجميع الإسطمبات
  int calculateTotalProduction() {
    int total = 0;
    for (var mold in molds) {
      if (mold.id != null) {
        total += int.parse(getTotalProducedParts(mold.id!));
      }
    }
    return total;
  }

  // حساب متوسط وقت الاستخدام
  String calculateAverageUsageTime() {
    if (molds.isEmpty) return '0 ساعة';
    double totalHours = 0;
    int count = 0;

    for (var mold in molds) {
      if (mold.id != null) {
        final usage = getTotalUsageTime(mold.id!);
        final hours = double.tryParse(usage.split(' ').first) ?? 0;
        totalHours += hours;
        count++;
      }
    }

    if (count == 0) return '0 ساعة';
    return '${(totalHours / count).toStringAsFixed(1)} ساعة';
  }

  // الحصول على قائمة الإسطمبات
  Future<void> fetchMolds() async {
    isLoading.value = true;
    try {
      final moldsList = await _moldRepository.getAllMolds();
      molds.assignAll(moldsList);

      // تحديث القائمة المصفاة أيضًا بناءً على التصفية الحالية
      filterMolds(currentFilter.value);

      // تحديث الإحصائيات
      updateStatistics();
    } catch (e) {
      _showError('حدث خطأ أثناء جلب الإسطمبات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // الحصول على قائمة الماكينات
  Future<void> fetchMachines() async {
    try {
      final machines = await _machineRepository.getAllMachines();

      // إنشاء قاموس للوصول السريع إلى معلومات الماكينات
      for (var machine in machines) {
        if (machine.id != null) {
          machinesInfo[machine.id!] = {
            'id': machine.id,
            'name': machine.name,
            'model': machine.model,
            'status': machine.status,
          };
        }
      }
    } catch (e) {
      _showError('حدث خطأ أثناء جلب الماكينات: $e');
    }
  }

  // الحصول على معلومات الماكينة
  Map<String, dynamic>? getMachineInfo(String? machineId) {
    if (machineId == null) return null;
    return machinesInfo[machineId];
  }

  // إضافة إسطمبة جديدة
  Future<void> addMold(Mold mold) async {
    isLoading.value = true;
    try {
      await _moldRepository.createMold(mold);
      await fetchMolds();
      _showSuccess('تم إضافة الإسطمبة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة الإسطمبة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث إسطمبة
  Future<void> updateMold(Mold mold) async {
    isLoading.value = true;
    try {
      await _moldRepository.updateMold(mold);
      await fetchMolds();
      _showSuccess('تم تحديث الإسطمبة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث الإسطمبة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // حذف إسطمبة
  Future<void> deleteMold(String id) async {
    isLoading.value = true;
    try {
      await _moldRepository.deleteMold(id);
      molds.removeWhere((mold) => mold.id == id);
      _showSuccess('تم حذف الإسطمبة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء حذف الإسطمبة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تغيير حالة الإسطمبة
  Future<void> changeMoldStatus(String id, String status) async {
    isLoading.value = true;
    try {
      await _moldRepository.changeMoldStatus(id, status);
      await fetchMolds();
      _showSuccess('تم تغيير حالة الإسطمبة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تغيير حالة الإسطمبة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // جلب تاريخ استخدام إسطمبة معينة
  Future<void> fetchMoldUsageHistory(String moldId) async {
    isLoadingHistory.value = true;

    try {
      final history = await _historyRepository.getMoldUsageHistory(moldId);
      moldUsageHistory.assignAll(history);
    } catch (e) {
      _showError('حدث خطأ أثناء جلب سجل استخدام الإسطمبة: $e');
    } finally {
      isLoadingHistory.value = false;
    }
  }

  // عرض سجل استخدام إسطمبة معينة
  void showMoldUsageHistoryDialog(String moldId, String moldName) async {
    try {
      await fetchMoldUsageHistory(moldId);

      Get.dialog(
        AlertDialog(
          title: Text('سجل استخدام الإسطمبة - $moldName'),
          content: SizedBox(
            width: 500,
            height: 400,
            child: Obx(() {
              if (isLoadingHistory.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (moldUsageHistory.isEmpty) {
                return const Center(
                  child: Text('لا يوجد سجل لاستخدام هذه الإسطمبة'),
                );
              }

              return ListView.builder(
                itemCount: moldUsageHistory.length,
                itemBuilder: (context, index) {
                  try {
                    final record = moldUsageHistory[index];

                    // التأكد من وجود البيانات المطلوبة
                    if (record['changeDate'] == null ||
                        record['machineName'] == null) {
                      return const ListTile(title: Text('بيانات غير مكتملة'));
                    }

                    DateTime changeDate;
                    String formattedDate = '';

                    try {
                      changeDate = DateTime.parse(
                        record['changeDate'].toString(),
                      );
                      formattedDate = DateFormat(
                        'yyyy/MM/dd - HH:mm',
                      ).format(changeDate);
                    } catch (e) {
                      formattedDate = 'تاريخ غير صالح';
                    }

                    return Card(
                      margin: const EdgeInsets.only(bottom: 8),
                      child: ListTile(
                        title: Text('تم التركيب على: ${record['machineName']}'),
                        subtitle: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text('التاريخ: $formattedDate'),
                            if (record['notes'] != null &&
                                record['notes'].toString().isNotEmpty)
                              Text('ملاحظات: ${record['notes']}'),
                          ],
                        ),
                        leading: const Icon(
                          Icons.precision_manufacturing,
                          size: 32,
                        ),
                      ),
                    );
                  } catch (e) {
                    return ListTile(title: Text('خطأ في عرض السجل: $e'));
                  }
                },
              );
            }),
          ),
          actions: [
            TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
          ],
        ),
      );
    } catch (e) {
      _showError('حدث خطأ أثناء عرض سجل استخدام الإسطمبة: $e');
    }
  }

  // عرض رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // إضافة بيانات تجريبية للإسطمبات
  Future<void> _addDemoDataIfEmpty() async {
    try {
      final moldsList = await _moldRepository.getAllMolds();
      if (moldsList.isEmpty) {
        // إضافة بعض الإسطمبات التجريبية
        await _moldRepository.createMold(
          Mold(
            name: 'عظمة دكر جيما',
            productCode: 'دكر جيما',
            status: MoldStatus.available,
            cavityCount: 8,
            singlePartWeight: 4.7,
            createdAt: DateTime.now(),
          ),
        );

        await _moldRepository.createMold(
          Mold(
            name: 'إسطمبة كوع 1/2 بوصة',
            productCode: 'K12',
            status: MoldStatus.inUse,
            cavityCount: 4,
            singlePartWeight: 12.5,
            createdAt: DateTime.now(),
          ),
        );

        await _moldRepository.createMold(
          Mold(
            name: 'إسطمبة تي 3/4',
            productCode: 'T34',
            status: MoldStatus.maintenance,
            cavityCount: 2,
            singlePartWeight: 18.2,
            createdAt: DateTime.now(),
          ),
        );

        // إعادة تحميل البيانات بعد إضافة البيانات التجريبية
        await fetchMolds();

        // تحديث القائمة المصفاة
        filterMolds(currentFilter.value);
      }
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة البيانات التجريبية: $e');
    }
  }
}
