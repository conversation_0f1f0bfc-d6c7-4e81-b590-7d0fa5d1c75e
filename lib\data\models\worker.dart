class Worker {
  final String? id;
  final String name; // اسم العامل
  final String code; // كود العامل
  final String jobTitle; // المسمى الوظيفي
  final double hourlyRate; // معدل الأجر بالساعة
  final String department; // القسم
  final bool isActive; // حالة العامل (نشط أو غير نشط)

  Worker({
    this.id,
    required this.name,
    required this.code,
    required this.jobTitle,
    required this.hourlyRate,
    required this.department,
    required this.isActive,
  });

  // تحويل Worker إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'jobTitle': jobTitle,
      'hourlyRate': hourlyRate,
      'department': department,
      'isActive': isActive ? 1 : 0,
    };
  }

  // إنشاء Worker من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Worker.fromMap(Map<String, dynamic> map) {
    return Worker(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      jobTitle: map['jobTitle'],
      hourlyRate: map['hourlyRate'],
      department: map['department'],
      isActive: map['isActive'] == 1,
    );
  }
}

// أقسام العمال
class WorkerDepartment {
  static const String assembly = 'التجميع';
  static const String injection = 'الحقن';
  static const String packaging = 'التغليف';
  static const String quality = 'مراقبة الجودة';
  static const String maintenance = 'الصيانة';
  static const String warehouse = 'المخزن';
}
