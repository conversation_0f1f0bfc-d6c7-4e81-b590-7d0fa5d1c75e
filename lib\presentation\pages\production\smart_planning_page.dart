import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/machine_capacity.dart';
import 'package:mostafa_final/data/models/smart_schedule.dart';
import 'package:mostafa_final/presentation/controllers/smart_planning_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class SmartPlanningPage extends StatelessWidget {
  const SmartPlanningPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(SmartPlanningController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('التخطيط الذكي للإنتاج'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadInitialData,
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.showAddOrderDialog,
        child: const Icon(Icons.add),
        tooltip: 'إضافة طلبية جديدة',
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: 'جاري تحميل البيانات...');
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildControlPanel(controller),
              const SizedBox(height: 20),
              _buildPerformanceStats(controller),
              const SizedBox(height: 20),
              _buildAlertsSection(controller),
              const SizedBox(height: 20),
              _buildPendingOrdersSection(controller),
              const SizedBox(height: 20),
              _buildCurrentScheduleSection(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildControlPanel(SmartPlanningController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لوحة التحكم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => ListTile(
                      title: const Text('تاريخ الجدولة'),
                      subtitle: Text(
                        DateFormat('yyyy-MM-dd').format(controller.selectedDate.value),
                      ),
                      trailing: const Icon(Icons.calendar_today),
                      onTap: () async {
                        final date = await showDatePicker(
                          context: Get.context!,
                          initialDate: controller.selectedDate.value,
                          firstDate: DateTime.now(),
                          lastDate: DateTime.now().add(const Duration(days: 365)),
                        );
                        if (date != null) {
                          controller.changeSelectedDate(date);
                        }
                      },
                    ),
                  ),
                ),
                Expanded(
                  child: Obx(
                    () => ListTile(
                      title: const Text('عدد الورديات'),
                      subtitle: Text('${controller.shiftsCount.value}'),
                      trailing: DropdownButton<double>(
                        value: controller.shiftsCount.value,
                        items: const [
                          DropdownMenuItem(value: 1.0, child: Text('1')),
                          DropdownMenuItem(value: 1.5, child: Text('1.5')),
                          DropdownMenuItem(value: 2.0, child: Text('2')),
                          DropdownMenuItem(value: 3.0, child: Text('3')),
                        ],
                        onChanged: (value) {
                          if (value != null) {
                            controller.changeShiftsCount(value);
                          }
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: Obx(
                () => ElevatedButton.icon(
                  onPressed: controller.isGeneratingSchedule.value
                      ? null
                      : controller.generateSmartSchedule,
                  icon: controller.isGeneratingSchedule.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.auto_awesome),
                  label: Text(
                    controller.isGeneratingSchedule.value
                        ? 'جاري إنشاء الجدولة...'
                        : 'إنشاء جدولة ذكية',
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.all(16),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceStats(SmartPlanningController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات الأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final stats = controller.performanceStats;
              if (stats.isEmpty) {
                return const Text('لا توجد إحصائيات متاحة');
              }

              return Row(
                children: [
                  Expanded(
                    child: _buildStatCard(
                      'متوسط الكفاءة',
                      '${(stats['averageEfficiency'] ?? 0).toStringAsFixed(1)}%',
                      Icons.trending_up,
                      Colors.blue,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'الماكينات عالية الكفاءة',
                      '${stats['highEfficiencyMachines'] ?? 0}',
                      Icons.star,
                      Colors.green,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: _buildStatCard(
                      'الماكينات منخفضة الكفاءة',
                      '${stats['lowEfficiencyMachines'] ?? 0}',
                      Icons.warning,
                      Colors.orange,
                    ),
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection(SmartPlanningController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'التنبيهات',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Obx(() => Chip(
                  label: Text('${controller.alerts.length}'),
                  backgroundColor: controller.alerts.isEmpty 
                      ? Colors.green.shade100 
                      : Colors.red.shade100,
                )),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.alerts.isEmpty) {
                return const ListTile(
                  leading: Icon(Icons.check_circle, color: Colors.green),
                  title: Text('لا توجد تنبيهات'),
                  subtitle: Text('جميع العمليات تسير بشكل طبيعي'),
                );
              }

              return Column(
                children: controller.alerts.take(3).map((alert) {
                  return ListTile(
                    leading: Icon(
                      _getAlertIcon(alert.type),
                      color: _getAlertColor(alert.severity),
                    ),
                    title: Text(alert.type.displayName),
                    subtitle: Text(alert.message),
                    trailing: IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: () => controller.markAlertAsRead(alert.id!),
                    ),
                  );
                }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildPendingOrdersSection(SmartPlanningController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'الطلبيات المعلقة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Obx(() => Chip(
                  label: Text('${controller.pendingOrders.length}'),
                  backgroundColor: Colors.blue.shade100,
                )),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.pendingOrders.isEmpty) {
                return const ListTile(
                  leading: Icon(Icons.inbox, color: Colors.grey),
                  title: Text('لا توجد طلبيات معلقة'),
                  subtitle: Text('جميع الطلبيات مكتملة'),
                );
              }

              return Column(
                children: controller.pendingOrders.take(5).map((order) {
                  return ListTile(
                    leading: CircleAvatar(
                      backgroundColor: _getPriorityColor(order.priority),
                      child: Text(
                        OrderPriority.getPriorityName(order.priority)[0],
                        style: const TextStyle(color: Colors.white),
                      ),
                    ),
                    title: Text(order.productName),
                    subtitle: Text(
                      'الكمية: ${order.remainingQuantity} | التسليم: ${DateFormat('yyyy-MM-dd').format(order.dueDate)}',
                    ),
                    trailing: order.isOverdue
                        ? const Icon(Icons.warning, color: Colors.red)
                        : null,
                  );
                }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildCurrentScheduleSection(SmartPlanningController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الجدولة الحالية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final schedule = controller.currentSchedule.value;
              if (schedule == null) {
                return const ListTile(
                  leading: Icon(Icons.schedule, color: Colors.grey),
                  title: Text('لا توجد جدولة نشطة'),
                  subtitle: Text('قم بإنشاء جدولة ذكية جديدة'),
                );
              }

              return Column(
                children: [
                  ListTile(
                    leading: const Icon(Icons.calendar_today, color: Colors.blue),
                    title: Text('تاريخ الجدولة: ${DateFormat('yyyy-MM-dd').format(schedule.scheduleDate)}'),
                    subtitle: Text('استغلال الطاقة: ${schedule.totalCapacityUtilization.toStringAsFixed(1)}%'),
                  ),
                  ListTile(
                    leading: const Icon(Icons.assignment, color: Colors.green),
                    title: Text('عدد المهام: ${schedule.tasks.length}'),
                    subtitle: Text('الإنتاج المخطط: ${schedule.totalPlannedProduction} قطعة'),
                  ),
                  if (schedule.tasks.isNotEmpty) ...[
                    const Divider(),
                    const Text(
                      'المهام المجدولة:',
                      style: TextStyle(fontWeight: FontWeight.bold),
                    ),
                    ...schedule.tasks.take(3).map((task) {
                      return ListTile(
                        leading: CircleAvatar(
                          backgroundColor: _getTaskStatusColor(task.status),
                          child: Text(task.status.displayName[0]),
                        ),
                        title: Text(task.productCode),
                        subtitle: Text(
                          'الكمية: ${task.plannedQuantity} | الوقت: ${task.estimatedRunTime.toStringAsFixed(1)} ساعة',
                        ),
                      );
                    }).toList(),
                  ],
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  IconData _getAlertIcon(AlertType type) {
    switch (type) {
      case AlertType.capacityOverload:
        return Icons.warning;
      case AlertType.materialShortage:
        return Icons.inventory;
      case AlertType.machineUnavailable:
        return Icons.build;
      case AlertType.scheduleDelay:
        return Icons.schedule;
      case AlertType.priorityConflict:
        return Icons.priority_high;
      case AlertType.maintenanceDue:
        return Icons.build_circle;
    }
  }

  Color _getAlertColor(AlertSeverity severity) {
    switch (severity) {
      case AlertSeverity.low:
        return Colors.blue;
      case AlertSeverity.medium:
        return Colors.orange;
      case AlertSeverity.high:
        return Colors.red;
      case AlertSeverity.critical:
        return Colors.purple;
    }
  }

  Color _getPriorityColor(int priority) {
    switch (priority) {
      case OrderPriority.high:
        return Colors.red;
      case OrderPriority.medium:
        return Colors.orange;
      case OrderPriority.low:
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Color _getTaskStatusColor(TaskStatus status) {
    switch (status) {
      case TaskStatus.planned:
        return Colors.blue;
      case TaskStatus.inProgress:
        return Colors.orange;
      case TaskStatus.completed:
        return Colors.green;
      case TaskStatus.delayed:
        return Colors.red;
      case TaskStatus.cancelled:
        return Colors.grey;
    }
  }
}
