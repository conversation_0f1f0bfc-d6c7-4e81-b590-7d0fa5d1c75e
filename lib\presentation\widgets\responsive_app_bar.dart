import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// شريط تطبيق متجاوب مع حجم الشاشة
class ResponsiveAppBar extends StatelessWidget implements PreferredSizeWidget {
  final String title;
  final List<Widget>? actions;
  final bool centerTitle;
  final bool automaticallyImplyLeading;
  final Widget? leading;
  final Color? backgroundColor;
  final double? elevation;
  final Widget? flexibleSpace;
  final PreferredSizeWidget? bottom;
  final double? toolbarHeight;
  final Widget? titleWidget;

  const ResponsiveAppBar({
    super.key,
    required this.title,
    this.actions,
    this.centerTitle = true,
    this.automaticallyImplyLeading = true,
    this.leading,
    this.backgroundColor,
    this.elevation,
    this.flexibleSpace,
    this.bottom,
    this.toolbarHeight,
    this.titleWidget,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title:
          titleWidget ??
          TextUtils.responsiveText(
            title,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      actions:
          actions?.map((action) {
            // تطبيق تنسيق متجاوب على الأزرار في شريط التطبيق
            if (action is IconButton) {
              return IconButton(
                icon: action.icon,
                onPressed: action.onPressed,
                tooltip: action.tooltip,
                iconSize: ScreenSize.isSmallScreen ? 20 : 24,
                padding: EdgeInsets.all(ScreenSize.getPadding(8)),
              );
            }
            return action;
          }).toList(),
      backgroundColor: backgroundColor,
      elevation: elevation,
      flexibleSpace: flexibleSpace,
      bottom: bottom,
      toolbarHeight: toolbarHeight,
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(
    toolbarHeight ?? (bottom != null ? kToolbarHeight + 48 : kToolbarHeight),
  );

  /// إنشاء شريط تطبيق متجاوب مع حقل بحث
  static ResponsiveAppBar withSearchField({
    required String title,
    required TextEditingController searchController,
    required Function(String) onSearch,
    List<Widget>? actions,
    bool centerTitle = true,
    bool automaticallyImplyLeading = true,
    Widget? leading,
    Color? backgroundColor,
    double? elevation,
    Widget? flexibleSpace,
    PreferredSizeWidget? bottom,
    double? toolbarHeight,
    String hintText = 'بحث...',
  }) {
    return ResponsiveAppBar(
      title: title,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      backgroundColor: backgroundColor,
      elevation: elevation,
      flexibleSpace: flexibleSpace,
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: Padding(
          padding: EdgeInsets.symmetric(
            horizontal: ScreenSize.getPadding(16),
            vertical: ScreenSize.getPadding(8),
          ),
          child: TextField(
            controller: searchController,
            decoration: InputDecoration(
              hintText: hintText,
              prefixIcon: const Icon(Icons.search),
              filled: true,
              fillColor: Colors.white,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              contentPadding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(16),
                vertical: ScreenSize.getPadding(8),
              ),
            ),
            onChanged: onSearch,
          ),
        ),
      ),
      actions: actions,
      toolbarHeight: toolbarHeight,
    );
  }
}
