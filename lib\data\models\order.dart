import 'package:intl/intl.dart';

class Order {
  final String? id;
  final DateTime date;
  final List<OrderItem> items;
  final double transportCost; // مصاريف النقل
  final double shippingCost; // مصاريف الشحن
  final double clearanceCost; // مصاريف التخليص
  final double customsDuty; // الجمارك
  final double exchangeRate; // سعر الصرف للعملة الأجنبية
  final String currency; // العملة (دولار، يوان، إلخ)

  Order({
    this.id,
    required this.date,
    required this.items,
    required this.transportCost,
    required this.shippingCost,
    required this.clearanceCost,
    required this.customsDuty,
    required this.exchangeRate,
    required this.currency,
  });

  double get totalOrderCost =>
      items.fold(0, (sum, item) => sum + (item.price * item.quantity));

  // حساب تكلفة القطعة بالجنيه المصري
  double calculateItemEgpCost(OrderItem item) {
    double itemRatio = (item.price * item.quantity) / totalOrderCost;

    double transportShare = transportCost * itemRatio;
    double shippingShare = shippingCost * itemRatio;
    double clearanceShare = clearanceCost * itemRatio;
    double customsShare = customsDuty * itemRatio;

    double totalCostForeign =
        item.price +
        (transportShare / item.quantity) +
        (shippingShare / item.quantity) +
        (clearanceShare / item.quantity) +
        (customsShare / item.quantity);

    return totalCostForeign * exchangeRate;
  }

  // تحويل Order إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': DateFormat('yyyy-MM-dd').format(date),
      'transportCost': transportCost,
      'shippingCost': shippingCost,
      'clearanceCost': clearanceCost,
      'customsDuty': customsDuty,
      'exchangeRate': exchangeRate,
      'currency': currency,
    };
  }

  // إنشاء Order من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Order.fromMap(Map<String, dynamic> map, List<OrderItem> items) {
    return Order(
      id: map['id'],
      date: DateFormat('yyyy-MM-dd').parse(map['date']),
      transportCost: map['transportCost'],
      shippingCost: map['shippingCost'],
      clearanceCost: map['clearanceCost'],
      customsDuty: map['customsDuty'],
      exchangeRate: map['exchangeRate'],
      currency: map['currency'],
      items: items,
    );
  }
}

class OrderItem {
  final String? id;
  final String? orderId;
  final String accessoryId;
  final String name;
  final double price; // بالعملة الأجنبية
  final int quantity;
  final double weight; // الوزن بالكيلوجرام

  OrderItem({
    this.id,
    this.orderId,
    required this.accessoryId,
    required this.name,
    required this.price,
    required this.quantity,
    required this.weight,
  });

  // تحويل OrderItem إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'orderId': orderId,
      'accessoryId': accessoryId,
      'name': name,
      'price': price,
      'quantity': quantity,
      'weight': weight,
    };
  }

  // إنشاء OrderItem من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      id: map['id'],
      orderId: map['orderId'],
      accessoryId: map['accessoryId'],
      name: map['name'],
      price: map['price'],
      quantity: map['quantity'],
      weight: map['weight'],
    );
  }
}
