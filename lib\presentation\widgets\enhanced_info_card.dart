import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';

class EnhancedInfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final Widget? trailing;
  final String? subtitle;

  const EnhancedInfoCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.iconColor,
    this.backgroundColor,
    this.onTap,
    this.trailing,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveIconColor = iconColor ?? EnhancedAppTheme.primaryColor;
    final effectiveBackgroundColor = backgroundColor ?? Colors.white;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: effectiveIconColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: effectiveIconColor, size: 24),
                  ),
                  if (trailing != null) trailing!,
                ],
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 14,
                  color: EnhancedAppTheme.textSecondary,
                  fontFamily: 'Cairo',
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                  color: EnhancedAppTheme.textPrimary,
                  fontFamily: 'Cairo',
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: const TextStyle(
                    fontSize: 12,
                    color: EnhancedAppTheme.textLight,
                    fontFamily: 'Cairo',
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

class EnhancedStatsCard extends StatelessWidget {
  final String title;
  final List<StatItem> stats;
  final Color? headerColor;
  final VoidCallback? onTap;
  final Widget? trailing;

  const EnhancedStatsCard({
    super.key,
    required this.title,
    required this.stats,
    this.headerColor,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    final effectiveHeaderColor = headerColor ?? EnhancedAppTheme.primaryColor;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: effectiveHeaderColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(EnhancedAppTheme.borderRadius),
                  topRight: Radius.circular(EnhancedAppTheme.borderRadius),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Cairo',
                    ),
                  ),
                  if (trailing != null) trailing!,
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children:
                    stats.map((stat) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 12),
                        child: Row(
                          children: [
                            if (stat.icon != null)
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color:
                                      stat.color?.withAlpha(25) ??
                                      EnhancedAppTheme.primaryColor.withAlpha(
                                        25,
                                      ),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Icon(
                                  stat.icon,
                                  color:
                                      stat.color ??
                                      EnhancedAppTheme.primaryColor,
                                  size: 20,
                                ),
                              ),
                            if (stat.icon != null) const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    stat.label,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      color: EnhancedAppTheme.textSecondary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    stat.value,
                                    style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          stat.color ??
                                          EnhancedAppTheme.textPrimary,
                                      fontFamily: 'Cairo',
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (stat.trailing != null) stat.trailing!,
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class StatItem {
  final String label;
  final String value;
  final IconData? icon;
  final Color? color;
  final Widget? trailing;

  StatItem({
    required this.label,
    required this.value,
    this.icon,
    this.color,
    this.trailing,
  });
}
