import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/di/dependency_injection.dart';
import 'package:mostafa_final/utils/performance_monitor.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة حقن التبعيات
  setUpAll(() {
    DependencyInjection.init();
  });

  group('اختبار أداء قاعدة البيانات بعد التحسينات', () {
    final performanceMonitor = PerformanceMonitor();
    final inventoryRepository = Get.find<InventoryRepository>();
    final salesRepository = Get.find<SalesRepository>();

    test('اختبار أداء استعلامات المخزون مع التخزين المؤقت', () async {
      // قياس أداء الاستعلام الأول (بدون تخزين مؤقت)
      final startDate = DateTime.now().subtract(const Duration(days: 30));
      final endDate = DateTime.now();

      final firstQueryTime = await performanceMonitor.measureDatabaseQuery(
        () => inventoryRepository.getRawMaterialConsumptionReport(
          startDate,
          endDate,
        ),
        'first_query',
      );

      // قياس أداء الاستعلام الثاني (مع تخزين مؤقت)
      final secondQueryTime = await performanceMonitor.measureDatabaseQuery(
        () => inventoryRepository.getRawMaterialConsumptionReport(
          startDate,
          endDate,
        ),
        'second_query',
      );

      // طباعة النتائج
      debugPrint('زمن الاستعلام الأول: $firstQueryTime ms');
      debugPrint('زمن الاستعلام الثاني: $secondQueryTime ms');

      // التحقق من أن الاستعلام الثاني أسرع من الأول (بسبب التخزين المؤقت)
      expect(secondQueryTime, lessThan(firstQueryTime));
    });

    test('اختبار أداء استعلامات المبيعات مع التخزين المؤقت', () async {
      // قياس أداء الاستعلام الأول (بدون تخزين مؤقت)
      final firstQueryTime = await performanceMonitor.measureDatabaseQuery(
        () => salesRepository.getTopSellingProducts(10),
        'first_query',
      );

      // قياس أداء الاستعلام الثاني (مع تخزين مؤقت)
      final secondQueryTime = await performanceMonitor.measureDatabaseQuery(
        () => salesRepository.getTopSellingProducts(10),
        'second_query',
      );

      // طباعة النتائج
      debugPrint('زمن الاستعلام الأول: $firstQueryTime ms');
      debugPrint('زمن الاستعلام الثاني: $secondQueryTime ms');

      // التحقق من أن الاستعلام الثاني أسرع من الأول (بسبب التخزين المؤقت)
      expect(secondQueryTime, lessThan(firstQueryTime));
    });

    test('اختبار أداء استعلامات المبيعات بالفترة الزمنية', () async {
      // قياس أداء الاستعلام
      final startDate = DateTime.now().subtract(const Duration(days: 30));
      final endDate = DateTime.now();

      final queryTime = await performanceMonitor.measureDatabaseQuery(
        () => salesRepository.getSalesByDateRange(startDate, endDate),
        'sales_by_date_range',
      );

      // طباعة النتائج
      debugPrint('زمن استعلام المبيعات بالفترة الزمنية: $queryTime ms');

      // التحقق من أن الاستعلام يعمل بشكل صحيح
      final result = await salesRepository.getSalesByDateRange(
        startDate,
        endDate,
      );
      expect(result, isA<List>());
    });

    test('اختبار أداء قاعدة البيانات تحت ضغط عالٍ', () async {
      // اختبار أداء قاعدة البيانات تحت ضغط عالٍ
      final results = await performanceMonitor.testDatabasePerformance();

      // طباعة النتائج
      debugPrint('نتائج اختبار أداء قاعدة البيانات:');
      debugPrint('استعلام بسيط: ${results['simple_query']} ms');
      debugPrint('استعلام معقد: ${results['complex_query']} ms');
      debugPrint('إدراج متعدد: ${results['bulk_insert']} ms');

      // التحقق من وجود النتائج
      expect(results.containsKey('simple_query'), isTrue);
      expect(results.containsKey('complex_query'), isTrue);
      expect(results.containsKey('bulk_insert'), isTrue);
    });

    test('اختبار الفهارس في قاعدة البيانات', () async {
      // الحصول على قاعدة البيانات
      final db = await DatabaseHelper.instance.database;

      // استعلام عن الفهارس الموجودة في قاعدة البيانات
      final indexes = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type = 'index'",
      );

      // طباعة الفهارس
      debugPrint('الفهارس الموجودة في قاعدة البيانات:');
      for (final index in indexes) {
        debugPrint('- ${index['name']}');
      }

      // التحقق من وجود الفهارس التي أضفناها
      final indexNames = indexes.map((index) => index['name']).toList();

      // التحقق من وجود بعض الفهارس الرئيسية
      expect(
        indexNames.any((name) => name.toString().contains('production')),
        isTrue,
      );
      expect(
        indexNames.any((name) => name.toString().contains('inventory')),
        isTrue,
      );
      expect(
        indexNames.any((name) => name.toString().contains('sales')),
        isTrue,
      );
    });
  });
}
