import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/assembly_repository.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:mostafa_final/utils/format_utils.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:flutter/services.dart';

class CostController extends GetxController {
  // المستودعات
  final ProductRepository _productRepository = Get.find<ProductRepository>();
  final RawMaterialRepository _rawMaterialRepository = Get.find<RawMaterialRepository>();
  final ProductionRepository _productionRepository = Get.find<ProductionRepository>();
  final AssemblyRepository _assemblyRepository = Get.find<AssemblyRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;

  // بيانات التكاليف
  final RxDouble rawMaterialsCost = 0.0.obs;
  final RxDouble productionCost = 0.0.obs;
  final RxDouble assemblyCost = 0.0.obs;
  final RxDouble totalCost = 0.0.obs;

  // البيانات الشهرية
  final RxList<Map<String, dynamic>> monthlyCostData =
      <Map<String, dynamic>>[].obs;

  // تكاليف المنتجات
  final RxList<Map<String, dynamic>> productCosts =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> filteredProductCosts =
      <Map<String, dynamic>>[].obs;
  final RxString selectedProductFilter = 'all'.obs;

  // توزيع التكاليف
  final RxList<Map<String, dynamic>> costDistribution =
      <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchAllData();
  }

  // جلب جميع البيانات
  Future<void> fetchAllData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        fetchRawMaterialsCost(),
        fetchProductionCost(),
        fetchAssemblyCost(),
        fetchProductCosts(),
        fetchMonthlyCostData(),
      ]);

      // حساب إجمالي التكاليف
      calculateTotalCost();

      // حساب توزيع التكاليف
      calculateCostDistribution();

      // تطبيق الفلتر الحالي
      filterProductCosts();
    } catch (e) {
      debugPrint('Error fetching cost data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // حساب إجمالي التكاليف
  void calculateTotalCost() {
    totalCost.value =
        rawMaterialsCost.value + productionCost.value + assemblyCost.value;
  }

  // حساب توزيع التكاليف
  void calculateCostDistribution() {
    final List<Map<String, dynamic>> distribution = [];

    if (totalCost.value > 0) {
      final double rawMaterialsPercentage =
          (rawMaterialsCost.value / totalCost.value) * 100;
      final double productionPercentage =
          (productionCost.value / totalCost.value) * 100;
      final double assemblyPercentage =
          (assemblyCost.value / totalCost.value) * 100;
      const double administrativePercentage =
          10.0; // قيمة ثابتة للمصاريف الإدارية

      distribution.addAll([
        {
          'name': 'المواد الخام',
          'value': rawMaterialsCost.value,
          'percentage': rawMaterialsPercentage,
          'color': Colors.blue,
        },
        {
          'name': 'الإنتاج',
          'value': productionCost.value,
          'percentage': productionPercentage,
          'color': Colors.green,
        },
        {
          'name': 'التجميع',
          'value': assemblyCost.value,
          'percentage': assemblyPercentage,
          'color': Colors.orange,
        },
        {
          'name': 'المصاريف الإدارية',
          'value': totalCost.value * (administrativePercentage / 100),
          'percentage': administrativePercentage,
          'color': Colors.purple,
        },
      ]);
    }

    costDistribution.assignAll(distribution);
  }

  // جلب تكلفة المواد الخام
  Future<void> fetchRawMaterialsCost() async {
    try {
      final rawMaterialsStats =
          await _rawMaterialRepository.getRawMaterialsStatistics();
      rawMaterialsCost.value = rawMaterialsStats['totalCost'] ?? 0.0;
    } catch (e) {
      debugPrint('Error fetching raw materials cost: $e');
    }
  }

  // جلب تكلفة الإنتاج
  Future<void> fetchProductionCost() async {
    try {
      final productionStats =
          await _productionRepository.getProductionStatistics();
      productionCost.value = productionStats['totalCost'] ?? 0.0;
    } catch (e) {
      debugPrint('Error fetching production cost: $e');
    }
  }

  // جلب تكلفة التجميع
  Future<void> fetchAssemblyCost() async {
    try {
      final assemblyStats = await _assemblyRepository.getAssemblyStatistics();
      assemblyCost.value = assemblyStats['totalCost'] ?? 0.0;
    } catch (e) {
      debugPrint('Error fetching assembly cost: $e');
    }
  }

  // جلب تكاليف المنتجات
  Future<void> fetchProductCosts() async {
    try {
      final products = await _productRepository.getAllProducts();
      final List<Map<String, dynamic>> costs = [];

      for (final product in products) {
        // حساب تكلفة المواد الخام للمنتج
        final double materialCost = _calculateMaterialCost(product);

        // حساب تكلفة الإنتاج للمنتج
        final double injectionCost = _calculateInjectionCost(product);

        // حساب تكلفة التجميع للمنتج
        final double assemblyCost =
            product.assemblyTime * 2.0 + product.packagingCost;

        // إجمالي التكلفة
        final double totalCost = materialCost + injectionCost + assemblyCost;

        costs.add({
          'id': product.id,
          'name': product.name,
          'code': product.code,
          'materialCost': materialCost,
          'productionCost': injectionCost,
          'assemblyCost': assemblyCost,
          'totalCost': totalCost,
        });
      }

      productCosts.assignAll(costs);
    } catch (e) {
      debugPrint('Error fetching product costs: $e');
    }
  }

  // حساب تكلفة المواد للمنتج
  double _calculateMaterialCost(Product product) {
    return product.components.fold(
      0.0,
      (sum, component) => sum + (component.cost * component.quantity),
    );
  }

  // حساب تكلفة الإنتاج للمنتج
  double _calculateInjectionCost(Product product) {
    // تكلفة الحقن للأجزاء البلاستيكية
    final plasticParts = product.components.where(
      (c) => c.type == ComponentType.plasticPart,
    );

    double cost = 0.0;
    for (final part in plasticParts) {
      // تكلفة تقديرية للحقن
      cost += part.cost * 0.3 * part.quantity;
    }

    return cost;
  }

  // تطبيق فلتر على تكاليف المنتجات
  void filterProductCosts() {
    switch (selectedProductFilter.value) {
      case 'high':
        // ترتيب المنتجات من الأعلى تكلفة إلى الأقل
        final sorted = List<Map<String, dynamic>>.from(productCosts)..sort(
          (a, b) =>
              (b['totalCost'] as double).compareTo(a['totalCost'] as double),
        );
        filteredProductCosts.assignAll(sorted);
        break;
      case 'low':
        // ترتيب المنتجات من الأقل تكلفة إلى الأعلى
        final sorted = List<Map<String, dynamic>>.from(productCosts)..sort(
          (a, b) =>
              (a['totalCost'] as double).compareTo(b['totalCost'] as double),
        );
        filteredProductCosts.assignAll(sorted);
        break;
      case 'all':
      default:
        // عرض كل المنتجات بالترتيب الأصلي
        filteredProductCosts.assignAll(productCosts);
        break;
    }
  }

  // جلب بيانات التكاليف الشهرية
  Future<void> fetchMonthlyCostData() async {
    try {
      // محاكاة لبيانات التكاليف الشهرية
      final List<Map<String, dynamic>> monthlyData = [];

      final List<String> months = [
        'يناير',
        'فبراير',
        'مارس',
        'أبريل',
        'مايو',
        'يونيو',
        'يوليو',
        'أغسطس',
        'سبتمبر',
        'أكتوبر',
        'نوفمبر',
        'ديسمبر',
      ];

      for (int i = 0; i < months.length; i++) {
        final double randomMultiplier = 0.8 + (i * 0.05);

        monthlyData.add({
          'month': months[i],
          'rawMaterials':
              rawMaterialsCost.value * randomMultiplier * (1 + (i % 3) * 0.1),
          'production':
              productionCost.value * randomMultiplier * (1 - (i % 2) * 0.05),
          'assembly':
              assemblyCost.value * randomMultiplier * (1 + (i % 4) * 0.15),
          'total': totalCost.value * randomMultiplier,
        });
      }

      monthlyCostData.assignAll(monthlyData);
    } catch (e) {
      debugPrint('Error generating monthly cost data: $e');
    }
  }

  // طباعة تقرير التكاليف
  Future<void> printCostReport() async {
    final pdf = pw.Document();

    // إضافة خط عربي للتقرير
    final arabicFont = await rootBundle.load("assets/fonts/Cairo-Regular.ttf");
    final ttf = pw.Font.ttf(arabicFont);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Center(
                child: pw.Text(
                  'تقرير التكاليف الإجمالية',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 20,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.SizedBox(height: 20),

              // ملخص التكاليف
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(border: pw.Border.all()),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'ملخص التكاليف',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'تكلفة المواد الخام:',
                          style: pw.TextStyle(font: ttf),
                        ),
                        pw.Text(
                          FormatUtils.formatCurrency(rawMaterialsCost.value),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'تكلفة الإنتاج:',
                          style: pw.TextStyle(font: ttf),
                        ),
                        pw.Text(
                          FormatUtils.formatCurrency(productionCost.value),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'تكلفة التجميع:',
                          style: pw.TextStyle(font: ttf),
                        ),
                        pw.Text(FormatUtils.formatCurrency(assemblyCost.value)),
                      ],
                    ),
                    pw.Divider(),
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                      children: [
                        pw.Text(
                          'إجمالي التكاليف:',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.Text(
                          FormatUtils.formatCurrency(totalCost.value),
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // جدول تكاليف المنتجات
              pw.Text(
                'تكاليف المنتجات',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  // رأس الجدول
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey300,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'المنتج',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'المواد',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'الإنتاج',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'التجميع',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'الإجمالي',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ),
                  // بيانات المنتجات
                  for (final product in filteredProductCosts.take(10))
                    pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            product['name'] as String,
                            style: pw.TextStyle(font: ttf),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(
                              product['materialCost'] as double,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(
                              product['productionCost'] as double,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(
                              product['assemblyCost'] as double,
                            ),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(
                              product['totalCost'] as double,
                            ),
                            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              pw.SizedBox(height: 20),

              // معلومات الشركة
              pw.Footer(
                leading: pw.Text(
                  'تقرير التكاليف - نظام إدارة المصنع',
                  style: pw.TextStyle(font: ttf, fontSize: 10),
                ),
                trailing: pw.Text(
                  'تاريخ الطباعة: ${FormatUtils.formatDate(DateTime.now())}',
                  style: pw.TextStyle(font: ttf, fontSize: 10),
                ),
              ),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }
}
