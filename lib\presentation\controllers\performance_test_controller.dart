import 'dart:io';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/utils/logger.dart';
import 'package:mostafa_final/utils/performance_monitor.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

/// وحدة تحكم اختبارات الأداء
class PerformanceTestController extends GetxController {
  final PerformanceMonitor _performanceMonitor = PerformanceMonitor();
  final InventoryRepository _inventoryRepository =
      Get.find<InventoryRepository>();
  final ProductionRepository _productionRepository =
      Get.find<ProductionRepository>();
  final SalesRepository _salesRepository = Get.find<SalesRepository>();

  // متغيرات تفاعلية
  final RxBool isRunningTests = false.obs;
  final RxBool isMonitoring = false.obs;
  final RxString testStatus = ''.obs;
  final RxString errorMessage = ''.obs;
  final RxString reportPath = ''.obs;

  final RxMap<String, dynamic> databaseTestResults = <String, dynamic>{}.obs;
  final RxMap<String, dynamic> uiTestResults = <String, dynamic>{}.obs;
  final RxInt memoryUsage = 0.obs;

  // تم حذف onInit() لأنها غير ضرورية

  @override
  void onClose() {
    stopMonitoring();
    super.onClose();
  }

  /// بدء مراقبة الأداء
  void startMonitoring() {
    try {
      _performanceMonitor.startMonitoring();
      isMonitoring.value = true;
      testStatus.value = 'تم بدء مراقبة الأداء';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء بدء مراقبة الأداء: $e';
      AppLogger.e('حدث خطأ أثناء بدء مراقبة الأداء', e);
    }
  }

  /// إيقاف مراقبة الأداء
  void stopMonitoring() {
    try {
      _performanceMonitor.stopMonitoring();
      isMonitoring.value = false;
      testStatus.value = 'تم إيقاف مراقبة الأداء';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إيقاف مراقبة الأداء: $e';
      AppLogger.e('حدث خطأ أثناء إيقاف مراقبة الأداء', e);
    }
  }

  /// تشغيل اختبارات أداء قاعدة البيانات
  Future<void> runDatabaseTests() async {
    isRunningTests.value = true;
    testStatus.value = 'جاري تشغيل اختبارات أداء قاعدة البيانات...';
    errorMessage.value = '';

    try {
      // اختبار استعلامات المخزون
      final startDate = DateTime.now().subtract(const Duration(days: 30));
      final endDate = DateTime.now();

      final inventoryQueryTime = await _performanceMonitor.measureDatabaseQuery(
        () => _inventoryRepository.getRawMaterialConsumptionReport(
          startDate,
          endDate,
        ),
        'raw_material_consumption_report',
      );

      // اختبار استعلامات الإنتاج
      final productionQueryTime = await _performanceMonitor
          .measureDatabaseQuery(
            () => _productionRepository.getProductionByDate(DateTime.now()),
            'production_by_date',
          );

      // اختبار استعلامات المبيعات
      final salesQueryTime = await _performanceMonitor.measureDatabaseQuery(
        () => _salesRepository.getTopSellingProducts(10),
        'top_selling_products',
      );

      // اختبار أداء قاعدة البيانات تحت ضغط عالٍ
      final stressTestResults =
          await _performanceMonitor.testDatabasePerformance();

      // تحديث النتائج
      databaseTestResults.value = {
        'inventory_query': inventoryQueryTime,
        'production_query': productionQueryTime,
        'sales_query': salesQueryTime,
        'stress_test': stressTestResults,
      };

      testStatus.value = 'تم الانتهاء من اختبارات أداء قاعدة البيانات';
    } catch (e) {
      errorMessage.value =
          'حدث خطأ أثناء تشغيل اختبارات أداء قاعدة البيانات: $e';
      AppLogger.e('حدث خطأ أثناء تشغيل اختبارات أداء قاعدة البيانات', e);
    } finally {
      isRunningTests.value = false;
    }
  }

  /// قياس زمن استجابة واجهة المستخدم
  Future<void> measureUIResponseTime(String screenName) async {
    isRunningTests.value = true;
    testStatus.value = 'جاري قياس زمن استجابة واجهة المستخدم لـ $screenName...';
    errorMessage.value = '';

    try {
      final responseTime = await _performanceMonitor.measureUIResponseTime(
        () async {
          // انتظار لمحاكاة تحميل الشاشة
          await Future.delayed(const Duration(milliseconds: 100));
        },
        screenName,
      );

      // تحديث النتائج
      uiTestResults[screenName] = responseTime;
      uiTestResults.refresh();

      testStatus.value = 'تم قياس زمن استجابة واجهة المستخدم لـ $screenName';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء قياس زمن استجابة واجهة المستخدم: $e';
      AppLogger.e('حدث خطأ أثناء قياس زمن استجابة واجهة المستخدم', e);
    } finally {
      isRunningTests.value = false;
    }
  }

  /// قياس استهلاك الذاكرة
  Future<void> measureMemory() async {
    isRunningTests.value = true;
    testStatus.value = 'جاري قياس استهلاك الذاكرة...';
    errorMessage.value = '';

    try {
      final usage = await _performanceMonitor.measureMemoryUsage();
      memoryUsage.value = usage;

      testStatus.value = 'تم قياس استهلاك الذاكرة';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء قياس استهلاك الذاكرة: $e';
      AppLogger.e('حدث خطأ أثناء قياس استهلاك الذاكرة', e);
    } finally {
      isRunningTests.value = false;
    }
  }

  /// إنشاء تقرير أداء مفصل
  Future<void> generatePerformanceReport() async {
    isRunningTests.value = true;
    testStatus.value = 'جاري إنشاء تقرير أداء مفصل...';
    errorMessage.value = '';

    try {
      final path = await _performanceMonitor.generatePerformanceReport();
      reportPath.value = path;

      testStatus.value = 'تم إنشاء تقرير أداء مفصل';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء تقرير أداء مفصل: $e';
      AppLogger.e('حدث خطأ أثناء إنشاء تقرير أداء مفصل', e);
    } finally {
      isRunningTests.value = false;
    }
  }

  /// مشاركة تقرير الأداء
  Future<void> sharePerformanceReport() async {
    if (reportPath.value.isEmpty) {
      errorMessage.value = 'لم يتم إنشاء تقرير أداء بعد';
      return;
    }

    try {
      final file = File(reportPath.value);
      if (await file.exists()) {
        await SharePlus.instance.share(
          ShareParams(
            files: [XFile(reportPath.value)],
            text: 'تقرير أداء التطبيق',
          ),
        );
      } else {
        errorMessage.value = 'ملف التقرير غير موجود';
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء مشاركة تقرير الأداء: $e';
      AppLogger.e('حدث خطأ أثناء مشاركة تقرير الأداء', e);
    }
  }

  /// حفظ تقرير الأداء
  Future<void> savePerformanceReport() async {
    if (reportPath.value.isEmpty) {
      await generatePerformanceReport();
    }

    try {
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        errorMessage.value = 'لا يمكن الوصول إلى مجلد التخزين الخارجي';
        return;
      }

      final file = File(reportPath.value);
      if (await file.exists()) {
        final dateStr = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
        final newPath = '${directory.path}/performance_report_$dateStr.md';

        await file.copy(newPath);

        testStatus.value = 'تم حفظ تقرير الأداء في: $newPath';
      } else {
        errorMessage.value = 'ملف التقرير غير موجود';
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حفظ تقرير الأداء: $e';
      AppLogger.e('حدث خطأ أثناء حفظ تقرير الأداء', e);
    }
  }

  /// إعادة تعيين بيانات المراقبة
  void resetMonitoringData() {
    try {
      _performanceMonitor.resetMonitoringData();
      databaseTestResults.clear();
      uiTestResults.clear();
      memoryUsage.value = 0;
      reportPath.value = '';

      testStatus.value = 'تم إعادة تعيين بيانات المراقبة';
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إعادة تعيين بيانات المراقبة: $e';
      AppLogger.e('حدث خطأ أثناء إعادة تعيين بيانات المراقبة', e);
    }
  }

  /// تنسيق زمن الاستجابة
  String formatResponseTime(double time) {
    return '${time.toStringAsFixed(2)} ms';
  }

  /// تنسيق استهلاك الذاكرة
  String formatMemoryUsage(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }
}
