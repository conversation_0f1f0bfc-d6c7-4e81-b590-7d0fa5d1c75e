/// نموذج بيانات الجدولة الذكية للإنتاج
class SmartSchedule {
  final String? id;
  final DateTime scheduleDate;
  final List<ScheduledTask> tasks;
  final double totalCapacityUtilization; // نسبة استغلال الطاقة الإنتاجية
  final DateTime createdAt;
  final ScheduleStatus status;

  SmartSchedule({
    this.id,
    required this.scheduleDate,
    required this.tasks,
    required this.totalCapacityUtilization,
    required this.createdAt,
    required this.status,
  });

  // حساب إجمالي الإنتاج المخطط
  int get totalPlannedProduction {
    return tasks.fold(0, (sum, task) => sum + task.plannedQuantity);
  }

  // حساب عدد الماكينات المستخدمة
  int get machinesUsed {
    return tasks.map((task) => task.machineId).toSet().length;
  }

  // حساب متوسط وقت التشغيل
  double get averageRunTime {
    if (tasks.isEmpty) return 0;
    return tasks.fold(0.0, (sum, task) => sum + task.estimatedRunTime) /
        tasks.length;
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'scheduleDate': scheduleDate.toIso8601String(),
      'totalCapacityUtilization': totalCapacityUtilization,
      'createdAt': createdAt.toIso8601String(),
      'status': status.index,
    };
  }

  // إنشاء من Map
  factory SmartSchedule.fromMap(Map<String, dynamic> map) {
    return SmartSchedule(
      id: map['id'],
      scheduleDate: DateTime.parse(map['scheduleDate']),
      tasks: [], // سيتم تحميل المهام منفصلة
      totalCapacityUtilization: map['totalCapacityUtilization'],
      createdAt: DateTime.parse(map['createdAt']),
      status: ScheduleStatus.values[map['status']],
    );
  }

  // إنشاء نسخة معدلة
  SmartSchedule copyWith({
    String? id,
    DateTime? scheduleDate,
    List<ScheduledTask>? tasks,
    double? totalCapacityUtilization,
    DateTime? createdAt,
    ScheduleStatus? status,
  }) {
    return SmartSchedule(
      id: id ?? this.id,
      scheduleDate: scheduleDate ?? this.scheduleDate,
      tasks: tasks ?? this.tasks,
      totalCapacityUtilization:
          totalCapacityUtilization ?? this.totalCapacityUtilization,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
    );
  }
}

/// مهمة مجدولة في الإنتاج
class ScheduledTask {
  final String? id;
  final String scheduleId;
  final String machineId;
  final String moldId;
  final String productCode;
  final int plannedQuantity;
  final double estimatedRunTime; // بالساعات
  final double setupTime; // بالدقائق
  final DateTime plannedStartTime;
  final DateTime plannedEndTime;
  final int priority;
  final TaskStatus status;
  final String? notes;

  ScheduledTask({
    this.id,
    required this.scheduleId,
    required this.machineId,
    required this.moldId,
    required this.productCode,
    required this.plannedQuantity,
    required this.estimatedRunTime,
    required this.setupTime,
    required this.plannedStartTime,
    required this.plannedEndTime,
    required this.priority,
    required this.status,
    this.notes,
  });

  // حساب إجمالي الوقت المطلوب (إعداد + تشغيل)
  double get totalTimeRequired {
    return (setupTime / 60) + estimatedRunTime; // تحويل الإعداد إلى ساعات
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'scheduleId': scheduleId,
      'machineId': machineId,
      'moldId': moldId,
      'productCode': productCode,
      'plannedQuantity': plannedQuantity,
      'estimatedRunTime': estimatedRunTime,
      'setupTime': setupTime,
      'plannedStartTime': plannedStartTime.toIso8601String(),
      'plannedEndTime': plannedEndTime.toIso8601String(),
      'priority': priority,
      'status': status.index,
      'notes': notes,
    };
  }

  // إنشاء من Map
  factory ScheduledTask.fromMap(Map<String, dynamic> map) {
    return ScheduledTask(
      id: map['id'],
      scheduleId: map['scheduleId'],
      machineId: map['machineId'],
      moldId: map['moldId'],
      productCode: map['productCode'],
      plannedQuantity: map['plannedQuantity'],
      estimatedRunTime: map['estimatedRunTime'],
      setupTime: map['setupTime'],
      plannedStartTime: DateTime.parse(map['plannedStartTime']),
      plannedEndTime: DateTime.parse(map['plannedEndTime']),
      priority: map['priority'],
      status: TaskStatus.values[map['status']],
      notes: map['notes'],
    );
  }
}

/// تنبيه التخطيط
class ScheduleAlert {
  final String? id;
  final String scheduleId;
  final AlertType type;
  final String message;
  final AlertSeverity severity;
  final DateTime createdAt;
  final bool isRead;
  final Map<String, dynamic>? metadata;

  ScheduleAlert({
    this.id,
    required this.scheduleId,
    required this.type,
    required this.message,
    required this.severity,
    required this.createdAt,
    this.isRead = false,
    this.metadata,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'scheduleId': scheduleId,
      'type': type.index,
      'message': message,
      'severity': severity.index,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead ? 1 : 0,
      'metadata': metadata?.toString(),
    };
  }

  // إنشاء من Map
  factory ScheduleAlert.fromMap(Map<String, dynamic> map) {
    return ScheduleAlert(
      id: map['id'],
      scheduleId: map['scheduleId'],
      type: AlertType.values[map['type']],
      message: map['message'],
      severity: AlertSeverity.values[map['severity']],
      createdAt: DateTime.parse(map['createdAt']),
      isRead: map['isRead'] == 1,
      metadata: map['metadata'] != null ? {} : null, // سيتم تحسينه لاحقاً
    );
  }

  // إنشاء نسخة معدلة
  ScheduleAlert copyWith({
    String? id,
    String? scheduleId,
    AlertType? type,
    String? message,
    AlertSeverity? severity,
    DateTime? createdAt,
    bool? isRead,
    Map<String, dynamic>? metadata,
  }) {
    return ScheduleAlert(
      id: id ?? this.id,
      scheduleId: scheduleId ?? this.scheduleId,
      type: type ?? this.type,
      message: message ?? this.message,
      severity: severity ?? this.severity,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// حالات الجدولة
enum ScheduleStatus {
  draft, // مسودة
  active, // نشط
  completed, // مكتمل
  cancelled, // ملغي
}

/// حالات المهام
enum TaskStatus {
  planned, // مخطط
  inProgress, // قيد التنفيذ
  completed, // مكتمل
  delayed, // متأخر
  cancelled, // ملغي
}

/// أنواع التنبيهات
enum AlertType {
  capacityOverload, // تجاوز الطاقة الإنتاجية
  materialShortage, // نقص المواد الخام
  machineUnavailable, // ماكينة غير متاحة
  scheduleDelay, // تأخير في الجدولة
  priorityConflict, // تعارض في الأولويات
  maintenanceDue, // صيانة مستحقة
}

/// مستويات خطورة التنبيهات
enum AlertSeverity {
  low, // منخفض
  medium, // متوسط
  high, // عالي
  critical, // حرج
}

/// إضافات مساعدة للتعامل مع الحالات
extension ScheduleStatusExtension on ScheduleStatus {
  String get displayName {
    switch (this) {
      case ScheduleStatus.draft:
        return 'مسودة';
      case ScheduleStatus.active:
        return 'نشط';
      case ScheduleStatus.completed:
        return 'مكتمل';
      case ScheduleStatus.cancelled:
        return 'ملغي';
    }
  }
}

extension TaskStatusExtension on TaskStatus {
  String get displayName {
    switch (this) {
      case TaskStatus.planned:
        return 'مخطط';
      case TaskStatus.inProgress:
        return 'قيد التنفيذ';
      case TaskStatus.completed:
        return 'مكتمل';
      case TaskStatus.delayed:
        return 'متأخر';
      case TaskStatus.cancelled:
        return 'ملغي';
    }
  }
}

extension AlertTypeExtension on AlertType {
  String get displayName {
    switch (this) {
      case AlertType.capacityOverload:
        return 'تجاوز الطاقة الإنتاجية';
      case AlertType.materialShortage:
        return 'نقص المواد الخام';
      case AlertType.machineUnavailable:
        return 'ماكينة غير متاحة';
      case AlertType.scheduleDelay:
        return 'تأخير في الجدولة';
      case AlertType.priorityConflict:
        return 'تعارض في الأولويات';
      case AlertType.maintenanceDue:
        return 'صيانة مستحقة';
    }
  }
}

extension AlertSeverityExtension on AlertSeverity {
  String get displayName {
    switch (this) {
      case AlertSeverity.low:
        return 'منخفض';
      case AlertSeverity.medium:
        return 'متوسط';
      case AlertSeverity.high:
        return 'عالي';
      case AlertSeverity.critical:
        return 'حرج';
    }
  }
}
