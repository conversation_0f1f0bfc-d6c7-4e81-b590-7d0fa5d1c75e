import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';

/// مساعد للتعامل مع النصوص الطويلة
class TextUtils {
  /// إنشاء نص متجاوب مع حجم الشاشة
  static Widget responsiveText(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: ScreenSize.getFontSize(style?.fontSize ?? 14),
        overflow: overflow,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      softWrap: softWrap,
      overflow: overflow,
    );
  }

  /// إنشاء نص عنوان متجاوب مع حجم الشاشة
  static Widget responsiveTitle(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: ScreenSize.getFontSize(style?.fontSize ?? 18),
        fontWeight: FontWeight.bold,
        overflow: overflow,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      softWrap: softWrap,
      overflow: overflow,
    );
  }

  /// إنشاء نص وصفي متجاوب مع حجم الشاشة
  static Widget responsiveSubtitle(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: ScreenSize.getFontSize(style?.fontSize ?? 14),
        color: Colors.grey.shade700,
        overflow: overflow,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      softWrap: softWrap,
      overflow: overflow,
    );
  }

  /// إنشاء نص ملاحظة متجاوب مع حجم الشاشة
  static Widget responsiveCaption(
    String text, {
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    bool softWrap = true,
  }) {
    return Text(
      text,
      style: (style ?? const TextStyle()).copyWith(
        fontSize: ScreenSize.getFontSize(style?.fontSize ?? 12),
        color: Colors.grey.shade500,
        overflow: overflow,
      ),
      textAlign: textAlign ?? TextAlign.start,
      maxLines: maxLines,
      softWrap: softWrap,
      overflow: overflow,
    );
  }
}

/// امتداد للتعامل مع النصوص الطويلة
extension TextExtension on Text {
  /// تحويل النص إلى نص متجاوب مع حجم الشاشة
  Widget responsive({
    TextStyle? style,
    TextAlign? textAlign,
    int? maxLines,
    TextOverflow overflow = TextOverflow.ellipsis,
    bool softWrap = true,
  }) {
    return TextUtils.responsiveText(
      data ?? '',
      style: this.style?.merge(style) ?? style,
      textAlign: textAlign ?? this.textAlign,
      maxLines: maxLines ?? this.maxLines,
      overflow: overflow,
      softWrap: softWrap,
    );
  }
}
