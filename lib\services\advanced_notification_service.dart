import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/predictive_analytics.dart';
import 'package:mostafa_final/services/predictive_analytics_service.dart';
import 'package:mostafa_final/services/notification_service.dart';
import 'package:uuid/uuid.dart';

/// خدمة التنبيهات المتقدمة والمخصصة
class AdvancedNotificationService extends GetxService {
  static AdvancedNotificationService get instance =>
      Get.find<AdvancedNotificationService>();

  final PredictiveAnalyticsService _analyticsService =
      PredictiveAnalyticsService();
  final NotificationService _notificationService = NotificationService.instance;
  final _uuid = const Uuid();

  // التنبيهات التنبؤية النشطة
  final predictiveNotifications = <PredictiveNotification>[].obs;

  // إعدادات التنبيهات المخصصة
  final customNotificationSettings = <String, CustomNotificationRule>{}.obs;

  // جدولة التنبيهات
  final scheduledNotifications = <ScheduledNotification>[].obs;

  // حالة الخدمة
  final isServiceEnabled = true.obs;
  final isPredictiveAnalysisRunning = false.obs;

  // مؤقتات
  Timer? _predictiveAnalysisTimer;
  Timer? _scheduledNotificationTimer;

  @override
  void onInit() {
    super.onInit();
    _initializeService();
  }

  @override
  void onClose() {
    _predictiveAnalysisTimer?.cancel();
    _scheduledNotificationTimer?.cancel();
    super.onClose();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    await _loadCustomNotificationSettings();
    _startPredictiveAnalysis();
    _startScheduledNotificationCheck();
  }

  /// بدء التحليل التنبؤي الدوري
  void _startPredictiveAnalysis() {
    // تشغيل التحليل كل ساعة
    _predictiveAnalysisTimer = Timer.periodic(const Duration(hours: 1), (_) {
      if (isServiceEnabled.value) {
        runPredictiveAnalysis();
      }
    });

    // تشغيل فوري
    runPredictiveAnalysis();
  }

  /// بدء فحص التنبيهات المجدولة
  void _startScheduledNotificationCheck() {
    // فحص كل 5 دقائق
    _scheduledNotificationTimer = Timer.periodic(const Duration(minutes: 5), (
      _,
    ) {
      if (isServiceEnabled.value) {
        _checkScheduledNotifications();
      }
    });
  }

  /// تشغيل التحليل التنبؤي
  Future<void> runPredictiveAnalysis() async {
    if (isPredictiveAnalysisRunning.value) return;

    try {
      isPredictiveAnalysisRunning.value = true;

      // التنبؤ بالأعطال
      await _runFailurePredictionAnalysis();

      // تحليل الاتجاهات
      await _runTrendAnalysis();

      // تحليل الارتباطات
      await _runCorrelationAnalysis();

      // إنشاء التوقعات المستقبلية
      await _runForecastAnalysis();
    } catch (e) {
      debugPrint('خطأ في التحليل التنبؤي: $e');
    } finally {
      isPredictiveAnalysisRunning.value = false;
    }
  }

  /// تحليل التنبؤ بالأعطال
  Future<void> _runFailurePredictionAnalysis() async {
    try {
      final predictions = await _analyticsService.predictMachineFailures(
        daysAhead: 30,
      );

      for (var prediction in predictions) {
        // إنشاء تنبيه تنبؤي للأعطال عالية الاحتمالية
        if (prediction.failureProbability > 70) {
          await _createPredictiveNotification(
            type: PredictiveNotificationType.failure_prediction,
            title: 'تحذير: احتمالية عطل عالية',
            message:
                'الماكينة ${prediction.machineName} لديها احتمالية عطل ${prediction.failureProbability.toStringAsFixed(1)}% خلال ${prediction.daysUntilFailure} أيام',
            severity: NotificationSeverity.critical,
            relatedData: prediction.toMap(),
            actionRequired: prediction.recommendedAction,
          );
        } else if (prediction.failureProbability > 50) {
          await _createPredictiveNotification(
            type: PredictiveNotificationType.failure_prediction,
            title: 'تنبيه: احتمالية عطل متوسطة',
            message:
                'الماكينة ${prediction.machineName} تحتاج مراقبة - احتمالية عطل ${prediction.failureProbability.toStringAsFixed(1)}%',
            severity: NotificationSeverity.warning,
            relatedData: prediction.toMap(),
            actionRequired: prediction.recommendedAction,
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل التنبؤ بالأعطال: $e');
    }
  }

  /// تحليل الاتجاهات
  Future<void> _runTrendAnalysis() async {
    try {
      final trends = await _analyticsService.analyzeTrends(analysisWindow: 30);

      for (var trend in trends) {
        // تنبيهات للاتجاهات السلبية القوية
        if (trend.direction == TrendDirection.decreasing &&
            trend.trendStrength > 70) {
          await _createPredictiveNotification(
            type: PredictiveNotificationType.trend_alert,
            title: 'تحذير: اتجاه سلبي قوي',
            message:
                '${trend.metricName} يظهر اتجاهاً متناقصاً بقوة ${trend.trendStrength.toStringAsFixed(1)}%',
            severity: NotificationSeverity.warning,
            relatedData: trend.toMap(),
            actionRequired: 'مراجعة أسباب الانخفاض واتخاذ إجراءات تصحيحية',
          );
        }

        // تنبيهات للتقلبات العالية
        if (trend.direction == TrendDirection.volatile &&
            trend.trendStrength > 80) {
          await _createPredictiveNotification(
            type: PredictiveNotificationType.trend_alert,
            title: 'تنبيه: تقلبات عالية',
            message:
                '${trend.metricName} يظهر تقلبات عالية قد تؤثر على الاستقرار',
            severity: NotificationSeverity.info,
            relatedData: trend.toMap(),
            actionRequired: 'تحليل أسباب التقلبات وتحسين الاستقرار',
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل الاتجاهات: $e');
    }
  }

  /// تحليل الارتباطات
  Future<void> _runCorrelationAnalysis() async {
    try {
      final correlations = await _analyticsService.analyzeCorrelations();

      for (var correlation in correlations) {
        // تنبيهات للارتباطات القوية والمعنوية
        if (correlation.isSignificant &&
            correlation.strength == CorrelationStrength.very_strong) {
          await _createPredictiveNotification(
            type: PredictiveNotificationType.correlation_insight,
            title: 'اكتشاف: ارتباط قوي',
            message:
                'تم اكتشاف ارتباط ${correlation.type.displayName} قوي (${(correlation.correlationCoefficient * 100).toStringAsFixed(1)}%) بين ${correlation.variable1} و ${correlation.variable2}',
            severity: NotificationSeverity.info,
            relatedData: correlation.toMap(),
            actionRequired: 'استغلال هذا الارتباط في تحسين الأداء',
          );
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل الارتباطات: $e');
    }
  }

  /// تحليل التوقعات المستقبلية
  Future<void> _runForecastAnalysis() async {
    try {
      final forecasts = await _analyticsService.generateFutureForecasts(
        forecastDays: 30,
      );

      for (var forecast in forecasts) {
        // تنبيهات للتوقعات المقلقة
        if (forecast.type == ForecastType.production) {
          final growthRate = forecast.predictions['معدل النمو اليومي %'] ?? 0;

          if (growthRate < -10) {
            await _createPredictiveNotification(
              type: PredictiveNotificationType.forecast_alert,
              title: 'تحذير: انخفاض متوقع في الإنتاج',
              message:
                  'التوقعات تشير إلى انخفاض ${growthRate.abs().toStringAsFixed(1)}% في الإنتاج خلال الشهر القادم',
              severity: NotificationSeverity.warning,
              relatedData: forecast.toMap(),
              actionRequired: 'مراجعة خطط الإنتاج واتخاذ إجراءات وقائية',
            );
          }
        }

        if (forecast.type == ForecastType.maintenance) {
          final expectedBreakdowns =
              forecast.predictions['عدد الأعطال المتوقعة'] ?? 0;
          final maintenanceCost =
              forecast.predictions['تكلفة الصيانة المتوقعة'] ?? 0;

          if (expectedBreakdowns > 15) {
            await _createPredictiveNotification(
              type: PredictiveNotificationType.forecast_alert,
              title: 'تحذير: زيادة متوقعة في الأعطال',
              message:
                  'التوقعات تشير إلى ${expectedBreakdowns.toStringAsFixed(0)} عطل خلال الشهر القادم بتكلفة ${maintenanceCost.toStringAsFixed(0)} ريال',
              severity: NotificationSeverity.critical,
              relatedData: forecast.toMap(),
              actionRequired: 'تكثيف برنامج الصيانة الوقائية',
            );
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ في تحليل التوقعات: $e');
    }
  }

  /// إنشاء تنبيه تنبؤي
  Future<void> _createPredictiveNotification({
    required PredictiveNotificationType type,
    required String title,
    required String message,
    required NotificationSeverity severity,
    required Map<String, dynamic> relatedData,
    required String actionRequired,
  }) async {
    // تجنب التنبيهات المكررة
    final existingNotification = predictiveNotifications.firstWhereOrNull(
      (n) =>
          n.type == type &&
          n.title == title &&
          DateTime.now().difference(n.createdAt).inHours < 24,
    );

    if (existingNotification != null) return;

    final notification = PredictiveNotification(
      id: _uuid.v4(),
      type: type,
      title: title,
      message: message,
      severity: severity,
      relatedData: relatedData,
      actionRequired: actionRequired,
      createdAt: DateTime.now(),
      isRead: false,
      isActionTaken: false,
    );

    predictiveNotifications.add(notification);

    // إرسال التنبيه للمستخدم
    await _sendNotificationToUser(notification);

    // الحفاظ على حد أقصى 100 تنبيه
    if (predictiveNotifications.length > 100) {
      predictiveNotifications.removeRange(
        0,
        predictiveNotifications.length - 100,
      );
    }
  }

  /// إرسال التنبيه للمستخدم
  Future<void> _sendNotificationToUser(
    PredictiveNotification notification,
  ) async {
    // تحويل إلى تنبيه عادي
    final smartNotification = SmartNotification(
      id: notification.id,
      title: notification.title,
      message: notification.message,
      level: _mapSeverityToLevel(notification.severity),
      kpiId: 'predictive_${notification.type.name}',
      kpiName: notification.type.displayName,
      currentValue: 0.0,
      threshold: NotificationThreshold(
        kpiId: 'predictive',
        minValue: 0.0,
        maxValue: 100.0,
      ),
      createdAt: notification.createdAt,
    );

    _notificationService.activeNotifications.add(smartNotification);
    // عرض التنبيه للمستخدم (سيتم تطبيقه لاحقاً)
    // _notificationService._showNotificationToUser(smartNotification);
  }

  /// تحويل شدة التنبيه إلى مستوى
  NotificationLevel _mapSeverityToLevel(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return NotificationLevel.critical;
      case NotificationSeverity.warning:
        return NotificationLevel.warning;
      case NotificationSeverity.info:
        return NotificationLevel.info;
      case NotificationSeverity.success:
        return NotificationLevel.success;
    }
  }

  /// فحص التنبيهات المجدولة
  Future<void> _checkScheduledNotifications() async {
    final now = DateTime.now();

    for (var scheduled in scheduledNotifications.toList()) {
      if (scheduled.isActive && scheduled.nextExecutionTime.isBefore(now)) {
        await _executeScheduledNotification(scheduled);
      }
    }
  }

  /// تنفيذ تنبيه مجدول
  Future<void> _executeScheduledNotification(
    ScheduledNotification scheduled,
  ) async {
    try {
      // تنفيذ التنبيه حسب النوع
      switch (scheduled.type) {
        case ScheduledNotificationType.daily_summary:
          await _generateDailySummaryNotification();
          break;
        case ScheduledNotificationType.weekly_report:
          await _generateWeeklyReportNotification();
          break;
        case ScheduledNotificationType.maintenance_reminder:
          await _generateMaintenanceReminderNotification();
          break;
        case ScheduledNotificationType.performance_review:
          await _generatePerformanceReviewNotification();
          break;
      }

      // تحديث وقت التنفيذ التالي
      scheduled.updateNextExecution();
    } catch (e) {
      debugPrint('خطأ في تنفيذ التنبيه المجدول: $e');
    }
  }

  /// إنشاء تنبيه ملخص يومي
  Future<void> _generateDailySummaryNotification() async {
    await _createPredictiveNotification(
      type: PredictiveNotificationType.daily_summary,
      title: 'ملخص الأداء اليومي',
      message: 'تم إنشاء ملخص شامل لأداء اليوم مع التوصيات',
      severity: NotificationSeverity.info,
      relatedData: {'date': DateTime.now().toIso8601String()},
      actionRequired: 'مراجعة الملخص اليومي',
    );
  }

  /// إنشاء تنبيه تقرير أسبوعي
  Future<void> _generateWeeklyReportNotification() async {
    await _createPredictiveNotification(
      type: PredictiveNotificationType.weekly_report,
      title: 'التقرير الأسبوعي جاهز',
      message: 'تم إنشاء التقرير الأسبوعي مع تحليل الاتجاهات والتوقعات',
      severity: NotificationSeverity.info,
      relatedData: {'week': DateTime.now().toIso8601String()},
      actionRequired: 'مراجعة التقرير الأسبوعي',
    );
  }

  /// إنشاء تذكير صيانة
  Future<void> _generateMaintenanceReminderNotification() async {
    await _createPredictiveNotification(
      type: PredictiveNotificationType.maintenance_reminder,
      title: 'تذكير: صيانة مجدولة',
      message: 'هناك مهام صيانة مجدولة تحتاج للتنفيذ',
      severity: NotificationSeverity.warning,
      relatedData: {'reminder_date': DateTime.now().toIso8601String()},
      actionRequired: 'مراجعة جدول الصيانة',
    );
  }

  /// إنشاء تنبيه مراجعة الأداء
  Future<void> _generatePerformanceReviewNotification() async {
    await _createPredictiveNotification(
      type: PredictiveNotificationType.performance_review,
      title: 'مراجعة الأداء الدورية',
      message: 'حان وقت مراجعة مؤشرات الأداء وتحديث الأهداف',
      severity: NotificationSeverity.info,
      relatedData: {'review_date': DateTime.now().toIso8601String()},
      actionRequired: 'إجراء مراجعة شاملة للأداء',
    );
  }

  /// تحميل إعدادات التنبيهات المخصصة
  Future<void> _loadCustomNotificationSettings() async {
    // هنا يمكن تحميل الإعدادات من قاعدة البيانات
    // مؤقتاً سنستخدم إعدادات افتراضية
    _setDefaultCustomNotificationSettings();
  }

  /// تعيين إعدادات افتراضية للتنبيهات المخصصة
  void _setDefaultCustomNotificationSettings() {
    customNotificationSettings.value = {
      'failure_prediction': CustomNotificationRule(
        id: 'failure_prediction',
        name: 'تنبيهات التنبؤ بالأعطال',
        isEnabled: true,
        conditions: [
          NotificationCondition(
            parameter: 'failureProbability',
            operator: ConditionOperator.greater_than,
            value: 70.0,
          ),
        ],
        actions: [
          NotificationAction.send_notification,
          NotificationAction.send_email,
        ],
        schedule: NotificationSchedule.immediate,
      ),
      'trend_analysis': CustomNotificationRule(
        id: 'trend_analysis',
        name: 'تنبيهات تحليل الاتجاهات',
        isEnabled: true,
        conditions: [
          NotificationCondition(
            parameter: 'trendStrength',
            operator: ConditionOperator.greater_than,
            value: 70.0,
          ),
        ],
        actions: [NotificationAction.send_notification],
        schedule: NotificationSchedule.immediate,
      ),
    };
  }

  /// إضافة تنبيه مجدول
  void addScheduledNotification(ScheduledNotification notification) {
    scheduledNotifications.add(notification);
  }

  /// حذف تنبيه مجدول
  void removeScheduledNotification(String id) {
    scheduledNotifications.removeWhere((n) => n.id == id);
  }

  /// تمكين/تعطيل الخدمة
  void toggleService(bool enabled) {
    isServiceEnabled.value = enabled;
  }

  /// وضع علامة مقروء على التنبيه التنبؤي
  void markPredictiveNotificationAsRead(String id) {
    final index = predictiveNotifications.indexWhere((n) => n.id == id);
    if (index != -1) {
      predictiveNotifications[index] = predictiveNotifications[index].copyWith(
        isRead: true,
      );
    }
  }

  /// وضع علامة إجراء مُتخذ
  void markActionTaken(String id) {
    final index = predictiveNotifications.indexWhere((n) => n.id == id);
    if (index != -1) {
      predictiveNotifications[index] = predictiveNotifications[index].copyWith(
        isActionTaken: true,
      );
    }
  }

  /// الحصول على عدد التنبيهات التنبؤية غير المقروءة
  int get unreadPredictiveCount =>
      predictiveNotifications.where((n) => !n.isRead).length;

  /// الحصول على التنبيهات حسب النوع
  List<PredictiveNotification> getNotificationsByType(
    PredictiveNotificationType type,
  ) {
    return predictiveNotifications.where((n) => n.type == type).toList();
  }

  /// الحصول على التنبيهات حسب الشدة
  List<PredictiveNotification> getNotificationsBySeverity(
    NotificationSeverity severity,
  ) {
    return predictiveNotifications
        .where((n) => n.severity == severity)
        .toList();
  }
}
