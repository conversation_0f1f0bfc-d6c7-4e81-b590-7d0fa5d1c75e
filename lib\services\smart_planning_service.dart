import 'package:mostafa_final/data/models/machine_capacity.dart';
import 'package:mostafa_final/data/models/smart_schedule.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/repositories/smart_planning_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';

/// خدمة التخطيط الذكي للإنتاج
class SmartPlanningService {
  final SmartPlanningRepository _planningRepository;
  final MachineRepository _machineRepository;
  final MoldRepository _moldRepository;
  final InventoryRepository _inventoryRepository;

  SmartPlanningService({
    required SmartPlanningRepository planningRepository,
    required MachineRepository machineRepository,
    required MoldRepository moldRepository,
    required InventoryRepository inventoryRepository,
  }) : _planningRepository = planningRepository,
       _machineRepository = machineRepository,
       _moldRepository = moldRepository,
       _inventoryRepository = inventoryRepository;

  /// إنشاء جدولة ذكية للإنتاج
  Future<SmartSchedule> generateSmartSchedule(
    DateTime targetDate, {
    double shiftsCount = 1.0,
  }) async {
    // 1. الحصول على الطلبيات المعلقة
    final pendingOrders = await _planningRepository.getPendingOrders();

    // 2. الحصول على الماكينات المتاحة
    final availableMachines = await _getAvailableMachines();

    // 3. حساب الطاقة الإنتاجية لكل ماكينة
    final machineCapacities = await _calculateAllMachineCapacities();

    // 4. إنشاء المهام المجدولة
    final scheduledTasks = await _createScheduledTasks(
      pendingOrders,
      availableMachines,
      machineCapacities,
      targetDate,
      shiftsCount,
    );

    // 5. حساب نسبة استغلال الطاقة الإنتاجية
    final capacityUtilization = _calculateCapacityUtilization(
      scheduledTasks,
      machineCapacities,
      shiftsCount,
    );

    // 6. إنشاء التنبيهات
    final alerts = await _generateAlerts(
      scheduledTasks,
      pendingOrders,
      machineCapacities,
    );

    // 7. إنشاء الجدولة
    final schedule = SmartSchedule(
      scheduleDate: targetDate,
      tasks: scheduledTasks,
      totalCapacityUtilization: capacityUtilization,
      createdAt: DateTime.now(),
      status: ScheduleStatus.draft,
    );

    // 8. حفظ الجدولة والتنبيهات
    final scheduleId = await _planningRepository.createSmartSchedule(schedule);

    for (var alert in alerts) {
      await _planningRepository.createAlert(
        alert.copyWith(scheduleId: scheduleId),
      );
    }

    return schedule.copyWith(id: scheduleId);
  }

  /// الحصول على الماكينات المتاحة
  Future<List<Machine>> _getAvailableMachines() async {
    final allMachines = await _machineRepository.getAllMachines();
    return allMachines
        .where(
          (machine) =>
              machine.status == MachineStatus.operating ||
              machine.status == MachineStatus.stopped,
        )
        .toList();
  }

  /// حساب الطاقة الإنتاجية لجميع الماكينات
  Future<Map<String, MachineCapacity>> _calculateAllMachineCapacities() async {
    final capacities = <String, MachineCapacity>{};
    final machines = await _getAvailableMachines();
    final molds = await _moldRepository.getAllMolds();

    for (var machine in machines) {
      for (var mold in molds) {
        // محاولة الحصول على الطاقة المحفوظة أولاً
        var capacity = await _planningRepository.getMachineCapacity(
          machine.id!,
          mold.id!,
        );

        // إذا لم توجد، احسبها من البيانات التاريخية
        capacity ??= await _planningRepository.calculateMachineCapacity(
          machine.id!,
          mold.id!,
        );

        if (capacity != null) {
          capacities['${machine.id}_${mold.id}'] = capacity;
        }
      }
    }

    return capacities;
  }

  /// إنشاء المهام المجدولة
  Future<List<ScheduledTask>> _createScheduledTasks(
    List<PendingOrder> pendingOrders,
    List<Machine> availableMachines,
    Map<String, MachineCapacity> machineCapacities,
    DateTime targetDate,
    double shiftsCount,
  ) async {
    final tasks = <ScheduledTask>[];
    final machineSchedule =
        <String, DateTime>{}; // تتبع آخر وقت انتهاء لكل ماكينة

    // ترتيب الطلبيات حسب الأولوية والتاريخ المستحق
    pendingOrders.sort((a, b) {
      if (a.priority != b.priority) {
        return a.priority.compareTo(b.priority);
      }
      return a.dueDate.compareTo(b.dueDate);
    });

    for (var order in pendingOrders) {
      if (order.isCompleted) continue;

      // البحث عن أفضل ماكينة لهذا المنتج
      final bestMachine = await _findBestMachineForProduct(
        order.productCode,
        availableMachines,
        machineCapacities,
      );

      if (bestMachine == null) continue;

      // الحصول على الإسطمبة المناسبة
      final mold = await _findMoldForProduct(order.productCode);
      if (mold == null) continue;

      // الحصول على الطاقة الإنتاجية
      final capacityKey = '${bestMachine.id}_${mold.id}';
      final capacity = machineCapacities[capacityKey];
      if (capacity == null) continue;

      // حساب الوقت المطلوب للإنتاج
      final dailyCapacity = capacity.dailyCapacityWithShifts(shiftsCount);
      final requiredDays = (order.remainingQuantity / dailyCapacity).ceil();

      // تحديد وقت البداية (بعد آخر مهمة للماكينة)
      final machineLastEnd =
          machineSchedule[bestMachine.id!] ??
          DateTime(
            targetDate.year,
            targetDate.month,
            targetDate.day,
            8,
          ); // بداية الوردية 8 صباحاً

      final startTime = machineLastEnd;
      final endTime = startTime.add(Duration(days: requiredDays));

      // إنشاء المهمة
      final task = ScheduledTask(
        scheduleId: '', // سيتم تحديده لاحقاً
        machineId: bestMachine.id!,
        moldId: mold.id!,
        productCode: order.productCode,
        plannedQuantity: order.remainingQuantity,
        estimatedRunTime: requiredDays * 8 * shiftsCount, // بالساعات
        setupTime: capacity.setupTime,
        plannedStartTime: startTime,
        plannedEndTime: endTime,
        priority: order.priority,
        status: TaskStatus.planned,
        notes: 'تم إنشاؤها تلقائياً بواسطة النظام الذكي',
      );

      tasks.add(task);

      // تحديث آخر وقت انتهاء للماكينة
      machineSchedule[bestMachine.id!] = endTime;
    }

    return tasks;
  }

  /// البحث عن أفضل ماكينة لمنتج معين
  Future<Machine?> _findBestMachineForProduct(
    String productCode,
    List<Machine> availableMachines,
    Map<String, MachineCapacity> machineCapacities,
  ) async {
    Machine? bestMachine;
    double bestEfficiency = 0;

    for (var machine in availableMachines) {
      // البحث عن الطاقة الإنتاجية لهذه الماكينة مع المنتج
      final capacity = machineCapacities.values.firstWhere(
        (c) => c.machineId == machine.id,
        orElse: () => machineCapacities.values.first,
      );

      if (capacity.efficiency > bestEfficiency) {
        bestEfficiency = capacity.efficiency;
        bestMachine = machine;
      }
    }

    return bestMachine;
  }

  /// البحث عن الإسطمبة المناسبة للمنتج
  Future<Mold?> _findMoldForProduct(String productCode) async {
    final molds = await _moldRepository.getAllMolds();
    return molds.firstWhere(
      (mold) => mold.productCode == productCode,
      orElse: () => molds.first, // إسطمبة افتراضية
    );
  }

  /// حساب نسبة استغلال الطاقة الإنتاجية
  double _calculateCapacityUtilization(
    List<ScheduledTask> tasks,
    Map<String, MachineCapacity> machineCapacities,
    double shiftsCount,
  ) {
    if (tasks.isEmpty || machineCapacities.isEmpty) return 0;

    double totalUsedCapacity = 0;
    double totalAvailableCapacity = 0;

    final machineUsage = <String, double>{};

    // حساب الاستخدام لكل ماكينة
    for (var task in tasks) {
      machineUsage[task.machineId] =
          (machineUsage[task.machineId] ?? 0) + task.estimatedRunTime;
    }

    // حساب الطاقة المتاحة والمستخدمة
    for (var capacity in machineCapacities.values) {
      final dailyHours = 8 * shiftsCount;
      totalAvailableCapacity += dailyHours;

      final usedHours = machineUsage[capacity.machineId] ?? 0;
      totalUsedCapacity += usedHours;
    }

    return totalAvailableCapacity > 0
        ? (totalUsedCapacity / totalAvailableCapacity) * 100
        : 0;
  }

  /// إنشاء التنبيهات
  Future<List<ScheduleAlert>> _generateAlerts(
    List<ScheduledTask> tasks,
    List<PendingOrder> pendingOrders,
    Map<String, MachineCapacity> machineCapacities,
  ) async {
    final alerts = <ScheduleAlert>[];

    // تنبيه تجاوز الطاقة الإنتاجية
    final capacityUtilization = _calculateCapacityUtilization(
      tasks,
      machineCapacities,
      1.0,
    );
    if (capacityUtilization > 90) {
      alerts.add(
        ScheduleAlert(
          scheduleId: '',
          type: AlertType.capacityOverload,
          message:
              'تجاوز استغلال الطاقة الإنتاجية ${capacityUtilization.toStringAsFixed(1)}%',
          severity: AlertSeverity.high,
          createdAt: DateTime.now(),
        ),
      );
    }

    // تنبيه الطلبيات المتأخرة
    final overdueOrders =
        pendingOrders.where((order) => order.isOverdue).length;
    if (overdueOrders > 0) {
      alerts.add(
        ScheduleAlert(
          scheduleId: '',
          type: AlertType.scheduleDelay,
          message: 'يوجد $overdueOrders طلبية متأخرة',
          severity: AlertSeverity.medium,
          createdAt: DateTime.now(),
        ),
      );
    }

    // تنبيه نقص المواد الخام (سيتم تطويره لاحقاً)

    return alerts;
  }

  /// تحديث حالة المهمة
  Future<void> updateTaskStatus(String taskId, TaskStatus newStatus) async {
    // سيتم تطوير هذه الطريقة لاحقاً
  }

  /// الحصول على إحصائيات الأداء
  Future<Map<String, dynamic>> getPerformanceStats() async {
    final capacities = await _planningRepository.getAllMachineCapacities();

    if (capacities.isEmpty) {
      return {
        'averageEfficiency': 0.0,
        'totalMachines': 0,
        'highEfficiencyMachines': 0,
        'lowEfficiencyMachines': 0,
      };
    }

    final totalEfficiency = capacities.fold(
      0.0,
      (sum, capacity) => sum + capacity.efficiency,
    );
    final averageEfficiency = totalEfficiency / capacities.length;

    final highEfficiencyMachines =
        capacities.where((c) => c.efficiency >= 80).length;
    final lowEfficiencyMachines =
        capacities.where((c) => c.efficiency < 60).length;

    return {
      'averageEfficiency': averageEfficiency,
      'totalMachines': capacities.length,
      'highEfficiencyMachines': highEfficiencyMachines,
      'lowEfficiencyMachines': lowEfficiencyMachines,
    };
  }
}
