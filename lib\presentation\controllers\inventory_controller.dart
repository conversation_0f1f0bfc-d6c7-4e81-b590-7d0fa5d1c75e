import 'package:get/get.dart';
import 'package:mostafa_final/data/models/inventory.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/utils/logger.dart';
import 'package:uuid/uuid.dart';

class InventoryController extends GetxController {
  final InventoryRepository _inventoryRepository =
      Get.find<InventoryRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();
  final _uuid = const Uuid();

  // حالة الواجهة
  final RxBool isLoading = false.obs;
  final RxBool isSubmitting = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString searchQuery = ''.obs;

  // عناصر المخزون
  final RxList<Inventory> inventoryItems = <Inventory>[].obs;
  final RxList<Inventory> filteredItems = <Inventory>[].obs;

  // تنبيهات المخزون
  final RxList<InventoryAlert> activeAlerts = <InventoryAlert>[].obs;

  // عنصر المخزون الحالي
  final Rx<Inventory?> selectedItem = Rx<Inventory?>(null);

  // حركات المخزون للعنصر المحدد
  final RxList<InventoryMovement> movements = <InventoryMovement>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchInventoryItems();
    fetchActiveAlerts();
    debounce(
      searchQuery,
      (_) => filterItems(),
      time: const Duration(milliseconds: 300),
    );
  }

  // جلب جميع عناصر المخزون
  Future<void> fetchInventoryItems() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      inventoryItems.value = await _inventoryRepository.getAllInventoryItems();
      filterItems();
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // تصفية العناصر حسب البحث
  void filterItems() {
    final query = searchQuery.value.toLowerCase();

    if (query.isEmpty) {
      filteredItems.value = inventoryItems;
    } else {
      filteredItems.value =
          inventoryItems
              .where(
                (item) =>
                    item.itemName.toLowerCase().contains(query) ||
                    item.itemCode.toLowerCase().contains(query),
              )
              .toList();
    }
  }

  // جلب تنبيهات المخزون النشطة
  Future<void> fetchActiveAlerts() async {
    try {
      activeAlerts.value = await _inventoryRepository.getActiveAlerts();
    } catch (e) {
      AppLogger.e('خطأ في جلب التنبيهات', e);
    }
  }

  // اختيار عنصر مخزون
  Future<void> selectInventoryItem(String id) async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      final item = await _inventoryRepository.getInventoryItemById(id);
      if (item != null) {
        selectedItem.value = item;
        await fetchItemMovements(id);
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // جلب حركات المخزون لعنصر معين
  Future<void> fetchItemMovements(String inventoryId) async {
    try {
      movements.value = await _inventoryRepository.getInventoryMovements(
        inventoryId,
      );
    } catch (e) {
      AppLogger.e('خطأ في جلب حركات المخزون', e);
    }
  }

  // إنشاء عنصر مخزون جديد
  Future<bool> createInventoryItem({
    required String itemId,
    required String itemType,
    required String itemName,
    required String itemCode,
    required double currentQuantity,
    required double minimumLevel,
    required double reorderLevel,
    required String unit,
    required double unitCost,
  }) async {
    isSubmitting.value = true;
    errorMessage.value = '';

    try {
      // التحقق مما إذا كان العنصر موجود بالفعل
      final existingItem = await _inventoryRepository
          .getInventoryItemByItemIdAndType(itemId, itemType);

      if (existingItem != null) {
        errorMessage.value = 'هذا العنصر موجود بالفعل في المخزون';
        return false;
      }

      // تحديد حالة المخزون بناءً على المستويات
      final status = _determineInventoryStatus(
        currentQuantity,
        minimumLevel,
        reorderLevel,
      );

      // إنشاء عنصر المخزون
      final newItem = Inventory(
        itemId: itemId,
        itemType: itemType,
        itemName: itemName,
        itemCode: itemCode,
        currentQuantity: currentQuantity,
        minimumLevel: minimumLevel,
        reorderLevel: reorderLevel,
        unit: unit,
        unitCost: unitCost,
        lastUpdate: DateTime.now(),
        status: status,
      );

      // حفظ العنصر في قاعدة البيانات
      String id = await _inventoryRepository.createInventoryItem(newItem);

      // إنشاء حركة مخزون أولية إذا كانت الكمية أكبر من الصفر
      if (currentQuantity > 0) {
        await _inventoryRepository.updateInventoryQuantity(
          id,
          currentQuantity,
          'initial',
          _uuid.v4(),
          notes: 'رصيد افتتاحي',
          costPerUnit: unitCost,
        );
      }

      // إنشاء تنبيه إذا كانت الكمية منخفضة
      if (status != InventoryStatus.normal) {
        // سيتم إنشاء التنبيه تلقائياً في updateInventoryQuantity
      }

      // تحديث القائمة
      await fetchInventoryItems();
      await fetchActiveAlerts();

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // تسجيل حركة وارد للمخزون
  Future<bool> recordInventoryIn({
    required String inventoryId,
    required double quantity,
    required String referenceType,
    required String referenceId,
    required double costPerUnit,
    String notes = '',
  }) async {
    isSubmitting.value = true;
    errorMessage.value = '';

    try {
      await _inventoryRepository.updateInventoryQuantity(
        inventoryId,
        quantity, // كمية موجبة تعني وارد
        referenceType,
        referenceId,
        notes: notes,
        costPerUnit: costPerUnit,
      );

      // تحديث البيانات
      await fetchInventoryItems();
      if (selectedItem.value?.id == inventoryId) {
        await fetchItemMovements(inventoryId);
        await selectInventoryItem(inventoryId); // تحديث العنصر المحدد
      }
      await fetchActiveAlerts();

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // تسجيل حركة صادر من المخزون
  Future<bool> recordInventoryOut({
    required String inventoryId,
    required double quantity,
    required String referenceType,
    required String referenceId,
    String notes = '',
  }) async {
    isSubmitting.value = true;
    errorMessage.value = '';

    try {
      // التحقق من الكمية المتاحة
      final item = await _inventoryRepository.getInventoryItemById(inventoryId);
      if (item == null) {
        errorMessage.value = 'عنصر المخزون غير موجود';
        return false;
      }

      if (item.currentQuantity < quantity) {
        errorMessage.value = 'الكمية المطلوبة أكبر من الكمية المتاحة';
        return false;
      }

      await _inventoryRepository.updateInventoryQuantity(
        inventoryId,
        -quantity, // كمية سالبة تعني صادر
        referenceType,
        referenceId,
        notes: notes,
      );

      // تحديث البيانات
      await fetchInventoryItems();
      if (selectedItem.value?.id == inventoryId) {
        await fetchItemMovements(inventoryId);
        await selectInventoryItem(inventoryId); // تحديث العنصر المحدد
      }
      await fetchActiveAlerts();

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // تعديل عنصر مخزون
  Future<bool> updateInventoryItem({
    required String id,
    required String itemName,
    required String itemCode,
    required double minimumLevel,
    required double reorderLevel,
    required String unit,
    required double unitCost,
  }) async {
    isSubmitting.value = true;
    errorMessage.value = '';

    try {
      final item = await _inventoryRepository.getInventoryItemById(id);
      if (item == null) {
        errorMessage.value = 'عنصر المخزون غير موجود';
        return false;
      }

      final status = _determineInventoryStatus(
        item.currentQuantity,
        minimumLevel,
        reorderLevel,
      );

      final updatedItem = item.copyWith(
        itemName: itemName,
        itemCode: itemCode,
        minimumLevel: minimumLevel,
        reorderLevel: reorderLevel,
        unit: unit,
        unitCost: unitCost,
        status: status,
        lastUpdate: DateTime.now(),
      );

      await _inventoryRepository.updateInventoryItem(updatedItem);

      // تحديث البيانات
      await fetchInventoryItems();
      if (selectedItem.value?.id == id) {
        await selectInventoryItem(id);
      }
      await fetchActiveAlerts();

      return true;
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
      return false;
    } finally {
      isSubmitting.value = false;
    }
  }

  // مسح تنبيه
  Future<bool> resolveAlert(String alertId) async {
    try {
      await _inventoryRepository.resolveAlert(alertId);
      await fetchActiveAlerts();
      return true;
    } catch (e) {
      AppLogger.e('خطأ في مسح التنبيه', e);
      return false;
    }
  }

  // الحصول على عناصر المخزون ذات المستوى المنخفض
  Future<List<Inventory>> getLowInventoryItems() async {
    try {
      return await _inventoryRepository.getLowInventoryItems();
    } catch (e) {
      AppLogger.e('خطأ في جلب عناصر المخزون المنخفضة', e);
      return [];
    }
  }

  // تحديد حالة المخزون بناءً على المستويات
  InventoryStatus _determineInventoryStatus(
    double currentQuantity,
    double minimumLevel,
    double reorderLevel,
  ) {
    if (currentQuantity <= minimumLevel) {
      return InventoryStatus.critical;
    } else if (currentQuantity <= reorderLevel) {
      return InventoryStatus.low;
    } else {
      return InventoryStatus.normal;
    }
  }

  // إنشاء عناصر مخزون تلقائية للمواد الخام الحالية
  Future<void> initializeRawMaterialsInventory() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      // جلب جميع المواد الخام
      final rawMaterials = await _rawMaterialRepository.getAllRawMaterials();

      // التحقق من كل مادة والبحث عن عنصر مخزون موجود
      for (final rawMaterial in rawMaterials) {
        final existingItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(rawMaterial.id!, 'raw_material');

        // إذا لم يكن موجوداً، أنشئ عنصر مخزون جديد
        if (existingItem == null) {
          await createInventoryItem(
            itemId: rawMaterial.id!,
            itemType: 'raw_material',
            itemName: rawMaterial.name,
            itemCode: rawMaterial.code,
            currentQuantity: rawMaterial.availableQuantity,
            minimumLevel: 50.0, // قيمة افتراضية
            reorderLevel: 100.0, // قيمة افتراضية
            unit: 'كجم',
            unitCost: rawMaterial.costPerKg,
          );
        }
      }

      await fetchInventoryItems();
      await fetchActiveAlerts();
    } catch (e) {
      errorMessage.value = 'حدث خطأ: ${e.toString()}';
    } finally {
      isLoading.value = false;
    }
  }

  // الحصول على تقرير استهلاك المواد الخام
  Future<List<Map<String, dynamic>>> getRawMaterialConsumptionReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _inventoryRepository.getRawMaterialConsumptionReport(
        startDate,
        endDate,
      );
    } catch (e) {
      AppLogger.e('خطأ في جلب تقرير الاستهلاك', e);
      return [];
    }
  }

  // الحصول على تقرير إنتاج المنتجات
  Future<List<Map<String, dynamic>>> getProductionReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      return await _inventoryRepository.getProductionReport(startDate, endDate);
    } catch (e) {
      AppLogger.e('خطأ في جلب تقرير الإنتاج', e);
      return [];
    }
  }
}
