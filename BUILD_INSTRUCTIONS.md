# تعليمات بناء تطبيق نظام إدارة المصنع

## المشكلة

عند محاولة بناء ملف APK للتطبيق، تظهر المشكلة التالية:

```
FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':printing:verifyReleaseResources'.
> A failure occurred while executing com.android.build.gradle.tasks.VerifyLibraryResourcesTask$Action
   > Android resource linking failed
     ERROR: D:\flutter apps\mostafa_final\New folder\build\printing\intermediates\merged_res\release\mergeReleaseResources\values\values.xml:194: AAPT: error: resource android:attr/lStar not found.
```

هذه المشكلة ناتجة عن عدم توافق بين مكتبة `printing` وإصدار Android SDK المستخدم.

## الحلول المقترحة

### الحل 1: تحديث إصدار Android SDK

1. قم بتعديل ملف `android/app/build.gradle.kts` وتغيير `compileSdk` إلى 35:

```kotlin
android {
    namespace = "com.example.mostafa_final"
    compileSdk = 35
    ndkVersion = "27.0.12077973"
    
    // ...
}
```

2. قم بإضافة الإعدادات التالية لتجنب مشاكل الموارد:

```kotlin
android {
    // ...
    
    // Solución para el error de recurso android:attr/lStar
    lintOptions {
        disable += "ObsoleteSdkInt"
    }
    
    // Excluir el recurso problemático
    buildFeatures {
        buildConfig = true
    }
    
    // Configuración para excluir recursos problemáticos
    packagingOptions {
        resources {
            excludes += "/META-INF/{AL2.0,LGPL2.1}"
        }
    }
    
    // ...
}
```

### الحل 2: تعديل إصدار مكتبة printing

إذا استمرت المشكلة، يمكنك تجربة إصدار مختلف من مكتبة printing. قم بتعديل ملف `pubspec.yaml`:

```yaml
dependencies:
  # ...
  printing: 5.10.4  # جرب هذا الإصدار المحدد
  # ...
```

ثم قم بتنفيذ:

```
flutter clean
flutter pub get
flutter build apk
```

### الحل 3: إنشاء مشروع جديد ونقل الكود

إذا استمرت المشكلة، فإن الحل الأكثر فعالية هو إنشاء مشروع Flutter جديد ونقل الكود إليه:

1. قم بإنشاء مشروع Flutter جديد:

```
flutter create factory_management_system
```

2. قم بنسخ الملفات التالية من المشروع القديم إلى المشروع الجديد:
   - جميع الملفات في مجلد `lib`
   - ملف `pubspec.yaml` (مع تعديل اسم المشروع)
   - مجلد `assets` إذا كان موجودًا

3. قم بتعديل ملف `android/app/build.gradle` في المشروع الجديد لتعيين `compileSdk = 35`

4. قم بتنفيذ:

```
flutter pub get
flutter build apk
```

### الحل 4: استخدام Flutter 3.19.0 أو أحدث

تأكد من استخدام أحدث إصدار من Flutter:

```
flutter upgrade
flutter doctor
```

ثم قم بتنفيذ:

```
flutter clean
flutter pub get
flutter build apk
```

## ملاحظات إضافية

1. تأكد من تثبيت Android SDK 35 باستخدام Android Studio أو SDK Manager.

2. إذا كنت تستخدم مكتبة `printing` لإنشاء ملفات PDF وطباعتها، يمكنك النظر في استخدام مكتبات بديلة مثل:
   - `pdf` فقط لإنشاء ملفات PDF
   - `share_plus` لمشاركة ملفات PDF
   - `open_file` لفتح ملفات PDF

3. تأكد من توافق جميع المكتبات المستخدمة في المشروع.

## الخلاصة

المشكلة الرئيسية هي عدم توافق بين مكتبة `printing` وإصدار Android SDK. يمكن حل هذه المشكلة إما بتحديث إصدار Android SDK، أو استخدام إصدار متوافق من مكتبة `printing`، أو إنشاء مشروع جديد ونقل الكود إليه.

إذا استمرت المشكلة، فإن الحل الأكثر فعالية هو إنشاء مشروع جديد باستخدام أحدث إصدار من Flutter ونقل الكود إليه.
