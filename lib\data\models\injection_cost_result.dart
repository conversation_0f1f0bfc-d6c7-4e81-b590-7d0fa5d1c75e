/// نموذج بيانات نتائج حساب تكلفة الحقن
class InjectionCostResult {
  final int? id;
  final String machineId;
  final String machineName;
  final String moldId;
  final String moldName;
  final String materialId;
  final String materialName;

  // البيانات المدخلة
  final double cycleTimeInSeconds;
  final int cavityCount;
  final double partWeightInGrams;
  final double materialCostPerKg;
  final double scrapRate;
  final double setupTimeInMinutes;
  final int batchSize;
  final double electricityCostPerHour;
  final double operatorCostPerHour;
  final double overheadCostPerHour;
  final double maintenanceCostPerHour;

  // النتائج المحسوبة
  final double totalMaterialCost;
  final double totalElectricityCost;
  final double totalOperatorCost;
  final double totalOverheadCost;
  final double totalMaintenanceCost;
  final double totalSetupCost;
  final double costPerPart;
  final double productionTimeInHours;
  final int partsPerHour;

  // البيانات الإضافية
  final DateTime calculationDate;
  final String? notes;

  InjectionCostResult({
    this.id,
    required this.machineId,
    required this.machineName,
    required this.moldId,
    required this.moldName,
    required this.materialId,
    required this.materialName,
    required this.cycleTimeInSeconds,
    required this.cavityCount,
    required this.partWeightInGrams,
    required this.materialCostPerKg,
    required this.scrapRate,
    required this.setupTimeInMinutes,
    required this.batchSize,
    required this.electricityCostPerHour,
    required this.operatorCostPerHour,
    required this.overheadCostPerHour,
    required this.maintenanceCostPerHour,
    required this.totalMaterialCost,
    required this.totalElectricityCost,
    required this.totalOperatorCost,
    required this.totalOverheadCost,
    required this.totalMaintenanceCost,
    required this.totalSetupCost,
    required this.costPerPart,
    required this.productionTimeInHours,
    required this.partsPerHour,
    required this.calculationDate,
    this.notes,
  });

  // تحويل من Map إلى كائن
  factory InjectionCostResult.fromMap(Map<String, dynamic> map) {
    return InjectionCostResult(
      id: map['id'] as int?,
      machineId: map['machineId'] as String,
      machineName: map['machineName'] as String,
      moldId: map['moldId'] as String,
      moldName: map['moldName'] as String,
      materialId: map['materialId'] as String,
      materialName: map['materialName'] as String,
      cycleTimeInSeconds: map['cycleTimeInSeconds'] as double,
      cavityCount: map['cavityCount'] as int,
      partWeightInGrams: map['partWeightInGrams'] as double,
      materialCostPerKg: map['materialCostPerKg'] as double,
      scrapRate: map['scrapRate'] as double,
      setupTimeInMinutes: map['setupTimeInMinutes'] as double,
      batchSize: map['batchSize'] as int,
      electricityCostPerHour: map['electricityCostPerHour'] as double,
      operatorCostPerHour: map['operatorCostPerHour'] as double,
      overheadCostPerHour: map['overheadCostPerHour'] as double,
      maintenanceCostPerHour: map['maintenanceCostPerHour'] as double,
      totalMaterialCost: map['totalMaterialCost'] as double,
      totalElectricityCost: map['totalElectricityCost'] as double,
      totalOperatorCost: map['totalOperatorCost'] as double,
      totalOverheadCost: map['totalOverheadCost'] as double,
      totalMaintenanceCost: map['totalMaintenanceCost'] as double,
      totalSetupCost: map['totalSetupCost'] as double,
      costPerPart: map['costPerPart'] as double,
      productionTimeInHours: map['productionTimeInHours'] as double,
      partsPerHour: map['partsPerHour'] as int,
      calculationDate: DateTime.parse(map['calculationDate'] as String),
      notes: map['notes'] as String?,
    );
  }

  // تحويل من كائن إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'machineId': machineId,
      'machineName': machineName,
      'moldId': moldId,
      'moldName': moldName,
      'materialId': materialId,
      'materialName': materialName,
      'cycleTimeInSeconds': cycleTimeInSeconds,
      'cavityCount': cavityCount,
      'partWeightInGrams': partWeightInGrams,
      'materialCostPerKg': materialCostPerKg,
      'scrapRate': scrapRate,
      'setupTimeInMinutes': setupTimeInMinutes,
      'batchSize': batchSize,
      'electricityCostPerHour': electricityCostPerHour,
      'operatorCostPerHour': operatorCostPerHour,
      'overheadCostPerHour': overheadCostPerHour,
      'maintenanceCostPerHour': maintenanceCostPerHour,
      'totalMaterialCost': totalMaterialCost,
      'totalElectricityCost': totalElectricityCost,
      'totalOperatorCost': totalOperatorCost,
      'totalOverheadCost': totalOverheadCost,
      'totalMaintenanceCost': totalMaintenanceCost,
      'totalSetupCost': totalSetupCost,
      'costPerPart': costPerPart,
      'productionTimeInHours': productionTimeInHours,
      'partsPerHour': partsPerHour,
      'calculationDate': calculationDate.toIso8601String(),
      if (notes != null) 'notes': notes,
    };
  }
}
