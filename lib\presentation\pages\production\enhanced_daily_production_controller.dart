import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
// import 'package:mostafa_final/data/repositories/inventory_repository.dart';
// import 'package:mostafa_final/utils/pdf_generator.dart';
import 'package:mostafa_final/utils/logger.dart';

/// وحدة التحكم المحسنة لصفحة تسجيل الإنتاج اليومي
class EnhancedDailyProductionController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();
  final ProductionRepository _productionRepository =
      Get.find<ProductionRepository>();
  // final InventoryRepository _inventoryRepository =
  //     Get.find<InventoryRepository>();

  final formKey = GlobalKey<FormState>();

  // متغيرات للتحميل
  final RxBool isLoading = true.obs;
  final RxBool isLoadingHistory = false.obs;
  final RxBool isSaving = false.obs;

  // متغيرات للحفظ
  final TextEditingController rawMaterialUsedController =
      TextEditingController();
  final TextEditingController partsProducedController = TextEditingController();
  final TextEditingController cycleTimeController = TextEditingController();

  // متغيرات البحث والفلترة
  final TextEditingController searchController = TextEditingController();
  final RxString searchQuery = ''.obs;
  final RxString selectedFilter = 'all'.obs;

  // متغير لعدد الورديات
  final RxDouble selectedShiftsCount = 1.0.obs;
  final List<double> availableShifts = [1.0, 1.5, 2.0, 3.0];

  // متغيرات للاختيار
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final RxString selectedMachineId = ''.obs;
  final RxString selectedMoldId = ''.obs;
  final RxString selectedRawMaterialId = ''.obs;

  // متغيرات لعرض البيانات المحسوبة
  final RxDouble calculatedWeight = 0.0.obs;
  final RxDouble calculatedScrap = 0.0.obs;

  // قوائم البيانات
  final RxList<Machine> machines = <Machine>[].obs;
  final RxList<Mold> molds = <Mold>[].obs;
  final RxList<Mold> availableMolds = <Mold>[].obs;
  final RxList<RawMaterial> rawMaterials = <RawMaterial>[].obs;
  final RxList<Map<String, dynamic>> dailyProduction =
      <Map<String, dynamic>>[].obs;

  // الإحصائيات السريعة
  final RxMap<String, dynamic> quickStats =
      <String, dynamic>{
        'totalProduction': 0,
        'totalMaterials': 0.0,
        'avgCycleTime': 0.0,
        'recordCount': 0,
      }.obs;

  // ثوابت
  final double electricityCostPerKwh = 1.5;
  final double operatorCostPerHour = 20.0;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();

    // إضافة المستمعين
    rawMaterialUsedController.addListener(calculatePartsFromWeight);
    searchController.addListener(() {
      searchQuery.value = searchController.text;
    });
  }

  @override
  void onClose() {
    rawMaterialUsedController.dispose();
    partsProducedController.dispose();
    cycleTimeController.dispose();
    searchController.dispose();
    super.onClose();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;

    try {
      await Future.wait([fetchMachines(), fetchMolds(), fetchRawMaterials()]);

      await fetchDailyProduction();
      calculateQuickStats();
    } catch (e) {
      AppLogger.e('Error loading initial data', e);
      _showErrorSnackbar('خطأ في تحميل البيانات', e.toString());
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث البيانات
  Future<void> refreshData() async {
    await loadInitialData();
    _showSuccessSnackbar('تم تحديث البيانات بنجاح');
  }

  /// جلب قائمة الماكينات
  Future<void> fetchMachines() async {
    try {
      final machinesList = await _machineRepository.getAllMachines();
      machines.assignAll(machinesList);

      if (machines.isNotEmpty && selectedMachineId.isEmpty) {
        selectMachine(machines.first.id!);
      }
    } catch (e) {
      AppLogger.e('Error fetching machines', e);
    }
  }

  /// جلب قائمة الإسطمبات
  Future<void> fetchMolds() async {
    try {
      final moldsList = await _moldRepository.getAllMolds();
      molds.assignAll(moldsList);

      if (selectedMachineId.isNotEmpty) {
        updateAvailableMolds();
      }
    } catch (e) {
      AppLogger.e('Error fetching molds', e);
    }
  }

  /// جلب قائمة المواد الخام
  Future<void> fetchRawMaterials() async {
    try {
      final materialsList = await _rawMaterialRepository.getAllRawMaterials();
      rawMaterials.assignAll(materialsList);

      if (rawMaterials.isNotEmpty && selectedRawMaterialId.isEmpty) {
        selectRawMaterial(rawMaterials.first.id!);
      }
    } catch (e) {
      AppLogger.e('Error fetching raw materials', e);
    }
  }

  /// جلب إنتاج اليوم المحدد
  Future<void> fetchDailyProduction() async {
    isLoadingHistory.value = true;

    try {
      final dailyProductionList = await _productionRepository
          .getProductionByDate(selectedDate.value);
      dailyProduction.assignAll(dailyProductionList);
      calculateQuickStats();
    } catch (e) {
      AppLogger.e('Error fetching daily production', e);
    } finally {
      isLoadingHistory.value = false;
    }
  }

  /// حساب الإحصائيات السريعة
  void calculateQuickStats() {
    if (dailyProduction.isEmpty) {
      quickStats.value = {
        'totalProduction': 0,
        'totalMaterials': 0.0,
        'avgCycleTime': 0.0,
        'recordCount': 0,
      };
      return;
    }

    int totalProduction = 0;
    double totalMaterials = 0.0;
    double totalCycleTime = 0.0;

    for (var record in dailyProduction) {
      totalProduction += record['partsProduced'] as int;
      totalMaterials += record['rawMaterialUsed'] as double;
      totalCycleTime += record['cycleTime'] as double;
    }

    quickStats.value = {
      'totalProduction': totalProduction,
      'totalMaterials': totalMaterials,
      'avgCycleTime': totalCycleTime / dailyProduction.length,
      'recordCount': dailyProduction.length,
    };
  }

  /// تحديث قائمة الإسطمبات المتاحة للماكينة المحددة
  void updateAvailableMolds() {
    if (selectedMachineId.isEmpty) {
      availableMolds.clear();
      return;
    }

    final currentMachine = machines.firstWhereOrNull(
      (m) => m.id == selectedMachineId.value,
    );

    if (currentMachine != null) {
      final availableMoldsList =
          molds
              .where(
                (mold) =>
                    mold.status == MoldStatus.available ||
                    (mold.currentMachineId != null &&
                        mold.currentMachineId == currentMachine.id),
              )
              .toList();

      availableMolds.assignAll(availableMoldsList);

      if (currentMachine.currentMoldId != null) {
        selectedMoldId.value = currentMachine.currentMoldId!;
        _calculateWeight();
      } else if (availableMolds.isNotEmpty) {
        selectedMoldId.value = availableMolds.first.id!;
        _calculateWeight();
      } else {
        selectedMoldId.value = '';
        calculatedWeight.value = 0.0;
      }
    }
  }

  /// حساب عدد القطع من الوزن الإجمالي
  void calculatePartsFromWeight() {
    if (selectedMoldId.isEmpty || rawMaterialUsedController.text.isEmpty) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    final double? totalWeight = double.tryParse(rawMaterialUsedController.text);
    if (totalWeight == null || totalWeight <= 0) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    final double weightPerPart = selectedMold.singlePartWeight;
    final int cavityCount = selectedMold.cavityCount;
    final double scrapRate = 0.005;

    final double adjustedWeightPerPart = weightPerPart * (1 + scrapRate);
    final int estimatedParts =
        ((totalWeight * 1000) / adjustedWeightPerPart).round();
    final int adjustedParts = (estimatedParts ~/ cavityCount) * cavityCount;

    partsProducedController.text = adjustedParts.toString();

    final double theoreticalWeight = (adjustedParts * weightPerPart) / 1000;
    calculatedScrap.value = theoreticalWeight * scrapRate;

    calculateCycleTime(adjustedParts);
  }

  /// حساب وقت الدورة تلقائيًا
  void calculateCycleTime(int partsCount) {
    if (partsCount <= 0 || selectedMoldId.isEmpty) {
      cycleTimeController.text = '';
      return;
    }

    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      cycleTimeController.text = '';
      return;
    }

    final int cavityCount = selectedMold.cavityCount;
    final int cycleCount = partsCount ~/ cavityCount;
    final double shiftTimeInSeconds = 28800.0 * selectedShiftsCount.value;
    final double calculatedCycleTime = shiftTimeInSeconds / cycleCount;

    cycleTimeController.text = calculatedCycleTime.toStringAsFixed(2);
  }

  /// حساب الوزن النظري للإنتاج
  void _calculateWeight() {
    if (selectedMoldId.isEmpty || partsProducedController.text.isEmpty) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    final int? partsProduced = int.tryParse(partsProducedController.text);
    if (partsProduced == null || partsProduced <= 0) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    final double theoreticalWeight =
        (selectedMold.singlePartWeight * partsProduced) / 1000;
    calculatedWeight.value = theoreticalWeight;

    final double? rawMaterialUsed = double.tryParse(
      rawMaterialUsedController.text,
    );
    if (rawMaterialUsed != null && rawMaterialUsed > 0) {
      calculatedScrap.value = rawMaterialUsed - theoreticalWeight;
    } else {
      calculatedScrap.value = 0.0;
    }
  }

  /// اختيار تاريخ الإنتاج
  Future<void> selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: Colors.blue,
            colorScheme: const ColorScheme.light(primary: Colors.blue),
            buttonTheme: const ButtonThemeData(
              textTheme: ButtonTextTheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null && pickedDate != selectedDate.value) {
      selectedDate.value = pickedDate;
      await fetchDailyProduction();
    }
  }

  /// اختيار ماكينة
  void selectMachine(String machineId) {
    selectedMachineId.value = machineId;
    updateAvailableMolds();
  }

  /// اختيار إسطمبة
  void selectMold(String moldId) {
    selectedMoldId.value = moldId;
    _calculateWeight();
  }

  /// اختيار مادة خام
  void selectRawMaterial(String materialId) {
    selectedRawMaterialId.value = materialId;
  }

  /// البحث عن مادة خام حسب المعرف
  RawMaterial? findRawMaterialById(String id) {
    return rawMaterials.firstWhereOrNull((material) => material.id == id);
  }

  /// البحث عن إسطمبة حسب المعرف
  Mold? findMoldById(String id) {
    return molds.firstWhereOrNull((mold) => mold.id == id);
  }

  /// تحديث استعلام البحث
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  /// مسح البحث
  void clearSearch() {
    searchController.clear();
    searchQuery.value = '';
  }

  /// الحصول على السجلات المفلترة
  List<Map<String, dynamic>> get filteredDailyProduction {
    if (searchQuery.value.isEmpty) {
      return dailyProduction;
    }

    return dailyProduction.where((record) {
      final machineName =
          (record['machineName'] ?? '').toString().toLowerCase();
      final moldName = (record['moldName'] ?? '').toString().toLowerCase();
      final materialName =
          (record['rawMaterialName'] ?? '').toString().toLowerCase();
      final query = searchQuery.value.toLowerCase();

      return machineName.contains(query) ||
          moldName.contains(query) ||
          materialName.contains(query);
    }).toList();
  }

  /// تنسيق التاريخ
  String formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd', 'ar').format(date);
  }

  /// تنسيق الوقت فقط
  String formatTimeOnly(DateTime date) {
    return DateFormat('HH:mm', 'ar').format(date);
  }

  /// عرض رسالة نجاح
  void _showSuccessSnackbar(String message) {
    Get.snackbar(
      'نجح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 3),
    );
  }

  /// عرض رسالة خطأ
  void _showErrorSnackbar(String title, String message) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
      duration: const Duration(seconds: 5),
    );
  }

  /// حفظ الإنتاج
  Future<void> saveProduction() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    if (selectedMachineId.isEmpty ||
        selectedMoldId.isEmpty ||
        selectedRawMaterialId.isEmpty) {
      _showErrorSnackbar('خطأ', 'يرجى اختيار الماكينة والإسطمبة والمادة الخام');
      return;
    }

    isSaving.value = true;

    try {
      final double rawMaterialUsed = double.parse(
        rawMaterialUsedController.text,
      );
      final int partsProduced = int.parse(partsProducedController.text);
      final double cycleTime = double.parse(cycleTimeController.text);

      final Machine? machine = machines.firstWhereOrNull(
        (m) => m.id == selectedMachineId.value,
      );
      if (machine == null) {
        throw Exception('لم يتم العثور على الماكينة');
      }

      final Mold? mold = findMoldById(selectedMoldId.value);
      if (mold == null) {
        throw Exception('لم يتم العثور على الإسطمبة');
      }

      // حساب التكاليف
      final double machineHourlyCost =
          machine.powerConsumption * electricityCostPerKwh;
      final double operatingHours = (cycleTime * partsProduced) / 3600;
      final double electricityCost = machineHourlyCost * operatingHours;
      final double operatorCost = operatorCostPerHour * operatingHours;

      // إنشاء كائن الإنتاج
      final production = Production(
        date: DateTime.now(),
        machineId: selectedMachineId.value,
        moldId: selectedMoldId.value,
        rawMaterialId: selectedRawMaterialId.value,
        rawMaterialUsed: rawMaterialUsed,
        partsProduced: partsProduced,
        cycleTime: cycleTime,
        electricityCost: electricityCost,
        operatorCost: operatorCost,
        shiftsCount: selectedShiftsCount.value,
      );

      // حفظ الإنتاج
      await _productionRepository.createProduction(production);

      // تحديث المخزون
      await _updateInventoryWithProducedParts(
        mold,
        partsProduced,
        rawMaterialUsed,
        selectedRawMaterialId.value,
      );

      // تحديث البيانات
      await Future.wait([fetchDailyProduction(), fetchRawMaterials()]);

      // إعادة تعيين النموذج
      _resetForm();

      _showSuccessSnackbar('تم حفظ الإنتاج بنجاح');
    } catch (e) {
      AppLogger.e('Error saving production', e);
      _showErrorSnackbar('خطأ في الحفظ', e.toString());
    } finally {
      isSaving.value = false;
    }
  }

  /// تحديث المخزون بالقطع المنتجة
  Future<void> _updateInventoryWithProducedParts(
    Mold mold,
    int partsProduced,
    double rawMaterialUsed,
    String rawMaterialId,
  ) async {
    try {
      // تحديث المخزون بالقطع المنتجة
      // سيتم تنفيذ تحديث المخزون لاحقاً
      AppLogger.i('تم إنتاج $partsProduced قطعة من ${mold.name}');

      // تقليل كمية المواد الخام
      await _rawMaterialRepository.updateRawMaterialQuantity(
        rawMaterialId,
        -rawMaterialUsed,
      );
    } catch (e) {
      AppLogger.e('Error updating inventory', e);
      rethrow;
    }
  }

  /// إعادة تعيين النموذج
  void _resetForm() {
    rawMaterialUsedController.clear();
    partsProducedController.clear();
    cycleTimeController.clear();
    calculatedWeight.value = 0.0;
    calculatedScrap.value = 0.0;
  }

  /// تعديل سجل إنتاج
  Future<void> editProduction(Map<String, dynamic> production) async {
    // تنفيذ تعديل الإنتاج
    _showSuccessSnackbar('سيتم تنفيذ التعديل قريباً');
  }

  /// حذف سجل إنتاج
  Future<void> deleteProduction(String productionId) async {
    try {
      await _productionRepository.deleteProduction(productionId);
      await fetchDailyProduction();
      _showSuccessSnackbar('تم حذف السجل بنجاح');
    } catch (e) {
      AppLogger.e('Error deleting production', e);
      _showErrorSnackbar('خطأ في الحذف', e.toString());
    }
  }

  /// نسخ سجل إنتاج
  Future<void> duplicateProduction(Map<String, dynamic> production) async {
    // تنفيذ نسخ الإنتاج
    _showSuccessSnackbar('سيتم تنفيذ النسخ قريباً');
  }

  /// تصدير إلى PDF
  Future<void> exportToPDF() async {
    try {
      // تنفيذ تصدير PDF
      _showSuccessSnackbar('سيتم تنفيذ التصدير قريباً');
    } catch (e) {
      AppLogger.e('Error exporting to PDF', e);
      _showErrorSnackbar('خطأ في التصدير', e.toString());
    }
  }

  /// تصدير إلى Excel
  Future<void> exportToExcel() async {
    try {
      // تنفيذ تصدير Excel
      _showSuccessSnackbar('سيتم تنفيذ التصدير قريباً');
    } catch (e) {
      AppLogger.e('Error exporting to Excel', e);
      _showErrorSnackbar('خطأ في التصدير', e.toString());
    }
  }
}
