import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// بطاقة معلومات متجاوبة مع حجم الشاشة
class ResponsiveInfoCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color? iconColor;
  final Color? backgroundColor;
  final VoidCallback? onTap;
  final Widget? trailing;
  final String? subtitle;

  const ResponsiveInfoCard({
    super.key,
    required this.title,
    required this.value,
    required this.icon,
    this.iconColor,
    this.backgroundColor,
    this.onTap,
    this.trailing,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);
    
    final effectiveIconColor = iconColor ?? EnhancedAppTheme.primaryColor;
    final effectiveBackgroundColor = backgroundColor ?? Colors.white;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        child: Container(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          decoration: BoxDecoration(
            color: effectiveBackgroundColor,
            borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: EdgeInsets.all(ScreenSize.getPadding(8)),
                    decoration: BoxDecoration(
                      color: effectiveIconColor.withAlpha(25),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon, 
                      color: effectiveIconColor, 
                      size: ScreenSize.isSmallScreen ? 20 : 24
                    ),
                  ),
                  if (trailing != null) trailing!,
                ],
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextUtils.responsiveText(
                title,
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  color: EnhancedAppTheme.textSecondary,
                  fontFamily: 'Cairo',
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(4)),
              TextUtils.responsiveText(
                value,
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 16 : 20,
                  fontWeight: FontWeight.bold,
                  color: EnhancedAppTheme.textPrimary,
                  fontFamily: 'Cairo',
                ),
                maxLines: 1,
              ),
              if (subtitle != null) ...[
                SizedBox(height: ScreenSize.getPadding(4)),
                TextUtils.responsiveText(
                  subtitle!,
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                    color: EnhancedAppTheme.textLight,
                    fontFamily: 'Cairo',
                  ),
                  maxLines: 1,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}

/// بطاقة إحصائيات متجاوبة مع حجم الشاشة
class ResponsiveStatsCard extends StatelessWidget {
  final String title;
  final List<ResponsiveStatItem> stats;
  final Color? headerColor;
  final VoidCallback? onTap;
  final Widget? trailing;

  const ResponsiveStatsCard({
    super.key,
    required this.title,
    required this.stats,
    this.headerColor,
    this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);
    
    final effectiveHeaderColor = headerColor ?? EnhancedAppTheme.primaryColor;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.all(ScreenSize.getPadding(16)),
              decoration: BoxDecoration(
                color: effectiveHeaderColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(EnhancedAppTheme.borderRadius),
                  topRight: Radius.circular(EnhancedAppTheme.borderRadius),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TextUtils.responsiveText(
                    title,
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      fontFamily: 'Cairo',
                    ),
                    maxLines: 1,
                  ),
                  if (trailing != null) trailing!,
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.all(ScreenSize.getPadding(16)),
              child: Column(
                children: stats.map((stat) {
                  return Padding(
                    padding: EdgeInsets.only(bottom: ScreenSize.getPadding(12)),
                    child: Row(
                      children: [
                        if (stat.icon != null)
                          Container(
                            padding: EdgeInsets.all(ScreenSize.getPadding(8)),
                            decoration: BoxDecoration(
                              color: stat.color?.withAlpha(25) ??
                                  EnhancedAppTheme.primaryColor.withAlpha(25),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              stat.icon,
                              color: stat.color ?? EnhancedAppTheme.primaryColor,
                              size: ScreenSize.isSmallScreen ? 16 : 20,
                            ),
                          ),
                        if (stat.icon != null) 
                          SizedBox(width: ScreenSize.getPadding(12)),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              TextUtils.responsiveText(
                                stat.label,
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                  color: EnhancedAppTheme.textSecondary,
                                  fontFamily: 'Cairo',
                                ),
                                maxLines: 1,
                              ),
                              SizedBox(height: ScreenSize.getPadding(4)),
                              TextUtils.responsiveText(
                                stat.value,
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                                  fontWeight: FontWeight.bold,
                                  color: stat.color ?? EnhancedAppTheme.textPrimary,
                                  fontFamily: 'Cairo',
                                ),
                                maxLines: 1,
                              ),
                            ],
                          ),
                        ),
                        if (stat.trailing != null) stat.trailing!,
                      ],
                    ),
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// عنصر إحصائي متجاوب مع حجم الشاشة
class ResponsiveStatItem {
  final String label;
  final String value;
  final IconData? icon;
  final Color? color;
  final Widget? trailing;

  ResponsiveStatItem({
    required this.label,
    required this.value,
    this.icon,
    this.color,
    this.trailing,
  });
}
