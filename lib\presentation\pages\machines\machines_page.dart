import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/app_theme.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'machines_controller.dart';
import 'add_edit_machine_dialog.dart';

class MachinesPage extends StatelessWidget {
  const MachinesPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final MachinesController controller = Get.put(MachinesController());

    return ResponsivePage(
      title: 'إدارة الماكينات',
      drawer: const CustomDrawer(),
      body: Column(
        children: [
          _buildHeader(context),
          Expanded(child: _buildMachinesList(controller)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddMachineDialog(context, controller),
        tooltip: 'إضافة ماكينة جديدة',
        child: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 20 : 24),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(ScreenSize.getPadding(16)),
      color: Colors.grey.shade100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextUtils.responsiveText(
            'ماكينات المصنع',
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
          ),
          SizedBox(height: ScreenSize.getPadding(8)),
          TextUtils.responsiveText(
            'إدارة ماكينات حقن البلاستيك والإسطمبات المستخدمة',
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
              color: Colors.grey.shade700,
            ),
            maxLines: 2,
          ),
        ],
      ),
    );
  }

  Widget _buildMachinesList(MachinesController controller) {
    return GetBuilder<MachinesController>(
      builder: (_) {
        if (controller.isLoading.value) {
          return Center(
            child: CircularProgressIndicator(
              strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
            ),
          );
        }

        if (controller.machines.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.precision_manufacturing_outlined,
                  size: ScreenSize.isSmallScreen ? 60 : 80,
                  color: Colors.grey.shade400,
                ),
                SizedBox(height: ScreenSize.getPadding(16)),
                TextUtils.responsiveText(
                  'لا توجد ماكينات مضافة',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                    color: Colors.grey.shade600,
                  ),
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: ScreenSize.getPadding(8)),
                ElevatedButton.icon(
                  onPressed:
                      () => _showAddMachineDialog(Get.context!, controller),
                  icon: Icon(
                    Icons.add,
                    size: ScreenSize.isSmallScreen ? 18 : 24,
                  ),
                  label: TextUtils.responsiveText(
                    'إضافة ماكينة جديدة',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(16),
                      vertical: ScreenSize.getPadding(8),
                    ),
                  ),
                ),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: EdgeInsets.all(ScreenSize.getPadding(8)),
          itemCount: controller.machines.length,
          itemBuilder: (context, index) {
            final machine = controller.machines[index];
            return _buildMachineCard(context, machine, controller);
          },
        );
      },
    );
  }

  Widget _buildMachineCard(
    BuildContext context,
    Machine machine,
    MachinesController controller,
  ) {
    final currentMold = controller.getMoldInfo(machine.currentMoldId);

    return Card(
      margin: EdgeInsets.only(bottom: ScreenSize.getPadding(12)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextUtils.responsiveText(
                  machine.name,
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                ),
                _buildMachineStatusChip(machine.status),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            TextUtils.responsiveText(
              'موديل: ${machine.model}',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                color: Colors.grey.shade700,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(4)),
            TextUtils.responsiveText(
              'استهلاك الطاقة: ${machine.powerConsumption} كيلوواط',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                color: Colors.grey.shade700,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            Divider(height: ScreenSize.getPadding(1)),
            SizedBox(height: ScreenSize.getPadding(8)),
            Row(
              children: [
                Icon(
                  Icons.settings,
                  size: ScreenSize.isSmallScreen ? 16 : 18,
                  color: Colors.grey,
                ),
                SizedBox(width: ScreenSize.getPadding(8)),
                Expanded(
                  child: TextUtils.responsiveText(
                    currentMold != null
                        ? 'الإسطمبة الحالية: ${currentMold['name']}'
                        : 'لا توجد إسطمبة مركبة',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      color: Colors.grey.shade700,
                    ),
                    maxLines: 1,
                  ),
                ),
                TextButton(
                  onPressed: () => controller.showChangeMoldDialog(machine.id!),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(8),
                      vertical: ScreenSize.getPadding(4),
                    ),
                  ),
                  child: TextUtils.responsiveText(
                    'تغيير الإسطمبة',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      color: Theme.of(context).primaryColor,
                    ),
                    maxLines: 1,
                  ),
                ),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            Row(
              children: [
                Icon(
                  Icons.calendar_today,
                  size: ScreenSize.isSmallScreen ? 16 : 18,
                  color: Colors.grey,
                ),
                SizedBox(width: ScreenSize.getPadding(8)),
                Expanded(
                  child: TextUtils.responsiveText(
                    machine.lastMoldChange != null
                        ? 'آخر تغيير: ${_formatDate(machine.lastMoldChange!)}'
                        : 'لا يوجد سجل لتغيير الإسطمبة',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      color: Colors.grey.shade700,
                    ),
                    maxLines: 1,
                  ),
                ),
                if (machine.id != null)
                  TextButton.icon(
                    onPressed:
                        () => controller.showMoldChangeHistoryDialog(
                          machine.id!,
                          machine.name,
                        ),
                    icon: Icon(
                      Icons.history,
                      size: ScreenSize.isSmallScreen ? 16 : 18,
                    ),
                    label: TextUtils.responsiveText(
                      'عرض السجل',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        color: Theme.of(context).primaryColor,
                      ),
                      maxLines: 1,
                    ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                  ),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                TextButton.icon(
                  onPressed:
                      () =>
                          _showEditMachineDialog(context, machine, controller),
                  icon: Icon(
                    Icons.edit,
                    size: ScreenSize.isSmallScreen ? 16 : 18,
                  ),
                  label: TextUtils.responsiveText(
                    'تعديل',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      color: Theme.of(context).primaryColor,
                    ),
                    maxLines: 1,
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(8),
                      vertical: ScreenSize.getPadding(4),
                    ),
                  ),
                ),
                SizedBox(width: ScreenSize.getPadding(8)),
                TextButton.icon(
                  onPressed:
                      () =>
                          _showDeleteConfirmation(context, machine, controller),
                  icon: Icon(
                    Icons.delete,
                    color: Colors.red,
                    size: ScreenSize.isSmallScreen ? 16 : 18,
                  ),
                  label: TextUtils.responsiveText(
                    'حذف',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      color: Colors.red,
                    ),
                    maxLines: 1,
                  ),
                  style: TextButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(8),
                      vertical: ScreenSize.getPadding(4),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMachineStatusChip(String status) {
    Color chipColor;

    switch (status) {
      case MachineStatus.operating:
        chipColor = AppTheme.successColor;
        break;
      case MachineStatus.stopped:
        chipColor = Colors.orange;
        break;
      case MachineStatus.maintenance:
        chipColor = AppTheme.errorColor;
        break;
      default:
        chipColor = Colors.grey;
    }

    return Chip(
      label: TextUtils.responsiveText(
        status,
        style: TextStyle(
          color: Colors.white,
          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
        ),
        maxLines: 1,
      ),
      backgroundColor: chipColor,
      padding: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(4),
        vertical: ScreenSize.isSmallScreen ? 0 : 2,
      ),
      labelPadding: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(4),
        vertical: 0,
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.year}/${date.month}/${date.day}';
  }

  void _showAddMachineDialog(
    BuildContext context,
    MachinesController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    showDialog(
      context: context,
      builder: (context) => AddEditMachineDialog(onSave: controller.addMachine),
    );
  }

  void _showEditMachineDialog(
    BuildContext context,
    Machine machine,
    MachinesController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    showDialog(
      context: context,
      builder:
          (context) => AddEditMachineDialog(
            machine: machine,
            onSave: controller.updateMachine,
          ),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Machine machine,
    MachinesController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'تأكيد الحذف',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            content: TextUtils.responsiveText(
              'هل أنت متأكد من حذف الماكينة "${machine.name}"؟',
              style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 14 : 16),
              maxLines: 2,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(24),
              vertical: ScreenSize.getPadding(16),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  controller.deleteMachine(machine.id!);
                },
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'حذف',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    color: Colors.red,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }
}
