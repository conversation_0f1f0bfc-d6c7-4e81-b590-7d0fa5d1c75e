import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:intl/intl.dart';

class PdfGenerator {
  // إنشاء تقرير PDF للإنتاج اليومي
  static Future<File> generateDailyProductionReport(
    DateTime date,
    List<Map<String, dynamic>> productionItems,
    String reportTitle,
  ) async {
    // إنشاء مستند PDF
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // توليد محتوى التقرير
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            _buildHeader(ttf, reportTitle, date),
            pw.SizedBox(height: 20),
            _buildSummary(ttf, productionItems),
            pw.SizedBox(height: 20),
            _buildProductionTable(ttf, productionItems),
          ];
        },
      ),
    );

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final String formattedDate = DateFormat('yyyy-MM-dd').format(date);
    final file = File('${output.path}/production_report_$formattedDate.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // إنشاء تقرير استهلاك المواد الخام
  static Future<File> generateRawMaterialConsumptionReport(
    List<Map<String, dynamic>> consumptionData,
    String reportTitle,
    DateTime startDate,
    DateTime endDate,
    double totalConsumption,
    double totalCost,
  ) async {
    // إنشاء مستند PDF
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // توليد محتوى التقرير
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            _buildHeader(ttf, reportTitle, DateTime.now()),
            pw.SizedBox(height: 20),
            _buildDateRangeInfo(ttf, startDate, endDate),
            pw.SizedBox(height: 20),
            _buildConsumptionSummary(ttf, totalConsumption, totalCost),
            pw.SizedBox(height: 20),
            _buildConsumptionTable(ttf, consumptionData),
          ];
        },
      ),
    );

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final String formattedDate = DateFormat(
      'yyyy-MM-dd',
    ).format(DateTime.now());
    final file = File(
      '${output.path}/raw_material_consumption_$formattedDate.pdf',
    );
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // إنشاء تقرير استلام المواد الخام
  static Future<File> generateRawMaterialReceiptReport(
    List<Map<String, dynamic>> receiptData,
    String reportTitle,
    DateTime startDate,
    DateTime endDate,
    double totalReceipt,
    double totalCost,
  ) async {
    // إنشاء مستند PDF
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // توليد محتوى التقرير
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            _buildHeader(ttf, reportTitle, DateTime.now()),
            pw.SizedBox(height: 20),
            _buildDateRangeInfo(ttf, startDate, endDate),
            pw.SizedBox(height: 20),
            _buildReceiptSummary(ttf, totalReceipt, totalCost),
            pw.SizedBox(height: 20),
            _buildReceiptTable(ttf, receiptData),
          ];
        },
      ),
    );

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final String formattedDate = DateFormat(
      'yyyy-MM-dd',
    ).format(DateTime.now());
    final file = File('${output.path}/raw_material_receipt_$formattedDate.pdf');
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // إنشاء تقرير مخزون المواد الخام
  static Future<File> generateRawMaterialInventoryReport(
    List<Map<String, dynamic>> inventoryData,
    String reportTitle,
  ) async {
    // إنشاء مستند PDF
    final pdf = pw.Document();

    // تحميل الخط العربي
    final arabicFont = await rootBundle.load('assets/fonts/Cairo-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // حساب إجمالي قيمة المخزون
    double totalValue = 0.0;
    for (final item in inventoryData) {
      double quantity = item['availableQuantity'] ?? 0.0;
      double costPerKg = item['costPerKg'] ?? 0.0;
      totalValue += quantity * costPerKg;
    }

    // توليد محتوى التقرير
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        build: (pw.Context context) {
          return [
            _buildHeader(ttf, reportTitle, DateTime.now()),
            pw.SizedBox(height: 20),
            _buildInventorySummary(ttf, inventoryData.length, totalValue),
            pw.SizedBox(height: 20),
            _buildInventoryTable(ttf, inventoryData),
          ];
        },
      ),
    );

    // حفظ الملف
    final output = await getTemporaryDirectory();
    final String formattedDate = DateFormat(
      'yyyy-MM-dd',
    ).format(DateTime.now());
    final file = File(
      '${output.path}/raw_material_inventory_$formattedDate.pdf',
    );
    await file.writeAsBytes(await pdf.save());

    return file;
  }

  // بناء معلومات نطاق التاريخ
  static pw.Widget _buildDateRangeInfo(
    pw.Font ttf,
    DateTime startDate,
    DateTime endDate,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.Text(
            'نطاق التقرير: من ',
            style: pw.TextStyle(font: ttf, fontSize: 12),
          ),
          pw.Text(
            DateFormat('yyyy/MM/dd').format(startDate),
            style: pw.TextStyle(
              font: ttf,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.Text(' إلى ', style: pw.TextStyle(font: ttf, fontSize: 12)),
          pw.Text(
            DateFormat('yyyy/MM/dd').format(endDate),
            style: pw.TextStyle(
              font: ttf,
              fontSize: 12,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  // بناء ملخص الاستهلاك
  static pw.Widget _buildConsumptionSummary(
    pw.Font ttf,
    double totalConsumption,
    double totalCost,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص استهلاك المواد الخام',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي الاستهلاك',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '${totalConsumption.toStringAsFixed(2)} كجم',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي التكلفة',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '${totalCost.toStringAsFixed(2)} جنيه',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء ملخص الاستلام
  static pw.Widget _buildReceiptSummary(
    pw.Font ttf,
    double totalReceipt,
    double totalCost,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        color: PdfColors.green50,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص استلام المواد الخام',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي الاستلام',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '${totalReceipt.toStringAsFixed(2)} كجم',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي التكلفة',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '${totalCost.toStringAsFixed(2)} جنيه',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء ملخص المخزون
  static pw.Widget _buildInventorySummary(
    pw.Font ttf,
    int totalItems,
    double totalValue,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        color: PdfColors.blue50,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص مخزون المواد الخام',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              pw.Column(
                children: [
                  pw.Text(
                    'عدد المواد الخام',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    totalItems.toString(),
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Column(
                children: [
                  pw.Text(
                    'القيمة الإجمالية للمخزون',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    '${totalValue.toStringAsFixed(2)} جنيه',
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 18,
                      fontWeight: pw.FontWeight.bold,
                      color: PdfColors.blue900,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء جدول الاستهلاك
  static pw.Widget _buildConsumptionTable(
    pw.Font ttf,
    List<Map<String, dynamic>> consumptionData,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(ttf, 'المادة الخام', isHeader: true),
            _buildTableCell(ttf, 'الكود', isHeader: true),
            _buildTableCell(ttf, 'الكمية المستهلكة (كجم)', isHeader: true),
            _buildTableCell(ttf, 'التكلفة (جنيه)', isHeader: true),
          ],
        ),
        // بيانات الجدول
        ...consumptionData.map((item) {
          final double totalOut = item['totalOut'] ?? 0.0;
          final double totalCost = item['totalCost'] ?? 0.0;

          return pw.TableRow(
            children: [
              _buildTableCell(ttf, item['itemName'] ?? 'غير معروف'),
              _buildTableCell(ttf, item['itemCode'] ?? '-'),
              _buildTableCell(
                ttf,
                totalOut.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                totalCost.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
            ],
          );
        }),
      ],
    );
  }

  // بناء جدول الاستلام
  static pw.Widget _buildReceiptTable(
    pw.Font ttf,
    List<Map<String, dynamic>> receiptData,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(ttf, 'المادة الخام', isHeader: true),
            _buildTableCell(ttf, 'التاريخ', isHeader: true),
            _buildTableCell(ttf, 'الكمية (كجم)', isHeader: true),
            _buildTableCell(ttf, 'سعر الكيلوجرام (جنيه)', isHeader: true),
            _buildTableCell(ttf, 'الإجمالي (جنيه)', isHeader: true),
          ],
        ),
        // بيانات الجدول
        ...receiptData.map((item) {
          final double quantity = item['quantity'] ?? 0.0;
          final double pricePerKg = item['pricePerKg'] ?? 0.0;
          final double totalCost = quantity * pricePerKg;
          final DateTime date =
              item['date'] != null
                  ? DateTime.parse(item['date'])
                  : DateTime.now();

          return pw.TableRow(
            children: [
              _buildTableCell(ttf, item['materialName'] ?? 'غير معروف'),
              _buildTableCell(
                ttf,
                DateFormat('yyyy/MM/dd').format(date),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                quantity.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                pricePerKg.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                totalCost.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
            ],
          );
        }),
      ],
    );
  }

  // بناء جدول المخزون
  static pw.Widget _buildInventoryTable(
    pw.Font ttf,
    List<Map<String, dynamic>> inventoryData,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(ttf, 'المادة الخام', isHeader: true),
            _buildTableCell(ttf, 'الكود', isHeader: true),
            _buildTableCell(ttf, 'اللون', isHeader: true),
            _buildTableCell(ttf, 'الكمية المتاحة (كجم)', isHeader: true),
            _buildTableCell(ttf, 'تكلفة الكيلوجرام (جنيه)', isHeader: true),
            _buildTableCell(ttf, 'القيمة الإجمالية (جنيه)', isHeader: true),
          ],
        ),
        // بيانات الجدول
        ...inventoryData.map((item) {
          final double quantity = item['availableQuantity'] ?? 0.0;
          final double costPerKg = item['costPerKg'] ?? 0.0;
          final double totalValue = quantity * costPerKg;

          return pw.TableRow(
            children: [
              _buildTableCell(ttf, item['name'] ?? 'غير معروف'),
              _buildTableCell(ttf, item['code'] ?? '-'),
              _buildTableCell(ttf, item['color'] ?? '-'),
              _buildTableCell(
                ttf,
                quantity.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                costPerKg.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                totalValue.toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
            ],
          );
        }),
      ],
    );
  }

  // بناء ترويسة التقرير
  static pw.Widget _buildHeader(pw.Font ttf, String title, DateTime date) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Row(
          mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          children: [
            pw.Text(
              title,
              style: pw.TextStyle(
                font: ttf,
                fontSize: 24,
                fontWeight: pw.FontWeight.bold,
              ),
            ),
            pw.Text(
              DateFormat('yyyy/MM/dd').format(date),
              style: pw.TextStyle(font: ttf, fontSize: 14),
            ),
          ],
        ),
        pw.Divider(thickness: 2),
        pw.SizedBox(height: 10),
      ],
    );
  }

  // بناء ملخص التقرير
  static pw.Widget _buildSummary(
    pw.Font ttf,
    List<Map<String, dynamic>> productionItems,
  ) {
    // حساب إجمالي القطع المنتجة
    final totalParts = productionItems.fold<int>(
      0,
      (sum, item) => sum + (item['partsProduced'] as int),
    );

    // حساب إجمالي الخامات المستخدمة
    final totalMaterials = productionItems.fold<double>(
      0,
      (sum, item) => sum + (item['rawMaterialUsed'] as double),
    );

    return pw.Container(
      padding: const pw.EdgeInsets.all(10),
      decoration: const pw.BoxDecoration(
        color: PdfColors.grey100,
        borderRadius: pw.BorderRadius.all(pw.Radius.circular(5)),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الإنتاج',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
            ),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
            children: [
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي القطع المنتجة',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    totalParts.toString(),
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
              pw.Column(
                children: [
                  pw.Text(
                    'إجمالي الخامات المستخدمة (كجم)',
                    style: pw.TextStyle(font: ttf, fontSize: 12),
                  ),
                  pw.SizedBox(height: 5),
                  pw.Text(
                    totalMaterials.toStringAsFixed(2),
                    style: pw.TextStyle(
                      font: ttf,
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  // بناء جدول الإنتاج
  static pw.Widget _buildProductionTable(
    pw.Font ttf,
    List<Map<String, dynamic>> productionItems,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(),
      children: [
        // رأس الجدول
        pw.TableRow(
          decoration: const pw.BoxDecoration(color: PdfColors.grey200),
          children: [
            _buildTableCell(ttf, 'الماكينة', isHeader: true),
            _buildTableCell(ttf, 'الإسطمبة', isHeader: true),
            _buildTableCell(ttf, 'المادة الخام', isHeader: true),
            _buildTableCell(ttf, 'القطع', isHeader: true),
            _buildTableCell(ttf, 'الخامات (كجم)', isHeader: true),
            _buildTableCell(ttf, 'وقت الدورة (ثانية)', isHeader: true),
          ],
        ),
        // بيانات الجدول
        ...productionItems.map((item) {
          return pw.TableRow(
            children: [
              _buildTableCell(ttf, item['machineName'] ?? 'غير معروف'),
              _buildTableCell(ttf, item['moldName'] ?? 'غير معروف'),
              _buildTableCell(ttf, item['rawMaterialName'] ?? 'غير معروف'),
              _buildTableCell(
                ttf,
                (item['partsProduced'] as int).toString(),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                (item['rawMaterialUsed'] as double).toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
              _buildTableCell(
                ttf,
                (item['cycleTime'] as double).toStringAsFixed(2),
                alignment: pw.Alignment.center,
              ),
            ],
          );
        }),
      ],
    );
  }

  // بناء خلية الجدول
  static pw.Widget _buildTableCell(
    pw.Font ttf,
    String text, {
    bool isHeader = false,
    pw.Alignment alignment = pw.Alignment.centerRight,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(5),
      alignment: alignment,
      height: 30,
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : null,
        ),
      ),
    );
  }

  // طباعة الملف
  static Future<void> printPdf(File file) async {
    await Printing.layoutPdf(onLayout: (_) => file.readAsBytes());
  }

  // فتح الملف
  static Future<void> openPdf(File file) async {
    await Printing.layoutPdf(onLayout: (_) => file.readAsBytes());
  }

  // مشاركة الملف
  static Future<void> sharePdf(File file) async {
    await Printing.sharePdf(
      bytes: await file.readAsBytes(),
      filename: file.path.split('/').last,
    );
  }
}
