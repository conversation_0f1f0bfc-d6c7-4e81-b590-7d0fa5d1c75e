import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'raw_materials_controller.dart';

class RawMaterialsPage extends StatelessWidget {
  const RawMaterialsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final RawMaterialsController controller = Get.put(RawMaterialsController());

    return ResponsivePage(
      title: 'إدارة المواد الخام',
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CircularProgressIndicator(
              strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              SizedBox(height: ScreenSize.getPadding(16)),

              // شريط البحث
              TextField(
                controller: controller.searchController,
                decoration: InputDecoration(
                  hintText: 'البحث عن مادة خام...',
                  prefixIcon: Icon(
                    Icons.search,
                    size: ScreenSize.isSmallScreen ? 20 : 24,
                  ),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(
                      ScreenSize.getPadding(8),
                    ),
                  ),
                  contentPadding: EdgeInsets.symmetric(
                    vertical: ScreenSize.getPadding(12),
                    horizontal: ScreenSize.getPadding(16),
                  ),
                ),
                onChanged: controller.onSearchTextChanged,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              Expanded(child: _buildRawMaterialsList(controller)),
            ],
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddMaterialDialog(context, controller),
        tooltip: 'إضافة مادة خام جديدة',
        child: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 20 : 24),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'المواد الخام',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(8)),
        TextUtils.responsiveText(
          'إدارة مخزون المواد الخام وإضافة مواد جديدة',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
            color: Colors.grey.shade700,
          ),
          maxLines: 2,
        ),
      ],
    );
  }

  Widget _buildRawMaterialsList(RawMaterialsController controller) {
    if (controller.filteredMaterials.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.category_outlined,
              size: ScreenSize.isSmallScreen ? 60 : 80,
              color: Colors.grey.shade400,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            TextUtils.responsiveText(
              controller.searchController.text.isEmpty
                  ? 'لا توجد مواد خام مضافة'
                  : 'لا توجد نتائج بحث',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                color: Colors.grey.shade600,
              ),
              maxLines: 2,
              textAlign: TextAlign.center,
            ),
            if (controller.searchController.text.isEmpty) ...[
              SizedBox(height: ScreenSize.getPadding(16)),
              ElevatedButton.icon(
                onPressed:
                    () => _showAddMaterialDialog(Get.context!, controller),
                icon: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 18 : 24),
                label: TextUtils.responsiveText(
                  'إضافة مادة خام جديدة',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(4)),
      itemCount: controller.filteredMaterials.length,
      itemBuilder: (context, index) {
        final material = controller.filteredMaterials[index];
        return _buildMaterialCard(context, material, controller);
      },
    );
  }

  Widget _buildMaterialCard(
    BuildContext context,
    RawMaterial material,
    RawMaterialsController controller,
  ) {
    return Card(
      margin: EdgeInsets.only(bottom: ScreenSize.getPadding(12)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextUtils.responsiveText(
                        material.name,
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(4)),
                      TextUtils.responsiveText(
                        'كود: ${material.code}',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          color: Colors.grey.shade700,
                        ),
                        maxLines: 1,
                      ),
                    ],
                  ),
                ),
                // دائرة لون المادة
                Container(
                  width: ScreenSize.isSmallScreen ? 24 : 32,
                  height: ScreenSize.isSmallScreen ? 24 : 32,
                  decoration: BoxDecoration(
                    color: _parseColor(material.color),
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                ),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Divider(height: ScreenSize.getPadding(1)),
            SizedBox(height: ScreenSize.getPadding(8)),
            ScreenSize.isSmallScreen
                ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildQuantityInfo(material),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    _buildCostInfo(material),
                  ],
                )
                : Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    _buildQuantityInfo(material),
                    _buildCostInfo(material),
                  ],
                ),
            SizedBox(height: ScreenSize.getPadding(16)),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  OutlinedButton.icon(
                    onPressed:
                        () => _showAddReceiptDialog(
                          context,
                          material,
                          controller,
                        ),
                    icon: Icon(
                      Icons.add_circle_outline,
                      size: ScreenSize.isSmallScreen ? 16 : 20,
                    ),
                    label: TextUtils.responsiveText(
                      'إضافة كمية',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                  ),
                  SizedBox(width: ScreenSize.getPadding(8)),
                  OutlinedButton.icon(
                    onPressed:
                        () => _showMaterialHistoryDialog(context, material),
                    icon: Icon(
                      Icons.history,
                      size: ScreenSize.isSmallScreen ? 16 : 20,
                    ),
                    label: TextUtils.responsiveText(
                      'السجل',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                    style: OutlinedButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                  ),
                  SizedBox(width: ScreenSize.getPadding(8)),
                  TextButton.icon(
                    onPressed:
                        () => _showEditMaterialDialog(
                          context,
                          material,
                          controller,
                        ),
                    icon: Icon(
                      Icons.edit,
                      size: ScreenSize.isSmallScreen ? 16 : 20,
                    ),
                    label: TextUtils.responsiveText(
                      'تعديل',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // معلومات الكمية المتاحة
  Widget _buildQuantityInfo(RawMaterial material) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'الكمية المتاحة:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(4)),
        TextUtils.responsiveText(
          '${material.availableQuantity.toStringAsFixed(2)} كجم',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
            color: _getQuantityColor(material.availableQuantity),
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  // معلومات التكلفة
  Widget _buildCostInfo(RawMaterial material) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'التكلفة للكيلوجرام:',
          style: TextStyle(
            fontWeight: FontWeight.w500,
            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(4)),
        TextUtils.responsiveText(
          '${material.costPerKg.toStringAsFixed(2)} جنيه',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  // تحويل اللون من سلسلة نصية إلى كائن Color
  Color _parseColor(String colorString) {
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse('0xFF${colorString.substring(1)}'));
      } else {
        // ألوان افتراضية معروفة
        switch (colorString.toLowerCase()) {
          case 'red':
            return Colors.red;
          case 'green':
            return Colors.green;
          case 'blue':
            return Colors.blue;
          case 'yellow':
            return Colors.yellow;
          case 'black':
            return Colors.black;
          case 'white':
            return Colors.white;
          case 'grey':
          case 'gray':
            return Colors.grey;
          default:
            return Colors.grey; // لون افتراضي
        }
      }
    } catch (e) {
      return Colors.grey; // لون افتراضي في حالة حدوث خطأ
    }
  }

  // تحديد لون الكمية المتاحة
  Color _getQuantityColor(double quantity) {
    if (quantity <= 10) {
      return Colors.red;
    } else if (quantity <= 50) {
      return Colors.orange;
    } else {
      return Colors.green;
    }
  }

  // عرض مربع حوار إضافة مادة خام جديدة
  void _showAddMaterialDialog(
    BuildContext context,
    RawMaterialsController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    controller.resetMaterialForm();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'إضافة مادة خام جديدة',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(24),
              vertical: ScreenSize.getPadding(16),
            ),
            content: Form(
              key: controller.formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: controller.nameController,
                      decoration: InputDecoration(
                        labelText: 'اسم المادة الخام',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.codeController,
                      decoration: InputDecoration(
                        labelText: 'كود المادة الخام',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كود المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.colorController,
                      decoration: InputDecoration(
                        labelText: 'اللون',
                        border: OutlineInputBorder(),
                        hintText: 'مثال: أحمر، أزرق، أخضر، #RRGGBB',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال لون المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية الأولية (كجم)',
                        border: OutlineInputBorder(),
                        suffixText: 'كجم',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الكمية الأولية';
                        }

                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }

                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.costPerKgController,
                      decoration: InputDecoration(
                        labelText: 'التكلفة للكيلوجرام (جنيه)',
                        border: OutlineInputBorder(),
                        suffixText: 'جنيه',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال التكلفة للكيلوجرام';
                        }

                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }

                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (controller.formKey.currentState!.validate()) {
                    controller.addRawMaterial();
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إضافة',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }

  // عرض مربع حوار تعديل مادة خام
  void _showEditMaterialDialog(
    BuildContext context,
    RawMaterial material,
    RawMaterialsController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    controller.setupEditForm(material);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'تعديل مادة خام',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(24),
              vertical: ScreenSize.getPadding(16),
            ),
            content: Form(
              key: controller.formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: controller.nameController,
                      decoration: InputDecoration(
                        labelText: 'اسم المادة الخام',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال اسم المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.codeController,
                      decoration: InputDecoration(
                        labelText: 'كود المادة الخام',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال كود المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.colorController,
                      decoration: InputDecoration(
                        labelText: 'اللون',
                        border: OutlineInputBorder(),
                        hintText: 'مثال: أحمر، أزرق، أخضر، #RRGGBB',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال لون المادة الخام';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      enabled: false, // لا يمكن تعديل الكمية المتاحة مباشرة
                      controller: controller.quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية المتاحة (كجم)',
                        border: OutlineInputBorder(),
                        suffixText: 'كجم',
                        helperText:
                            'يمكن تعديل الكمية عن طريق إضافة كمية جديدة',
                        helperStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                        ),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextFormField(
                      controller: controller.costPerKgController,
                      decoration: InputDecoration(
                        labelText: 'التكلفة للكيلوجرام (جنيه)',
                        border: OutlineInputBorder(),
                        suffixText: 'جنيه',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال التكلفة للكيلوجرام';
                        }

                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }

                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (controller.formKey.currentState!.validate()) {
                    controller.updateRawMaterial(material.id!);
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'تحديث',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }

  // عرض مربع حوار إضافة كمية جديدة
  void _showAddReceiptDialog(
    BuildContext context,
    RawMaterial material,
    RawMaterialsController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    controller.resetReceiptForm();

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'إضافة كمية جديدة',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(24),
              vertical: ScreenSize.getPadding(16),
            ),
            content: Form(
              key: controller.receiptFormKey,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextUtils.responsiveText(
                    'المادة الخام: ${material.name}',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  TextFormField(
                    controller: controller.newQuantityController,
                    decoration: InputDecoration(
                      labelText: 'الكمية (كجم)',
                      border: OutlineInputBorder(),
                      suffixText: 'كجم',
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(16),
                        vertical: ScreenSize.getPadding(12),
                      ),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال الكمية';
                      }

                      final double? quantity = double.tryParse(value);
                      if (quantity == null || quantity <= 0) {
                        return 'يرجى إدخال قيمة أكبر من الصفر';
                      }

                      return null;
                    },
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                  ),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  TextFormField(
                    controller: controller.newPriceController,
                    decoration: InputDecoration(
                      labelText: 'السعر للكيلوجرام (جنيه)',
                      border: OutlineInputBorder(),
                      suffixText: 'جنيه',
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(16),
                        vertical: ScreenSize.getPadding(12),
                      ),
                    ),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'يرجى إدخال السعر للكيلوجرام';
                      }

                      final double? price = double.tryParse(value);
                      if (price == null || price <= 0) {
                        return 'يرجى إدخال قيمة أكبر من الصفر';
                      }

                      return null;
                    },
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
              ElevatedButton(
                onPressed: () {
                  if (controller.receiptFormKey.currentState!.validate()) {
                    controller.addRawMaterialReceipt(material.id!);
                    Navigator.pop(context);
                  }
                },
                style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إضافة',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }

  // عرض سجل المادة الخام
  void _showMaterialHistoryDialog(BuildContext context, RawMaterial material) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    // تنفيذ عرض السجل
    Get.find<RawMaterialsController>().loadMaterialReceipts(material.id!);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'سجل استلام ${material.name}',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(16),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Obx(() {
                final controller = Get.find<RawMaterialsController>();

                if (controller.isLoadingReceipts.value) {
                  return SizedBox(
                    height: ScreenSize.isSmallScreen ? 150 : 200,
                    child: Center(
                      child: CircularProgressIndicator(
                        strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
                      ),
                    ),
                  );
                }

                if (controller.materialReceipts.isEmpty) {
                  return SizedBox(
                    height: ScreenSize.isSmallScreen ? 150 : 200,
                    child: Center(
                      child: TextUtils.responsiveText(
                        'لا يوجد سجل استلام لهذه المادة',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 2,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }

                return SizedBox(
                  height: ScreenSize.isSmallScreen ? 250 : 300,
                  child: ListView.builder(
                    itemCount: controller.materialReceipts.length,
                    itemBuilder: (context, index) {
                      final receipt = controller.materialReceipts[index];

                      return ListTile(
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(4),
                        ),
                        title: TextUtils.responsiveText(
                          'كمية: ${receipt.quantity.toStringAsFixed(2)} كجم',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                          maxLines: 1,
                        ),
                        subtitle: TextUtils.responsiveText(
                          'السعر: ${receipt.pricePerKg.toStringAsFixed(2)} جنيه/كجم',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                          ),
                          maxLines: 1,
                        ),
                        trailing: TextUtils.responsiveText(
                          controller.formatDate(receipt.date),
                          style: TextStyle(
                            color: Colors.grey.shade600,
                            fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                          ),
                          maxLines: 1,
                        ),
                      );
                    },
                  ),
                );
              }),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إغلاق',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }
}
