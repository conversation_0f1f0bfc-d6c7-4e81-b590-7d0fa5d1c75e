import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Worker;
import 'package:mostafa_final/data/models/assembly.dart';
import 'package:mostafa_final/data/models/worker.dart';
import 'package:mostafa_final/presentation/controllers/assembly_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/utils/format_utils.dart';
import 'package:intl/intl.dart';

class AssemblyPage extends StatelessWidget {
  const AssemblyPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AssemblyController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة التجميع'),
        actions: [
          IconButton(
            icon: const Icon(Icons.bar_chart),
            tooltip: 'تقارير التجميع',
            onPressed: () => Get.toNamed('/assembly-report'),
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchInitialData(),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CustomLoadingIndicator());
        }

        return Column(
          children: [
            // بطاقة الإحصائيات
            _buildStatisticsCard(controller),

            // قسم إضافة تجميع جديد
            ExpansionTile(
              title: const Text(
                'إضافة عملية تجميع جديدة',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              leading: const Icon(Icons.add_circle),
              children: [_buildAssemblyForm(controller, context)],
            ),

            // عنوان قائمة التجميعات
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: Text(
                'عمليات التجميع الحالية',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ),

            // قائمة عمليات التجميع
            Expanded(
              child:
                  controller.assemblies.isEmpty
                      ? const Center(child: Text('لا توجد عمليات تجميع مسجلة'))
                      : ListView.builder(
                        itemCount: controller.assemblies.length,
                        itemBuilder: (context, index) {
                          final assembly = controller.assemblies[index];
                          return _buildAssemblyItem(
                            context,
                            assembly,
                            controller,
                          );
                        },
                      ),
            ),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showDatePicker(context, controller);
        },
        tooltip: 'تصفية حسب التاريخ',
        child: const Icon(Icons.calendar_month),
      ),
    );
  }

  // بطاقة الإحصائيات
  Widget _buildStatisticsCard(AssemblyController controller) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات التجميع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'إجمالي القطع المجمعة',
                  controller.totalAssembledUnits.toString(),
                  Icons.inventory,
                ),
                _buildStatItem(
                  'عمليات التجميع',
                  controller.totalAssemblyOperations.toString(),
                  Icons.engineering,
                ),
                _buildStatItem(
                  'متوسط التكلفة',
                  FormatUtils.formatCurrency(controller.averageUnitCost.value),
                  Icons.attach_money,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // عنصر إحصائية واحد
  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 30, color: Colors.blue),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  // نموذج إضافة تجميع جديد
  Widget _buildAssemblyForm(
    AssemblyController controller,
    BuildContext context,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // اختيار التاريخ
          Row(
            children: [
              const Icon(Icons.calendar_today),
              const SizedBox(width: 8),
              const Text('تاريخ التجميع:'),
              const SizedBox(width: 16),
              Obx(() {
                return TextButton(
                  onPressed: () async {
                    final selectedDate = await showDatePicker(
                      context: context,
                      initialDate: controller.selectedDate.value,
                      firstDate: DateTime(2020),
                      lastDate: DateTime(2030),
                    );
                    if (selectedDate != null) {
                      controller.selectedDate.value = selectedDate;
                    }
                  },
                  child: Text(
                    controller.formatDate(controller.selectedDate.value),
                  ),
                );
              }),
            ],
          ),

          const SizedBox(height: 16),

          // اختيار العامل
          const Text('العامل المسؤول:'),
          Obx(() {
            return DropdownButton<Worker>(
              isExpanded: true,
              value: controller.selectedWorker.value,
              hint: const Text('اختر العامل'),
              items:
                  controller.workers.map((worker) {
                    return DropdownMenuItem<Worker>(
                      value: worker,
                      child: Text('${worker.name} (${worker.code})'),
                    );
                  }).toList(),
              onChanged: (worker) {
                if (worker != null) {
                  controller.selectedWorker.value = worker;
                  controller.calculateCosts();
                }
              },
            );
          }),

          const SizedBox(height: 16),

          // اختيار المنتج
          const Text('المنتج:'),
          Obx(() {
            return DropdownButton(
              isExpanded: true,
              value: controller.selectedProduct.value,
              hint: const Text('اختر المنتج'),
              items:
                  controller.products.map((product) {
                    return DropdownMenuItem(
                      value: product,
                      child: Text('${product.name} (${product.code})'),
                    );
                  }).toList(),
              onChanged: (product) {
                if (product != null) {
                  controller.selectProduct(product);
                }
              },
            );
          }),

          const SizedBox(height: 16),

          // تحديد الكمية
          Row(
            children: [
              const Text('الكمية:'),
              const SizedBox(width: 16),
              IconButton(
                icon: const Icon(Icons.remove_circle),
                onPressed: () {
                  if (controller.quantity.value > 1) {
                    controller.changeQuantity(controller.quantity.value - 1);
                  }
                },
              ),
              Obx(
                () => Text(
                  controller.quantity.value.toString(),
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              IconButton(
                icon: const Icon(Icons.add_circle),
                onPressed: () {
                  controller.changeQuantity(controller.quantity.value + 1);
                },
              ),
            ],
          ),

          const SizedBox(height: 24),

          // عرض قائمة المكونات المستخدمة
          const Text(
            'المكونات المستخدمة:',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Obx(() {
            if (controller.componentsUsed.isEmpty) {
              return const Padding(
                padding: EdgeInsets.all(8.0),
                child: Text('لم يتم اختيار مكونات بعد'),
              );
            }

            return Column(
              children:
                  controller.componentsUsed.map((component) {
                    final componentType =
                        component.componentType == 0
                            ? 'قطعة بلاستيك'
                            : 'إكسسوار';
                    return ListTile(
                      title: Text(component.name),
                      subtitle: Text(
                        'النوع: $componentType | العدد: ${component.quantity}',
                      ),
                      trailing: Text(
                        FormatUtils.formatCurrency(component.cost),
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    );
                  }).toList(),
            );
          }),

          const SizedBox(height: 24),

          // عرض التكاليف
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'تفاصيل التكلفة:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('تكلفة المكونات:'),
                      Obx(
                        () => Text(
                          FormatUtils.formatCurrency(
                            controller.componentsCost.value,
                          ),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('تكلفة العمالة:'),
                      Obx(
                        () => Text(
                          FormatUtils.formatCurrency(
                            controller.laborCost.value,
                          ),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text('تكلفة التغليف:'),
                      Obx(
                        () => Text(
                          FormatUtils.formatCurrency(
                            controller.packagingCost.value,
                          ),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                    ],
                  ),
                  const Divider(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const Text(
                        'التكلفة الإجمالية:',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Obx(
                        () => Text(
                          FormatUtils.formatCurrency(
                            controller.totalCost.value,
                          ),
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: Colors.blue,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 24),

          // زر الحفظ
          Center(
            child: Obx(() {
              return ElevatedButton.icon(
                onPressed:
                    controller.isSaving.value
                        ? null
                        : () async {
                          final success = await controller.saveAssembly();
                          if (success) {
                            // إخفاء التقسيم المتوسع بعد الحفظ
                            Future.delayed(const Duration(milliseconds: 300), () {
                              // التحقق من أن الـ widget لا يزال موجودًا في الشجرة قبل استخدام context
                              if (context.mounted) {
                                FocusScope.of(context).unfocus();
                              }
                            });
                          }
                        },
                icon:
                    controller.isSaving.value
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: Colors.white,
                          ),
                        )
                        : const Icon(Icons.save),
                label: Text(
                  controller.isSaving.value ? 'جاري الحفظ...' : 'حفظ التجميع',
                ),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                ),
              );
            }),
          ),
        ],
      ),
    );
  }

  // عنصر عملية تجميع في القائمة
  Widget _buildAssemblyItem(
    BuildContext context,
    Assembly assembly,
    AssemblyController controller,
  ) {
    // البحث عن اسم المنتج
    final product = controller.products.firstWhere(
      (p) => p.id == assembly.productId,
      orElse: () => controller.products.first,
    );

    // البحث عن اسم العامل
    final worker = controller.workers.firstWhere(
      (w) => w.id == assembly.workerId,
      orElse:
          () => Worker(
            name: 'غير معروف',
            code: '---',
            jobTitle: '---',
            hourlyRate: 0,
            department: WorkerDepartment.assembly,
            isActive: true,
          ),
    );

    // اللون حسب الحالة
    Color statusColor;
    switch (assembly.status) {
      case AssemblyStatus.completed:
        statusColor = Colors.green;
        break;
      case AssemblyStatus.defective:
        statusColor = Colors.red;
        break;
      case AssemblyStatus.inProgress:
        statusColor = Colors.orange;
        break;
      case AssemblyStatus.canceled:
        statusColor = Colors.grey;
        break;
      default:
        statusColor = Colors.blue;
    }

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ExpansionTile(
        title: Text(
          product.name,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(
          'الكمية: ${assembly.quantity} | التاريخ: ${DateFormat('yyyy-MM-dd').format(assembly.date)}',
        ),
        leading: const CircleAvatar(child: Icon(Icons.inventory)),
        trailing: Chip(
          label: Text(
            assembly.status,
            style: const TextStyle(color: Colors.white),
          ),
          backgroundColor: statusColor,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // معلومات العامل
                Row(
                  children: [
                    const Icon(Icons.person, size: 18),
                    const SizedBox(width: 8),
                    const Text('العامل:'),
                    const SizedBox(width: 8),
                    Text(
                      worker.name,
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // تفاصيل التكاليف
                Row(
                  children: [
                    const Icon(Icons.attach_money, size: 18),
                    const SizedBox(width: 8),
                    const Text('تكلفة التجميع:'),
                    const SizedBox(width: 8),
                    Text(
                      FormatUtils.formatCurrency(assembly.totalAssemblyCost),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.inventory_2, size: 18),
                    const SizedBox(width: 8),
                    const Text('تكلفة التغليف:'),
                    const SizedBox(width: 8),
                    Text(
                      FormatUtils.formatCurrency(assembly.totalPackagingCost),
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.calculate, size: 18),
                    const SizedBox(width: 8),
                    const Text('التكلفة الإجمالية:'),
                    const SizedBox(width: 8),
                    Text(
                      FormatUtils.formatCurrency(
                        assembly.totalAssemblyCost +
                            assembly.totalPackagingCost,
                      ),
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),

                // المكونات المستخدمة
                const Text(
                  'المكونات المستخدمة:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Column(
                  children:
                      assembly.componentsUsed.map((component) {
                        final componentType =
                            component.componentType == 0
                                ? 'قطعة بلاستيك'
                                : 'إكسسوار';
                        return Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Text(
                                  '${component.name} ($componentType)',
                                ),
                              ),
                              Text('العدد: ${component.quantity}'),
                              const SizedBox(width: 16),
                              Text(FormatUtils.formatCurrency(component.cost)),
                            ],
                          ),
                        );
                      }).toList(),
                ),
                const SizedBox(height: 16),

                // أزرار الإجراءات
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // تغيير الحالة
                    ElevatedButton.icon(
                      onPressed: () {
                        _showStatusChangeDialog(context, assembly, controller);
                      },
                      icon: const Icon(Icons.edit_note),
                      label: const Text('تغيير الحالة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.amber,
                      ),
                    ),
                    // حذف
                    ElevatedButton.icon(
                      onPressed: () {
                        _showDeleteConfirmationDialog(
                          context,
                          assembly,
                          controller,
                        );
                      },
                      icon: const Icon(Icons.delete),
                      label: const Text('حذف'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    Assembly assembly,
    AssemblyController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text(
              'هل أنت متأكد من حذف هذه العملية؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () async {
                  Navigator.pop(context);
                  await controller.deleteAssembly(assembly.id!);
                },
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }

  // حوار تغيير الحالة
  void _showStatusChangeDialog(
    BuildContext context,
    Assembly assembly,
    AssemblyController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تغيير حالة عملية التجميع'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildStatusOption(
                  context,
                  AssemblyStatus.completed,
                  'مكتمل',
                  Colors.green,
                  assembly,
                  controller,
                ),
                _buildStatusOption(
                  context,
                  AssemblyStatus.inProgress,
                  'قيد التنفيذ',
                  Colors.orange,
                  assembly,
                  controller,
                ),
                _buildStatusOption(
                  context,
                  AssemblyStatus.defective,
                  'معيب',
                  Colors.red,
                  assembly,
                  controller,
                ),
                _buildStatusOption(
                  context,
                  AssemblyStatus.canceled,
                  'ملغي',
                  Colors.grey,
                  assembly,
                  controller,
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
            ],
          ),
    );
  }

  // خيار واحد من خيارات الحالة
  Widget _buildStatusOption(
    BuildContext context,
    String status,
    String label,
    Color color,
    Assembly assembly,
    AssemblyController controller,
  ) {
    return ListTile(
      title: Text(label),
      leading: CircleAvatar(backgroundColor: color, radius: 12),
      onTap: () async {
        Navigator.pop(context);
        await controller.updateAssemblyStatus(assembly.id!, status);
      },
      selected: assembly.status == status,
    );
  }

  // حوار اختيار التاريخ للتصفية
  void _showDatePicker(
    BuildContext context,
    AssemblyController controller,
  ) async {
    final pickedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
    );

    if (pickedDate != null) {
      controller.fetchAssembliesByDate(pickedDate);
    }
  }
}
