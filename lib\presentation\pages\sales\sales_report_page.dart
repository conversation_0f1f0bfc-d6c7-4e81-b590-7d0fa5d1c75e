import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/presentation/controllers/sales_controller.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'package:mostafa_final/utils/format_utils.dart';
import 'package:fl_chart/fl_chart.dart';

// ignore_for_file: prefer_const_constructors, unnecessary_const, deprecated_member_use

class SalesReportPage extends StatelessWidget {
  const SalesReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final controller = Get.put(SalesController());

    return ResponsivePage(
      title: 'تقارير المبيعات',
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => controller.fetchSalesReportData(),
        ),
        IconButton(
          icon: Icon(Icons.print, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => controller.printSalesReport(),
        ),
      ],
      body: Obx(() {
        if (controller.isLoadingReports.value) {
          return Center(
            child: CustomLoadingIndicator(
              size: ScreenSize.isSmallScreen ? 40 : 50,
            ),
          );
        }

        return SingleChildScrollView(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateRangePicker(context, controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildSalesSummaryCards(controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildSalesChart(controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildTopSellingProductsTable(controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildSalesStatusDistribution(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildDateRangePicker(
    BuildContext context,
    SalesController controller,
  ) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(4)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'نطاق التقرير',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),

            // في الشاشات الصغيرة، نعرض حقول التاريخ بشكل عمودي
            ScreenSize.isSmallScreen
                ? Column(
                  children: [
                    InkWell(
                      onTap: () => _selectStartDate(context, controller),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'من تاريخ',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(
                            Icons.calendar_today,
                            size: ScreenSize.isSmallScreen ? 20 : 24,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: ScreenSize.getPadding(12),
                            vertical: ScreenSize.getPadding(8),
                          ),
                          labelStyle: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                        ),
                        child: TextUtils.responsiveText(
                          DateFormat(
                            'yyyy-MM-dd',
                          ).format(controller.reportStartDate.value),
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    InkWell(
                      onTap: () => _selectEndDate(context, controller),
                      child: InputDecorator(
                        decoration: InputDecoration(
                          labelText: 'إلى تاريخ',
                          border: OutlineInputBorder(),
                          suffixIcon: Icon(
                            Icons.calendar_today,
                            size: ScreenSize.isSmallScreen ? 20 : 24,
                          ),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: ScreenSize.getPadding(12),
                            vertical: ScreenSize.getPadding(8),
                          ),
                          labelStyle: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                        ),
                        child: TextUtils.responsiveText(
                          DateFormat(
                            'yyyy-MM-dd',
                          ).format(controller.reportEndDate.value),
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () => controller.fetchSalesReportData(),
                        icon: Icon(
                          Icons.search,
                          size: ScreenSize.isSmallScreen ? 18 : 24,
                        ),
                        label: TextUtils.responsiveText(
                          'تطبيق',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                          maxLines: 1,
                        ),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            vertical: ScreenSize.getPadding(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                )
                // في الشاشات الكبيرة، نعرض حقول التاريخ بشكل أفقي
                : Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectStartDate(context, controller),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'من تاريخ',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(
                              Icons.calendar_today,
                              size: ScreenSize.isSmallScreen ? 20 : 24,
                            ),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(12),
                              vertical: ScreenSize.getPadding(8),
                            ),
                            labelStyle: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                          ),
                          child: TextUtils.responsiveText(
                            DateFormat(
                              'yyyy-MM-dd',
                            ).format(controller.reportStartDate.value),
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: ScreenSize.getPadding(16)),
                    Expanded(
                      child: InkWell(
                        onTap: () => _selectEndDate(context, controller),
                        child: InputDecorator(
                          decoration: InputDecoration(
                            labelText: 'إلى تاريخ',
                            border: OutlineInputBorder(),
                            suffixIcon: Icon(
                              Icons.calendar_today,
                              size: ScreenSize.isSmallScreen ? 20 : 24,
                            ),
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(12),
                              vertical: ScreenSize.getPadding(8),
                            ),
                            labelStyle: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                          ),
                          child: TextUtils.responsiveText(
                            DateFormat(
                              'yyyy-MM-dd',
                            ).format(controller.reportEndDate.value),
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: ScreenSize.getPadding(16)),
                    ElevatedButton.icon(
                      onPressed: () => controller.fetchSalesReportData(),
                      icon: Icon(
                        Icons.search,
                        size: ScreenSize.isSmallScreen ? 18 : 24,
                      ),
                      label: TextUtils.responsiveText(
                        'تطبيق',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                    ),
                  ],
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesSummaryCards(SalesController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'ملخص المبيعات',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(16)),

        // في الشاشات الصغيرة، نعرض البطاقات بشكل عمودي
        ScreenSize.isSmallScreen
            ? Column(
              children: [
                _buildSummaryCard(
                  'إجمالي المبيعات',
                  FormatUtils.formatCurrency(controller.totalSalesAmount.value),
                  Icons.monetization_on,
                  Colors.green,
                ),
                _buildSummaryCard(
                  'عدد الطلبات',
                  controller.totalSalesCount.value.toString(),
                  Icons.shopping_bag,
                  Colors.blue,
                ),
                _buildSummaryCard(
                  'متوسط قيمة الطلب',
                  FormatUtils.formatCurrency(
                    controller.averageOrderValue.value,
                  ),
                  Icons.trending_up,
                  Colors.orange,
                ),
              ],
            )
            // في الشاشات الكبيرة، نعرض البطاقات بشكل أفقي
            : Row(
              children: [
                Expanded(
                  child: _buildSummaryCard(
                    'إجمالي المبيعات',
                    FormatUtils.formatCurrency(
                      controller.totalSalesAmount.value,
                    ),
                    Icons.monetization_on,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildSummaryCard(
                    'عدد الطلبات',
                    controller.totalSalesCount.value.toString(),
                    Icons.shopping_bag,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildSummaryCard(
                    'متوسط قيمة الطلب',
                    FormatUtils.formatCurrency(
                      controller.averageOrderValue.value,
                    ),
                    Icons.trending_up,
                    Colors.orange,
                  ),
                ),
              ],
            ),
      ],
    );
  }

  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(8)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          children: [
            Icon(icon, size: ScreenSize.isSmallScreen ? 24 : 32, color: color),
            SizedBox(height: ScreenSize.getPadding(8)),
            TextUtils.responsiveText(
              value,
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 20,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            TextUtils.responsiveText(
              title,
              style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesChart(SalesController controller) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(4)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'مخطط المبيعات',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            SizedBox(
              height: ScreenSize.isSmallScreen ? 200 : 300,
              child:
                  controller.salesChartData.isEmpty
                      ? Center(
                        child: TextUtils.responsiveText(
                          'لا توجد بيانات للعرض',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                            color: Colors.grey.shade600,
                          ),
                          maxLines: 1,
                          textAlign: TextAlign.center,
                        ),
                      )
                      : LineChart(
                        LineChartData(
                          gridData: FlGridData(show: true),
                          titlesData: FlTitlesData(
                            leftTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                reservedSize:
                                    ScreenSize.isSmallScreen ? 30 : 40,
                                getTitlesWidget: (value, meta) {
                                  return Padding(
                                    padding: EdgeInsets.only(
                                      right: ScreenSize.getPadding(4),
                                    ),
                                    child: Text(
                                      value.toInt().toString(),
                                      style: TextStyle(
                                        fontSize:
                                            ScreenSize.isSmallScreen ? 8 : 10,
                                        color: Colors.grey.shade600,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                            bottomTitles: AxisTitles(
                              sideTitles: SideTitles(
                                showTitles: true,
                                getTitlesWidget: (value, meta) {
                                  if (value.toInt() >= 0 &&
                                      value.toInt() <
                                          controller.salesChartLabels.length) {
                                    // في الشاشات الصغيرة، نعرض كل ثاني عنوان فقط لتجنب التداخل
                                    if (ScreenSize.isSmallScreen &&
                                        value.toInt() % 2 != 0) {
                                      return const SizedBox.shrink();
                                    }
                                    return Padding(
                                      padding: EdgeInsets.only(
                                        top: ScreenSize.getPadding(8),
                                      ),
                                      child: Text(
                                        controller.salesChartLabels[value
                                            .toInt()],
                                        style: TextStyle(
                                          fontSize:
                                              ScreenSize.isSmallScreen ? 8 : 10,
                                          color: Colors.grey.shade600,
                                        ),
                                      ),
                                    );
                                  }
                                  return const SizedBox.shrink();
                                },
                                reservedSize:
                                    ScreenSize.isSmallScreen ? 20 : 30,
                              ),
                            ),
                            rightTitles: AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                            topTitles: AxisTitles(
                              sideTitles: SideTitles(showTitles: false),
                            ),
                          ),
                          borderData: FlBorderData(show: true),
                          lineBarsData: [
                            LineChartBarData(
                              spots: controller.salesChartData,
                              isCurved: true,
                              color: Colors.blue,
                              barWidth: ScreenSize.isSmallScreen ? 2 : 3,
                              dotData: FlDotData(
                                show: true,
                                getDotPainter: (spot, percent, barData, index) {
                                  return FlDotCirclePainter(
                                    radius: ScreenSize.isSmallScreen ? 2 : 3.5,
                                    color: Colors.blue,
                                    strokeWidth: 1,
                                    strokeColor: Colors.white,
                                  );
                                },
                              ),
                              belowBarData: BarAreaData(
                                show: true,
                                color: Colors.blue.withAlpha(51),
                              ),
                            ),
                          ],
                        ),
                      ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopSellingProductsTable(SalesController controller) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(4)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'المنتجات الأكثر مبيعاً',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            controller.topSellingProducts.isEmpty
                ? Center(
                  child: TextUtils.responsiveText(
                    'لا توجد بيانات للعرض',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                )
                : Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 3,
                          child: TextUtils.responsiveText(
                            'المنتج',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: TextUtils.responsiveText(
                            'الكمية',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                            textAlign: TextAlign.center,
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: TextUtils.responsiveText(
                            'الإيرادات',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                    Divider(height: ScreenSize.getPadding(16)),
                    ListView.separated(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: controller.topSellingProducts.length,
                      separatorBuilder:
                          (context, index) =>
                              Divider(height: ScreenSize.getPadding(16)),
                      itemBuilder: (context, index) {
                        final product = controller.topSellingProducts[index];
                        return Row(
                          children: [
                            Expanded(
                              flex: 3,
                              child: TextUtils.responsiveText(
                                product['productName'] as String,
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                                ),
                                maxLines: 2,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: TextUtils.responsiveText(
                                (product['totalQuantity'] as int).toString(),
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                                ),
                                maxLines: 1,
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Expanded(
                              flex: 2,
                              child: TextUtils.responsiveText(
                                FormatUtils.formatCurrency(
                                  product['totalRevenue'] as double,
                                ),
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                                ),
                                maxLines: 1,
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
          ],
        ),
      ),
    );
  }

  Widget _buildSalesStatusDistribution(SalesController controller) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(4)),
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'توزيع حالات الطلبات',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            controller.salesStatusData.isEmpty
                ? Center(
                  child: TextUtils.responsiveText(
                    'لا توجد بيانات للعرض',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                )
                : SizedBox(
                  height: ScreenSize.isSmallScreen ? 180 : 200,
                  child: PieChart(
                    PieChartData(
                      sections: _buildPieChartSections(controller),
                      centerSpaceRadius: ScreenSize.isSmallScreen ? 30 : 40,
                      sectionsSpace: 2,
                    ),
                  ),
                ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Wrap(
              spacing: ScreenSize.getPadding(16),
              runSpacing: ScreenSize.getPadding(8),
              children: _buildStatusLegend(controller),
            ),
          ],
        ),
      ),
    );
  }

  List<PieChartSectionData> _buildPieChartSections(SalesController controller) {
    final List<PieChartSectionData> sections = [];
    final List<Color> colors = [
      Colors.blue,
      Colors.orange,
      Colors.green,
      Colors.red,
    ];
    int colorIndex = 0;

    for (final entry in controller.salesStatusData.entries) {
      sections.add(
        PieChartSectionData(
          value: entry.value.toDouble(),
          title:
              '${((entry.value / controller.totalSalesCount.value) * 100).toStringAsFixed(1)}%',
          color: colors[colorIndex % colors.length],
          radius: ScreenSize.isSmallScreen ? 80 : 100,
          titleStyle: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 10 : 14,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      );
      colorIndex++;
    }

    return sections;
  }

  List<Widget> _buildStatusLegend(SalesController controller) {
    final List<Widget> legends = [];
    final List<Color> colors = [
      Colors.blue,
      Colors.orange,
      Colors.green,
      Colors.red,
    ];
    int colorIndex = 0;

    for (final entry in controller.salesStatusData.entries) {
      legends.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: ScreenSize.isSmallScreen ? 12 : 16,
              height: ScreenSize.isSmallScreen ? 12 : 16,
              color: colors[colorIndex % colors.length],
            ),
            SizedBox(width: ScreenSize.getPadding(4)),
            TextUtils.responsiveText(
              '${entry.key} (${entry.value})',
              style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 11 : 13),
              maxLines: 1,
            ),
          ],
        ),
      );
      colorIndex++;
    }

    return legends;
  }

  void _selectStartDate(
    BuildContext context,
    SalesController controller,
  ) async {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: controller.reportStartDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Theme.of(context).primaryColor),
            textTheme: Theme.of(context).textTheme.copyWith(
              bodyLarge: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
              bodyMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
              ),
              titleMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      controller.reportStartDate.value = selectedDate;
    }
  }

  void _selectEndDate(BuildContext context, SalesController controller) async {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: controller.reportEndDate.value,
      firstDate: controller.reportStartDate.value,
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Theme.of(context).primaryColor),
            textTheme: Theme.of(context).textTheme.copyWith(
              bodyLarge: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
              bodyMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
              ),
              titleMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      controller.reportEndDate.value = selectedDate;
    }
  }
}
