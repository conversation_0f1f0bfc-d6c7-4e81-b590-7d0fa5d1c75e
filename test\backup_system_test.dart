import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/di/dependency_injection.dart';
import 'package:mostafa_final/presentation/controllers/backup_controller.dart';
import 'package:mostafa_final/utils/backup_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // تهيئة حقن التبعيات
  setUpAll(() {
    DependencyInjection.init();
  });

  group('اختبار نظام النسخ الاحتياطي', () {
    late BackupController backupController;
    late Directory tempDir;

    setUp(() async {
      // إنشاء مجلد مؤقت للاختبار
      tempDir = await Directory.systemTemp.createTemp('backup_test_');

      // تهيئة وحدة تحكم النسخ الاحتياطي
      backupController = Get.put(BackupController());

      // تعيين مجلد النسخ الاحتياطي إلى المجلد المؤقت
      await backupController.setBackupDirectory(tempDir.path);
    });

    tearDown(() async {
      // حذف المجلد المؤقت بعد الانتهاء من الاختبار
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }

      // إزالة وحدة تحكم النسخ الاحتياطي
      Get.delete<BackupController>();
    });

    test('إنشاء نسخة احتياطية', () async {
      // إنشاء نسخة احتياطية
      final result = await backupController.createBackup(
        customName: 'test_backup',
      );

      // التحقق من نجاح العملية
      expect(result, isTrue);

      // التحقق من وجود النسخة الاحتياطية
      await backupController.loadBackups();
      expect(backupController.backups.length, greaterThan(0));

      // التحقق من اسم النسخة الاحتياطية
      final backupName = backupController.backups[0]['name'] as String;
      expect(backupName, contains('test_backup'));

      // طباعة معلومات النسخة الاحتياطية
      debugPrint('تم إنشاء نسخة احتياطية: $backupName');
      debugPrint(
        'مسار النسخة الاحتياطية: ${backupController.backups[0]['path']}',
      );
      debugPrint(
        'حجم النسخة الاحتياطية: ${backupController.formatFileSize(backupController.backups[0]['size'] as int)}',
      );
    });

    test('حذف نسخة احتياطية', () async {
      // إنشاء نسخة احتياطية
      await backupController.createBackup(customName: 'test_backup_to_delete');

      // تحميل النسخ الاحتياطية
      await backupController.loadBackups();

      // التحقق من وجود النسخة الاحتياطية
      expect(backupController.backups.length, greaterThan(0));

      // حذف النسخة الاحتياطية
      final backupPath = backupController.backups[0]['path'] as String;
      final result = await backupController.deleteBackup(backupPath);

      // التحقق من نجاح العملية
      expect(result, isTrue);

      // التحقق من عدم وجود النسخة الاحتياطية
      await backupController.loadBackups();

      // التحقق من عدم وجود النسخة الاحتياطية بنفس المسار
      final backupExists = backupController.backups.any(
        (backup) => backup['path'] == backupPath,
      );
      expect(backupExists, isFalse);
    });

    test('تعيين جدول النسخ الاحتياطي التلقائي', () async {
      // تعيين جدول النسخ الاحتياطي التلقائي
      final result = await backupController.setBackupSchedule('auto_24');

      // التحقق من نجاح العملية
      expect(result, isTrue);

      // التحقق من تحديث جدول النسخ الاحتياطي
      expect(backupController.backupSchedule.value, equals('auto_24'));
    });

    test('الحصول على وقت آخر نسخة احتياطية', () async {
      // إنشاء نسخة احتياطية
      await backupController.createBackup(customName: 'test_backup_time');

      // تحميل النسخ الاحتياطية
      await backupController.loadBackups();

      // التحقق من وجود وقت آخر نسخة احتياطية
      expect(backupController.lastBackupTime.value, isNotNull);

      // طباعة وقت آخر نسخة احتياطية
      debugPrint(
        'وقت آخر نسخة احتياطية: ${backupController.lastBackupTime.value}',
      );
    });
  });

  group('اختبار خدمة النسخ الاحتياطي', () {
    late BackupService backupService;
    late Directory tempDir;

    setUp(() async {
      // إنشاء مجلد مؤقت للاختبار
      tempDir = await Directory.systemTemp.createTemp('backup_service_test_');

      // تهيئة خدمة النسخ الاحتياطي
      backupService = BackupService();

      // تعيين مجلد النسخ الاحتياطي إلى المجلد المؤقت
      await backupService.setBackupDirectory(tempDir.path);
    });

    tearDown(() async {
      // حذف المجلد المؤقت بعد الانتهاء من الاختبار
      if (await tempDir.exists()) {
        await tempDir.delete(recursive: true);
      }
    });

    test('إنشاء واستعادة نسخة احتياطية', () async {
      // إنشاء نسخة احتياطية
      final backupPath = await backupService.createBackup(
        customName: 'service_test_backup',
      );

      // التحقق من نجاح العملية
      expect(backupPath, isNotNull);

      // التحقق من وجود ملف النسخة الاحتياطية
      final backupFile = File(backupPath!);
      expect(await backupFile.exists(), isTrue);

      // طباعة معلومات النسخة الاحتياطية
      debugPrint('تم إنشاء نسخة احتياطية: $backupPath');
      debugPrint('حجم النسخة الاحتياطية: ${await backupFile.length()} بايت');

      // الحصول على قائمة النسخ الاحتياطية المتاحة
      final backups = await backupService.getAvailableBackups();

      // التحقق من وجود النسخة الاحتياطية في القائمة
      expect(backups.length, greaterThan(0));
      expect(backups[0]['path'], equals(backupPath));
    });
  });
}
