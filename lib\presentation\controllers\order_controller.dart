import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/order.dart';
import 'package:mostafa_final/data/repositories/order_repository.dart';

class OrderController extends GetxController {
  final OrderRepository _orderRepository = Get.find<OrderRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  // قائمة الطلبيات
  final RxList<Order> orders = <Order>[].obs;
  final Rx<Order?> selectedOrder = Rx<Order?>(null);

  // بيانات الطلبية الجديدة
  final Rx<DateTime> orderDate = DateTime.now().obs;
  final RxString currency = 'USD'.obs; // العملة الافتراضية
  final RxDouble exchangeRate = 0.0.obs;
  final RxDouble transportCost = 0.0.obs;
  final RxDouble shippingCost = 0.0.obs;
  final RxDouble clearanceCost = 0.0.obs;
  final RxDouble customsDuty = 0.0.obs;
  final RxList<OrderItem> orderItems = <OrderItem>[].obs;

  // بيانات العنصر الجديد
  final RxString newItemAccessoryId = ''.obs;
  final RxString newItemName = ''.obs;
  final RxDouble newItemPrice = 0.0.obs;
  final RxInt newItemQuantity = 0.obs;
  final RxDouble newItemWeight = 0.0.obs;

  // قائمة العملات المتاحة
  final RxList<String> availableCurrencies =
      <String>[
        'USD', // دولار أمريكي
        'EUR', // يورو
        'CNY', // يوان صيني
        'GBP', // جنيه إسترليني
        'JPY', // ين ياباني
        'SAR', // ريال سعودي
        'AED', // درهم إماراتي
      ].obs;

  @override
  void onInit() {
    super.onInit();
    fetchOrders();
  }

  // جلب جميع الطلبيات
  Future<void> fetchOrders() async {
    isLoading.value = true;
    try {
      final fetchedOrders = await _orderRepository.getAllOrders();
      orders.assignAll(fetchedOrders);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل الطلبيات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // اختيار طلبية
  void selectOrder(Order order) {
    selectedOrder.value = order;
  }

  // إضافة عنصر جديد إلى الطلبية
  void addItemToOrder() {
    if (newItemName.value.isEmpty ||
        newItemPrice.value <= 0 ||
        newItemQuantity.value <= 0) {
      _showError('يرجى إدخال جميع بيانات العنصر بشكل صحيح');
      return;
    }

    final newItem = OrderItem(
      accessoryId: newItemAccessoryId.value,
      name: newItemName.value,
      price: newItemPrice.value,
      quantity: newItemQuantity.value,
      weight: newItemWeight.value,
    );

    orderItems.add(newItem);

    // إعادة تعيين قيم العنصر الجديد
    newItemAccessoryId.value = '';
    newItemName.value = '';
    newItemPrice.value = 0;
    newItemQuantity.value = 0;
    newItemWeight.value = 0;
  }

  // حذف عنصر من الطلبية
  void removeItemFromOrder(int index) {
    orderItems.removeAt(index);
  }

  // حفظ الطلبية الجديدة
  Future<void> saveOrder() async {
    if (orderItems.isEmpty) {
      _showError('لا يمكن حفظ طلبية بدون عناصر');
      return;
    }

    if (exchangeRate.value <= 0) {
      _showError('يرجى إدخال سعر صرف صحيح');
      return;
    }

    isSaving.value = true;
    try {
      final newOrder = Order(
        date: orderDate.value,
        items: orderItems,
        transportCost: transportCost.value,
        shippingCost: shippingCost.value,
        clearanceCost: clearanceCost.value,
        customsDuty: customsDuty.value,
        exchangeRate: exchangeRate.value,
        currency: currency.value,
      );

      await _orderRepository.createOrder(newOrder);
      await fetchOrders(); // تحديث القائمة

      // إعادة تعيين القيم بعد الحفظ
      resetForm();

      Get.snackbar(
        'نجاح',
        'تم حفظ الطلبية بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _showError('حدث خطأ أثناء حفظ الطلبية: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // تحديث طلبية موجودة
  Future<void> updateOrder() async {
    if (selectedOrder.value == null) {
      _showError('لم يتم اختيار طلبية للتعديل');
      return;
    }

    isSaving.value = true;
    try {
      final updatedOrder = Order(
        id: selectedOrder.value!.id,
        date: orderDate.value,
        items: orderItems,
        transportCost: transportCost.value,
        shippingCost: shippingCost.value,
        clearanceCost: clearanceCost.value,
        customsDuty: customsDuty.value,
        exchangeRate: exchangeRate.value,
        currency: currency.value,
      );

      await _orderRepository.updateOrder(updatedOrder);
      await fetchOrders(); // تحديث القائمة

      Get.snackbar(
        'نجاح',
        'تم تحديث الطلبية بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث الطلبية: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // حذف طلبية
  Future<void> deleteOrder(String id) async {
    isSaving.value = true;
    try {
      await _orderRepository.deleteOrder(id);
      await fetchOrders(); // تحديث القائمة
      selectedOrder.value = null; // إلغاء التحديد

      Get.snackbar(
        'نجاح',
        'تم حذف الطلبية بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      _showError('حدث خطأ أثناء حذف الطلبية: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // حساب تكلفة العنصر بالجنيه المصري
  Future<double> calculateItemEgpCost(String orderId, String itemId) async {
    try {
      return await _orderRepository.calculateItemEgpCost(orderId, itemId);
    } catch (e) {
      _showError('حدث خطأ أثناء حساب التكلفة: $e');
      return 0;
    }
  }

  // تحميل بيانات طلبية للتعديل
  void loadOrderForEdit(Order order) {
    selectedOrder.value = order;
    orderDate.value = order.date;
    transportCost.value = order.transportCost;
    shippingCost.value = order.shippingCost;
    clearanceCost.value = order.clearanceCost;
    customsDuty.value = order.customsDuty;
    exchangeRate.value = order.exchangeRate;
    currency.value = order.currency;
    orderItems.assignAll(order.items);
  }

  // إعادة تعيين نموذج الإدخال
  void resetForm() {
    orderDate.value = DateTime.now();
    transportCost.value = 0;
    shippingCost.value = 0;
    clearanceCost.value = 0;
    customsDuty.value = 0;
    exchangeRate.value = 0;
    currency.value = 'USD';
    orderItems.clear();
    selectedOrder.value = null;
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }

  // حساب إجمالي تكلفة الطلبية بالعملة الأجنبية
  double calculateTotalForeignCost() {
    double itemsTotal = orderItems.fold(
      0,
      (sum, item) => sum + (item.price * item.quantity),
    );
    return itemsTotal +
        transportCost.value +
        shippingCost.value +
        clearanceCost.value +
        customsDuty.value;
  }

  // حساب إجمالي تكلفة الطلبية بالجنيه المصري
  double calculateTotalEgpCost() {
    return calculateTotalForeignCost() * exchangeRate.value;
  }

  // تنسيق التاريخ
  String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }
}
