/// نماذج البيانات للوحة التحكم التنفيذية
library;

/// مؤشر الأداء الرئيسي (KPI)
class KPI {
  final String id;
  final String name;
  final String description;
  final double currentValue;
  final double targetValue;
  final double previousValue;
  final KPIType type;
  final KPIUnit unit;
  final KPITrend trend;
  final DateTime lastUpdated;
  final String? icon;
  final String? color;

  KPI({
    required this.id,
    required this.name,
    required this.description,
    required this.currentValue,
    required this.targetValue,
    required this.previousValue,
    required this.type,
    required this.unit,
    required this.trend,
    required this.lastUpdated,
    this.icon,
    this.color,
  });

  // حساب نسبة التحقق من الهدف
  double get achievementPercentage {
    if (targetValue == 0) return 0;
    return (currentValue / targetValue) * 100;
  }

  // حساب نسبة التغيير من القيمة السابقة
  double get changePercentage {
    if (previousValue == 0) return 0;
    return ((currentValue - previousValue) / previousValue) * 100;
  }

  // هل المؤشر يحقق الهدف؟
  bool get isTargetAchieved {
    switch (type) {
      case KPIType.higherIsBetter:
        return currentValue >= targetValue;
      case KPIType.lowerIsBetter:
        return currentValue <= targetValue;
      case KPIType.targetRange:
        return (currentValue >= targetValue * 0.9) &&
            (currentValue <= targetValue * 1.1);
    }
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'currentValue': currentValue,
      'targetValue': targetValue,
      'previousValue': previousValue,
      'type': type.index,
      'unit': unit.index,
      'trend': trend.index,
      'lastUpdated': lastUpdated.toIso8601String(),
      'icon': icon,
      'color': color,
    };
  }

  // إنشاء من Map
  factory KPI.fromMap(Map<String, dynamic> map) {
    return KPI(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      currentValue: map['currentValue'],
      targetValue: map['targetValue'],
      previousValue: map['previousValue'],
      type: KPIType.values[map['type']],
      unit: KPIUnit.values[map['unit']],
      trend: KPITrend.values[map['trend']],
      lastUpdated: DateTime.parse(map['lastUpdated']),
      icon: map['icon'],
      color: map['color'],
    );
  }
}

/// أنواع المؤشرات
enum KPIType {
  higherIsBetter, // كلما زاد كان أفضل
  lowerIsBetter, // كلما قل كان أفضل
  targetRange, // ضمن نطاق محدد
}

/// وحدات القياس
enum KPIUnit {
  percentage, // نسبة مئوية
  currency, // عملة
  count, // عدد
  hours, // ساعات
  days, // أيام
  kilograms, // كيلوجرام
  pieces, // قطع
  rate, // معدل
}

/// اتجاه المؤشر
enum KPITrend {
  up, // صاعد
  down, // هابط
  stable, // مستقر
  unknown, // غير معروف
}

/// تقرير تنفيذي
class ExecutiveReport {
  final String id;
  final String title;
  final String description;
  final ReportType type;
  final ReportPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, dynamic> data;
  final List<String> insights;
  final List<String> recommendations;
  final DateTime generatedAt;
  final String generatedBy;

  ExecutiveReport({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.data,
    required this.insights,
    required this.recommendations,
    required this.generatedAt,
    required this.generatedBy,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.index,
      'period': period.index,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'data': data,
      'insights': insights,
      'recommendations': recommendations,
      'generatedAt': generatedAt.toIso8601String(),
      'generatedBy': generatedBy,
    };
  }

  // إنشاء من Map
  factory ExecutiveReport.fromMap(Map<String, dynamic> map) {
    return ExecutiveReport(
      id: map['id'],
      title: map['title'],
      description: map['description'],
      type: ReportType.values[map['type']],
      period: ReportPeriod.values[map['period']],
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      data: Map<String, dynamic>.from(map['data']),
      insights: List<String>.from(map['insights']),
      recommendations: List<String>.from(map['recommendations']),
      generatedAt: DateTime.parse(map['generatedAt']),
      generatedBy: map['generatedBy'],
    );
  }
}

/// أنواع التقارير
enum ReportType {
  production, // تقرير الإنتاج
  maintenance, // تقرير الصيانة
  financial, // تقرير مالي
  quality, // تقرير الجودة
  efficiency, // تقرير الكفاءة
  comprehensive, // تقرير شامل
}

/// فترات التقارير
enum ReportPeriod {
  daily, // يومي
  weekly, // أسبوعي
  monthly, // شهري
  quarterly, // ربع سنوي
  yearly, // سنوي
  custom, // مخصص
}

/// بيانات الرسم البياني
class ChartData {
  final String label;
  final double value;
  final String? color;
  final DateTime? date;
  final Map<String, dynamic>? metadata;

  ChartData({
    required this.label,
    required this.value,
    this.color,
    this.date,
    this.metadata,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'label': label,
      'value': value,
      'color': color,
      'date': date?.toIso8601String(),
      'metadata': metadata,
    };
  }

  // إنشاء من Map
  factory ChartData.fromMap(Map<String, dynamic> map) {
    return ChartData(
      label: map['label'],
      value: map['value'],
      color: map['color'],
      date: map['date'] != null ? DateTime.parse(map['date']) : null,
      metadata: map['metadata'],
    );
  }
}

/// ملخص الأداء العام
class PerformanceSummary {
  final double overallScore;
  final ProductionMetrics production;
  final MaintenanceMetrics maintenance;
  final FinancialMetrics financial;
  final QualityMetrics quality;
  final DateTime calculatedAt;

  PerformanceSummary({
    required this.overallScore,
    required this.production,
    required this.maintenance,
    required this.financial,
    required this.quality,
    required this.calculatedAt,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'overallScore': overallScore,
      'production': production.toMap(),
      'maintenance': maintenance.toMap(),
      'financial': financial.toMap(),
      'quality': quality.toMap(),
      'calculatedAt': calculatedAt.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory PerformanceSummary.fromMap(Map<String, dynamic> map) {
    return PerformanceSummary(
      overallScore: map['overallScore'],
      production: ProductionMetrics.fromMap(map['production']),
      maintenance: MaintenanceMetrics.fromMap(map['maintenance']),
      financial: FinancialMetrics.fromMap(map['financial']),
      quality: QualityMetrics.fromMap(map['quality']),
      calculatedAt: DateTime.parse(map['calculatedAt']),
    );
  }
}

/// مؤشرات الإنتاج
class ProductionMetrics {
  final double efficiency;
  final int totalProduction;
  final double capacityUtilization;
  final double oeeScore; // Overall Equipment Effectiveness

  ProductionMetrics({
    required this.efficiency,
    required this.totalProduction,
    required this.capacityUtilization,
    required this.oeeScore,
  });

  Map<String, dynamic> toMap() {
    return {
      'efficiency': efficiency,
      'totalProduction': totalProduction,
      'capacityUtilization': capacityUtilization,
      'oeeScore': oeeScore,
    };
  }

  factory ProductionMetrics.fromMap(Map<String, dynamic> map) {
    return ProductionMetrics(
      efficiency: map['efficiency'],
      totalProduction: map['totalProduction'],
      capacityUtilization: map['capacityUtilization'],
      oeeScore: map['oeeScore'],
    );
  }
}

/// مؤشرات الصيانة
class MaintenanceMetrics {
  final double mtbf; // Mean Time Between Failures
  final double mttr; // Mean Time To Repair
  final double availability;
  final double maintenanceCostRatio;

  MaintenanceMetrics({
    required this.mtbf,
    required this.mttr,
    required this.availability,
    required this.maintenanceCostRatio,
  });

  Map<String, dynamic> toMap() {
    return {
      'mtbf': mtbf,
      'mttr': mttr,
      'availability': availability,
      'maintenanceCostRatio': maintenanceCostRatio,
    };
  }

  factory MaintenanceMetrics.fromMap(Map<String, dynamic> map) {
    return MaintenanceMetrics(
      mtbf: map['mtbf'],
      mttr: map['mttr'],
      availability: map['availability'],
      maintenanceCostRatio: map['maintenanceCostRatio'],
    );
  }
}

/// المؤشرات المالية
class FinancialMetrics {
  final double totalRevenue;
  final double totalCosts;
  final double profitMargin;
  final double roi; // Return on Investment

  FinancialMetrics({
    required this.totalRevenue,
    required this.totalCosts,
    required this.profitMargin,
    required this.roi,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalRevenue': totalRevenue,
      'totalCosts': totalCosts,
      'profitMargin': profitMargin,
      'roi': roi,
    };
  }

  factory FinancialMetrics.fromMap(Map<String, dynamic> map) {
    return FinancialMetrics(
      totalRevenue: map['totalRevenue'],
      totalCosts: map['totalCosts'],
      profitMargin: map['profitMargin'],
      roi: map['roi'],
    );
  }
}

/// مؤشرات الجودة
class QualityMetrics {
  final double defectRate;
  final double customerSatisfaction;
  final double firstPassYield;
  final double reworkRate;

  QualityMetrics({
    required this.defectRate,
    required this.customerSatisfaction,
    required this.firstPassYield,
    required this.reworkRate,
  });

  Map<String, dynamic> toMap() {
    return {
      'defectRate': defectRate,
      'customerSatisfaction': customerSatisfaction,
      'firstPassYield': firstPassYield,
      'reworkRate': reworkRate,
    };
  }

  factory QualityMetrics.fromMap(Map<String, dynamic> map) {
    return QualityMetrics(
      defectRate: map['defectRate'],
      customerSatisfaction: map['customerSatisfaction'],
      firstPassYield: map['firstPassYield'],
      reworkRate: map['reworkRate'],
    );
  }
}

/// إضافات مساعدة للتعامل مع الأنواع
extension KPIUnitExtension on KPIUnit {
  String get symbol {
    switch (this) {
      case KPIUnit.percentage:
        return '%';
      case KPIUnit.currency:
        return 'ريال';
      case KPIUnit.count:
        return '';
      case KPIUnit.hours:
        return 'ساعة';
      case KPIUnit.days:
        return 'يوم';
      case KPIUnit.kilograms:
        return 'كجم';
      case KPIUnit.pieces:
        return 'قطعة';
      case KPIUnit.rate:
        return '';
    }
  }
}

extension ReportTypeExtension on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.production:
        return 'تقرير الإنتاج';
      case ReportType.maintenance:
        return 'تقرير الصيانة';
      case ReportType.financial:
        return 'تقرير مالي';
      case ReportType.quality:
        return 'تقرير الجودة';
      case ReportType.efficiency:
        return 'تقرير الكفاءة';
      case ReportType.comprehensive:
        return 'تقرير شامل';
    }
  }
}

extension ReportPeriodExtension on ReportPeriod {
  String get displayName {
    switch (this) {
      case ReportPeriod.daily:
        return 'يومي';
      case ReportPeriod.weekly:
        return 'أسبوعي';
      case ReportPeriod.monthly:
        return 'شهري';
      case ReportPeriod.quarterly:
        return 'ربع سنوي';
      case ReportPeriod.yearly:
        return 'سنوي';
      case ReportPeriod.custom:
        return 'مخصص';
    }
  }
}
