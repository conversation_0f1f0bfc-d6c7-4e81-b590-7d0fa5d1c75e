class MoldChangeHistory {
  final String? id;
  final String machineId;
  final String moldId;
  final DateTime changeDate;
  final String? previousMoldId;
  final String? notes;

  MoldChangeHistory({
    this.id,
    required this.machineId,
    required this.moldId,
    required this.changeDate,
    this.previousMoldId,
    this.notes,
  });

  // تحويل MoldChangeHistory إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'machineId': machineId,
      'moldId': moldId,
      'changeDate': changeDate.toIso8601String(),
      'previousMoldId': previousMoldId,
      'notes': notes,
    };
  }

  // إنشاء MoldChangeHistory من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory MoldChangeHistory.fromMap(Map<String, dynamic> map) {
    return MoldChangeHistory(
      id: map['id'],
      machineId: map['machineId'],
      moldId: map['moldId'],
      changeDate: DateTime.parse(map['changeDate']),
      previousMoldId: map['previousMoldId'],
      notes: map['notes'],
    );
  }
}
