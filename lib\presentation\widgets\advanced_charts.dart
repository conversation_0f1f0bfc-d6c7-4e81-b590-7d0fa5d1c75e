import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:intl/intl.dart';

/// مجموعة من الرسوم البيانية المتقدمة والتفاعلية
class AdvancedCharts {
  
  /// رسم بياني خطي متقدم لاتجاه الإنتاج
  static Widget buildProductionTrendChart(
    List<ChartData> data, {
    double height = 300,
    Color? lineColor,
    bool showGrid = true,
    bool showTooltip = true,
  }) {
    if (data.isEmpty) {
      return _buildEmptyChart(height, 'لا توجد بيانات إنتاج');
    }

    final spots = data.asMap().entries.map((entry) {
      return FlSpot(entry.key.toDouble(), entry.value.value);
    }).toList();

    return Container(
      height: height,
      padding: const EdgeInsets.all(16),
      child: <PERSON><PERSON><PERSON>(
        LineChartData(
          gridData: FlGridData(
            show: showGrid,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            getDrawingHorizontalLine: (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            ),
            getDrawingVerticalLine: (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: data.length > 10 ? (data.length / 5).ceilToDouble() : 1,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        data[index].label,
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 50,
                getTitlesWidget: (value, meta) {
                  return Text(
                    value.toInt().toString(),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey.withValues(alpha: 0.5)),
          ),
          minX: 0,
          maxX: (data.length - 1).toDouble(),
          minY: 0,
          maxY: data.map((e) => e.value).reduce((a, b) => a > b ? a : b) * 1.1,
          lineBarsData: [
            LineChartBarData(
              spots: spots,
              isCurved: true,
              color: lineColor ?? Colors.blue,
              barWidth: 3,
              isStrokeCapRound: true,
              dotData: const FlDotData(show: true),
              belowBarData: BarAreaData(
                show: true,
                color: (lineColor ?? Colors.blue).withValues(alpha: 0.1),
              ),
            ),
          ],
          lineTouchData: showTooltip ? LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  final index = spot.x.toInt();
                  if (index >= 0 && index < data.length) {
                    return LineTooltipItem(
                      '${data[index].label}\n${spot.y.toStringAsFixed(1)}',
                      const TextStyle(color: Colors.white, fontSize: 12),
                    );
                  }
                  return null;
                }).toList();
              },
            ),
          ) : LineTouchData(enabled: false),
        ),
        duration: const Duration(milliseconds: 250),
      ),
    );
  }

  /// رسم بياني دائري لكفاءة الماكينات
  static Widget buildMachineEfficiencyPieChart(
    List<ChartData> data, {
    double height = 300,
    bool showPercentage = true,
    bool showLegend = true,
  }) {
    if (data.isEmpty) {
      return _buildEmptyChart(height, 'لا توجد بيانات كفاءة');
    }

    final sections = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final color = _getColorFromString(item.color) ?? _getColorByIndex(index);
      
      return PieChartSectionData(
        color: color,
        value: item.value,
        title: showPercentage ? '${item.value.toStringAsFixed(1)}%' : '',
        radius: 80,
        titleStyle: const TextStyle(
          fontSize: 12,
          fontWeight: FontWeight.bold,
          color: Colors.white,
        ),
      );
    }).toList();

    return Container(
      height: height,
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Expanded(
            child: PieChart(
              PieChartData(
                sections: sections,
                centerSpaceRadius: 40,
                sectionsSpace: 2,
                pieTouchData: PieTouchData(
                  touchCallback: (FlTouchEvent event, pieTouchResponse) {
                    // يمكن إضافة تفاعل هنا
                  },
                ),
              ),
            ),
          ),
          if (showLegend) ...[
            const SizedBox(height: 16),
            _buildPieChartLegend(data),
          ],
        ],
      ),
    );
  }

  /// رسم بياني عمودي للتكاليف
  static Widget buildCostBarChart(
    List<ChartData> data, {
    double height = 300,
    Color? barColor,
    bool showValues = true,
  }) {
    if (data.isEmpty) {
      return _buildEmptyChart(height, 'لا توجد بيانات تكاليف');
    }

    final barGroups = data.asMap().entries.map((entry) {
      final index = entry.key;
      final item = entry.value;
      final color = _getColorFromString(item.color) ?? barColor ?? Colors.orange;
      
      return BarChartGroupData(
        x: index,
        barRods: [
          BarChartRodData(
            toY: item.value,
            color: color,
            width: 20,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(4),
              topRight: Radius.circular(4),
            ),
          ),
        ],
        showingTooltipIndicators: showValues ? [0] : [],
      );
    }).toList();

    return Container(
      height: height,
      padding: const EdgeInsets.all(16),
      child: BarChart(
        BarChartData(
          alignment: BarChartAlignment.spaceAround,
          maxY: data.map((e) => e.value).reduce((a, b) => a > b ? a : b) * 1.1,
          barTouchData: BarTouchData(
            enabled: true,
            touchTooltipData: BarTouchTooltipData(
              getTooltipItem: (group, groupIndex, rod, rodIndex) {
                if (groupIndex < data.length) {
                  return BarTooltipItem(
                    '${data[groupIndex].label}\n${rod.toY.toStringAsFixed(1)}',
                    const TextStyle(color: Colors.white, fontSize: 12),
                  );
                }
                return null;
              },
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < data.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        data[index].label,
                        style: const TextStyle(fontSize: 10),
                        textAlign: TextAlign.center,
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 50,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatNumber(value),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey.withValues(alpha: 0.5)),
          ),
          barGroups: barGroups,
          gridData: FlGridData(
            show: true,
            drawHorizontalLine: true,
            drawVerticalLine: false,
            getDrawingHorizontalLine: (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// رسم بياني مختلط للمبيعات والأرباح
  static Widget buildSalesRevenueChart(
    List<ChartData> salesData,
    List<ChartData> profitData, {
    double height = 300,
  }) {
    if (salesData.isEmpty && profitData.isEmpty) {
      return _buildEmptyChart(height, 'لا توجد بيانات مبيعات');
    }

    final maxLength = [salesData.length, profitData.length].reduce((a, b) => a > b ? a : b);
    
    return Container(
      height: height,
      padding: const EdgeInsets.all(16),
      child: LineChart(
        LineChartData(
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            drawHorizontalLine: true,
            getDrawingHorizontalLine: (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            ),
            getDrawingVerticalLine: (value) => FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            ),
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                getTitlesWidget: (value, meta) {
                  final index = value.toInt();
                  if (index >= 0 && index < salesData.length) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Text(
                        salesData[index].label,
                        style: const TextStyle(fontSize: 10),
                      ),
                    );
                  }
                  return const Text('');
                },
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 60,
                getTitlesWidget: (value, meta) {
                  return Text(
                    _formatNumber(value),
                    style: const TextStyle(fontSize: 10),
                  );
                },
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: Colors.grey.withValues(alpha: 0.5)),
          ),
          minX: 0,
          maxX: (maxLength - 1).toDouble(),
          lineBarsData: [
            // خط المبيعات
            if (salesData.isNotEmpty)
              LineChartBarData(
                spots: salesData.asMap().entries.map((entry) {
                  return FlSpot(entry.key.toDouble(), entry.value.value);
                }).toList(),
                isCurved: true,
                color: Colors.green,
                barWidth: 3,
                isStrokeCapRound: true,
                dotData: const FlDotData(show: true),
                belowBarData: BarAreaData(
                  show: true,
                  color: Colors.green.withValues(alpha: 0.1),
                ),
              ),
            // خط الأرباح
            if (profitData.isNotEmpty)
              LineChartBarData(
                spots: profitData.asMap().entries.map((entry) {
                  return FlSpot(entry.key.toDouble(), entry.value.value);
                }).toList(),
                isCurved: true,
                color: Colors.blue,
                barWidth: 3,
                isStrokeCapRound: true,
                dotData: const FlDotData(show: true),
                belowBarData: BarAreaData(
                  show: true,
                  color: Colors.blue.withValues(alpha: 0.1),
                ),
              ),
          ],
          lineTouchData: LineTouchData(
            enabled: true,
            touchTooltipData: LineTouchTooltipData(
              getTooltipItems: (touchedSpots) {
                return touchedSpots.map((spot) {
                  final index = spot.x.toInt();
                  final isProfit = spot.barIndex == 1;
                  final data = isProfit ? profitData : salesData;
                  final label = isProfit ? 'الربح' : 'المبيعات';
                  
                  if (index >= 0 && index < data.length) {
                    return LineTooltipItem(
                      '$label\n${data[index].label}\n${_formatNumber(spot.y)}',
                      TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    );
                  }
                  return null;
                }).toList();
              },
            ),
          ),
        ),
        duration: const Duration(milliseconds: 250),
      ),
    );
  }

  /// بناء مخطط فارغ
  static Widget _buildEmptyChart(double height, String message) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.bar_chart,
              size: 48,
              color: Colors.grey.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: TextStyle(
                color: Colors.grey.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء مفتاح الرسم البياني الدائري
  static Widget _buildPieChartLegend(List<ChartData> data) {
    return Wrap(
      spacing: 16,
      runSpacing: 8,
      children: data.asMap().entries.map((entry) {
        final index = entry.key;
        final item = entry.value;
        final color = _getColorFromString(item.color) ?? _getColorByIndex(index);
        
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 16,
              height: 16,
              decoration: BoxDecoration(
                color: color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              item.label,
              style: const TextStyle(fontSize: 12),
            ),
          ],
        );
      }).toList(),
    );
  }

  /// الحصول على لون من النص
  static Color? _getColorFromString(String? colorString) {
    if (colorString == null) return null;
    
    try {
      if (colorString.startsWith('#')) {
        return Color(int.parse(colorString.substring(1), radix: 16) + 0xFF000000);
      }
    } catch (e) {
      return null;
    }
    
    return null;
  }

  /// الحصول على لون حسب الفهرس
  static Color _getColorByIndex(int index) {
    final colors = [
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.teal,
      Colors.pink,
      Colors.indigo,
      Colors.amber,
      Colors.cyan,
    ];
    
    return colors[index % colors.length];
  }

  /// تنسيق الأرقام
  static String _formatNumber(double value) {
    if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}م';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}ك';
    } else {
      return value.toStringAsFixed(0);
    }
  }
}

/// مكون رسم بياني قابل للتصدير
class ExportableChart extends StatefulWidget {
  final Widget chart;
  final String chartName;
  final VoidCallback? onExport;

  const ExportableChart({
    super.key,
    required this.chart,
    required this.chartName,
    this.onExport,
  });

  @override
  State<ExportableChart> createState() => _ExportableChartState();
}

class _ExportableChartState extends State<ExportableChart> {
  final GlobalKey _chartKey = GlobalKey();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.chartName,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            IconButton(
              icon: const Icon(Icons.download),
              onPressed: widget.onExport,
              tooltip: 'تصدير كصورة',
            ),
          ],
        ),
        const SizedBox(height: 8),
        RepaintBoundary(
          key: _chartKey,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: widget.chart,
          ),
        ),
      ],
    );
  }

  GlobalKey get chartKey => _chartKey;
}
