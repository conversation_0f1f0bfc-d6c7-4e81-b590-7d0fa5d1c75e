import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/worker.dart';
import 'package:uuid/uuid.dart';

class WorkerRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء عامل جديد
  Future<String> createWorker(Worker worker) async {
    final workerWithId = Worker(
      id: _uuid.v4(),
      name: worker.name,
      code: worker.code,
      jobTitle: worker.jobTitle,
      hourlyRate: worker.hourlyRate,
      department: worker.department,
      isActive: worker.isActive,
    );

    // حفظ العامل
    final workerId = await _databaseHelper.insert(
      DatabaseHelper.tableWorkers,
      workerWithId.toMap(),
    );

    return workerId;
  }

  // الحصول على جميع العمال
  Future<List<Worker>> getAllWorkers() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableWorkers,
    );

    return maps.map((map) => Worker.fromMap(map)).toList();
  }

  // الحصول على العمال النشطين
  Future<List<Worker>> getActiveWorkers() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableWorkers} WHERE isActive = 1',
    );

    return maps.map((map) => Worker.fromMap(map)).toList();
  }

  // الحصول على العمال في قسم معين
  Future<List<Worker>> getWorkersByDepartment(String department) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableWorkers} WHERE department = ? AND isActive = 1',
      [department],
    );

    return maps.map((map) => Worker.fromMap(map)).toList();
  }

  // الحصول على عامل بمعرف معين
  Future<Worker?> getWorkerById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableWorkers,
      id,
    );

    if (map == null) {
      return null;
    }

    return Worker.fromMap(map);
  }

  // تحديث عامل
  Future<int> updateWorker(Worker worker) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableWorkers,
      worker.toMap(),
    );
  }

  // حذف عامل
  Future<int> deleteWorker(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableWorkers, id);
  }

  // تفعيل/إلغاء تفعيل عامل
  Future<int> toggleWorkerStatus(String id, bool isActive) async {
    final worker = await getWorkerById(id);

    if (worker == null) {
      return 0;
    }

    final updatedWorker = Worker(
      id: worker.id,
      name: worker.name,
      code: worker.code,
      jobTitle: worker.jobTitle,
      hourlyRate: worker.hourlyRate,
      department: worker.department,
      isActive: isActive,
    );

    return await updateWorker(updatedWorker);
  }

  // البحث عن عمال بناءً على الاسم أو الكود
  Future<List<Worker>> searchWorkers(String query) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableWorkers} WHERE name LIKE ? OR code LIKE ?',
      ['%$query%', '%$query%'],
    );

    return maps.map((map) => Worker.fromMap(map)).toList();
  }
}
