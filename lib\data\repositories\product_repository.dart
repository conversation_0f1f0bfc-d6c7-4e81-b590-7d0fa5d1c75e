import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:uuid/uuid.dart';

class ProductRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء منتج جديد
  Future<String> createProduct(Product product) async {
    final productWithId = Product(
      id: _uuid.v4(),
      name: product.name,
      code: product.code,
      components: product.components,
      assemblyTime: product.assemblyTime,
      packagingCost: product.packagingCost,
    );

    // حفظ المنتج
    final productId = await _databaseHelper.insert(
      DatabaseHelper.tableProducts,
      productWithId.toMap(),
    );

    // حفظ مكونات المنتج
    for (var component in productWithId.components) {
      final componentWithIds = ProductComponent(
        id: _uuid.v4(),
        productId: productWithId.id,
        name: component.name,
        partId: component.partId,
        type: component.type,
        quantity: component.quantity,
        cost: component.cost,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableProductComponents,
        componentWithIds.toMap(),
      );
    }

    return productId;
  }

  // الحصول على جميع المنتجات
  Future<List<Product>> getAllProducts() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableProducts,
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getProductComponents(map['id']);
        return Product.fromMap(map, components);
      }).toList(),
    );
  }

  // الحصول على منتج بمعرف معين
  Future<Product?> getProductById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableProducts,
      id,
    );

    if (map == null) {
      return null;
    }

    final components = await _getProductComponents(id);
    return Product.fromMap(map, components);
  }

  // تحديث منتج
  Future<void> updateProduct(Product product) async {
    // تحديث بيانات المنتج
    await _databaseHelper.update(DatabaseHelper.tableProducts, product.toMap());

    // حذف المكونات القديمة
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableProductComponents} WHERE productId = ?',
      [product.id],
    );

    // إضافة المكونات الجديدة
    for (var component in product.components) {
      final componentWithIds = ProductComponent(
        id: _uuid.v4(),
        productId: product.id,
        name: component.name,
        partId: component.partId,
        type: component.type,
        quantity: component.quantity,
        cost: component.cost,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableProductComponents,
        componentWithIds.toMap(),
      );
    }
  }

  // حذف منتج
  Future<void> deleteProduct(String id) async {
    // حذف مكونات المنتج أولاً
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableProductComponents} WHERE productId = ?',
      [id],
    );

    // ثم حذف المنتج نفسه
    await _databaseHelper.delete(DatabaseHelper.tableProducts, id);
  }

  // الحصول على مكونات منتج
  Future<List<ProductComponent>> _getProductComponents(String productId) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableProductComponents} WHERE productId = ?',
      [productId],
    );

    return maps.map((map) => ProductComponent.fromMap(map)).toList();
  }

  // البحث عن منتجات بناءً على الاسم أو الكود
  Future<List<Product>> searchProducts(String query) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableProducts} WHERE name LIKE ? OR code LIKE ?',
      ['%$query%', '%$query%'],
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getProductComponents(map['id']);
        return Product.fromMap(map, components);
      }).toList(),
    );
  }

  // حساب تكلفة المنتج
  Future<double> calculateProductCost(
    String productId,
    double laborCostPerMinute,
  ) async {
    final product = await getProductById(productId);
    if (product == null) {
      return 0;
    }

    return product.calculateTotalCost(laborCostPerMinute);
  }
}
