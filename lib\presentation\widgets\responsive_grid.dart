import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// شبكة متجاوبة مع حجم الشاشة
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final bool? primary;
  final String? emptyMessage;
  final IconData? emptyIcon;
  final Widget? emptyWidget;
  final bool isLoading;
  final Widget? loadingWidget;
  final int? crossAxisCount;
  final double? childAspectRatio;
  final double? crossAxisSpacing;
  final double? mainAxisSpacing;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.primary,
    this.emptyMessage,
    this.emptyIcon,
    this.emptyWidget,
    this.isLoading = false,
    this.loadingWidget,
    this.crossAxisCount,
    this.childAspectRatio,
    this.crossAxisSpacing,
    this.mainAxisSpacing,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    // تحديد عدد الأعمدة بناءً على حجم الشاشة
    final int gridCrossAxisCount =
        crossAxisCount ??
        (ScreenSize.isSmallScreen
            ? 2
            : ScreenSize.isMediumScreen
            ? 3
            : 4);

    // تحديد نسبة العرض إلى الارتفاع بناءً على حجم الشاشة
    final double gridChildAspectRatio =
        childAspectRatio ??
        (ScreenSize.isSmallScreen
            ? 0.8
            : ScreenSize.isMediumScreen
            ? 0.9
            : 1.0);

    // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
    if (isLoading) {
      return loadingWidget ?? const Center(child: CircularProgressIndicator());
    }

    // عرض رسالة فارغة إذا كانت القائمة فارغة
    if (children.isEmpty) {
      if (emptyWidget != null) {
        return emptyWidget!;
      }

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (emptyIcon != null)
              Icon(
                emptyIcon,
                size: ScreenSize.isSmallScreen ? 48 : 64,
                color: Colors.grey.shade400,
              ),
            if (emptyIcon != null) SizedBox(height: ScreenSize.getPadding(16)),
            if (emptyMessage != null)
              TextUtils.responsiveText(
                emptyMessage!,
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      primary: primary,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: gridCrossAxisCount,
        childAspectRatio: gridChildAspectRatio,
        crossAxisSpacing: crossAxisSpacing ?? ScreenSize.getPadding(16),
        mainAxisSpacing: mainAxisSpacing ?? ScreenSize.getPadding(16),
      ),
      itemCount: children.length,
      itemBuilder: (context, index) => children[index],
    );
  }

  /// إنشاء شبكة متجاوبة من العناصر
  static ResponsiveGrid fromItems<T>({
    required List<T> items,
    required Widget Function(T item) itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    ScrollController? controller,
    bool? primary,
    String? emptyMessage,
    IconData? emptyIcon,
    Widget? emptyWidget,
    bool isLoading = false,
    Widget? loadingWidget,
    int? crossAxisCount,
    double? childAspectRatio,
    double? crossAxisSpacing,
    double? mainAxisSpacing,
  }) {
    return ResponsiveGrid(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      primary: primary,
      emptyMessage: emptyMessage,
      emptyIcon: emptyIcon,
      emptyWidget: emptyWidget,
      isLoading: isLoading,
      loadingWidget: loadingWidget,
      crossAxisCount: crossAxisCount,
      childAspectRatio: childAspectRatio,
      crossAxisSpacing: crossAxisSpacing,
      mainAxisSpacing: mainAxisSpacing,
      children: items.map((item) => itemBuilder(item)).toList(),
    );
  }
}
