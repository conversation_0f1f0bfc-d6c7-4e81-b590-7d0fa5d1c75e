import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/controllers/performance_test_controller.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// صفحة اختبارات الأداء
class PerformanceTestPage extends StatelessWidget {
  final PerformanceTestController controller = Get.put(
    PerformanceTestController(),
  );

  PerformanceTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsivePage(
      title: 'اختبارات الأداء',
      drawer: const CustomDrawer(),
      body: Obx(() {
        return SingleChildScrollView(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              Si<PERSON><PERSON><PERSON>(height: ScreenSize.getPadding(24)),
              _buildMonitoringControls(),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildTestSections(),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildTestResults(),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildReportSection(),
            ],
          ),
        );
      }),
    );
  }

  /// بناء رأس الصفحة
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'اختبارات أداء التطبيق',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: EnhancedAppTheme.textPrimary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(8)),
        TextUtils.responsiveText(
          'قياس أداء التطبيق وتحسينه',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            color: EnhancedAppTheme.textSecondary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        Obx(() {
          if (controller.testStatus.value.isNotEmpty) {
            return Container(
              padding: EdgeInsets.all(ScreenSize.getPadding(12)),
              decoration: BoxDecoration(
                color: EnhancedAppTheme.primaryColor.withAlpha(30),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: EnhancedAppTheme.primaryColor,
                    size: ScreenSize.isSmallScreen ? 18 : 20,
                  ),
                  SizedBox(width: ScreenSize.getPadding(8)),
                  Expanded(
                    child: TextUtils.responsiveText(
                      controller.testStatus.value,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 13 : 14,
                        color: EnhancedAppTheme.textPrimary,
                      ),
                      maxLines: 2,
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        }),
        Obx(() {
          if (controller.errorMessage.value.isNotEmpty) {
            return Container(
              margin: EdgeInsets.only(top: ScreenSize.getPadding(8)),
              padding: EdgeInsets.all(ScreenSize.getPadding(12)),
              decoration: BoxDecoration(
                color: Colors.red.withAlpha(30),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Colors.red,
                    size: ScreenSize.isSmallScreen ? 18 : 20,
                  ),
                  SizedBox(width: ScreenSize.getPadding(8)),
                  Expanded(
                    child: TextUtils.responsiveText(
                      controller.errorMessage.value,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 13 : 14,
                        color: Colors.red,
                      ),
                      maxLines: 3,
                    ),
                  ),
                ],
              ),
            );
          }
          return const SizedBox.shrink();
        }),
      ],
    );
  }

  /// بناء أدوات التحكم في المراقبة
  Widget _buildMonitoringControls() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'مراقبة الأداء',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
                color: EnhancedAppTheme.textPrimary,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Obx(() {
                  return ElevatedButton.icon(
                    onPressed:
                        controller.isRunningTests.value
                            ? null
                            : controller.isMonitoring.value
                            ? controller.stopMonitoring
                            : controller.startMonitoring,
                    icon: Icon(
                      controller.isMonitoring.value
                          ? Icons.stop_circle
                          : Icons.play_circle,
                      size: ScreenSize.isSmallScreen ? 18 : 20,
                    ),
                    label: Text(
                      controller.isMonitoring.value
                          ? 'إيقاف المراقبة'
                          : 'بدء المراقبة',
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor:
                          controller.isMonitoring.value
                              ? Colors.red
                              : EnhancedAppTheme.primaryColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(16),
                        vertical: ScreenSize.getPadding(12),
                      ),
                    ),
                  );
                }),
                ElevatedButton.icon(
                  onPressed:
                      controller.isRunningTests.value
                          ? null
                          : controller.resetMonitoringData,
                  icon: Icon(
                    Icons.refresh,
                    size: ScreenSize.isSmallScreen ? 18 : 20,
                  ),
                  label: const Text('إعادة تعيين'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: EnhancedAppTheme.accentColor,
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(16),
                      vertical: ScreenSize.getPadding(12),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء أقسام الاختبارات
  Widget _buildTestSections() {
    return Column(
      children: [
        _buildTestSection(
          title: 'اختبارات أداء قاعدة البيانات',
          icon: Icons.storage,
          onPressed:
              controller.isRunningTests.value
                  ? null
                  : controller.runDatabaseTests,
          buttonText: 'تشغيل اختبارات قاعدة البيانات',
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        _buildTestSection(
          title: 'قياس زمن استجابة واجهة المستخدم',
          icon: Icons.speed,
          onPressed:
              controller.isRunningTests.value
                  ? null
                  : () => _showUIResponseTestDialog(),
          buttonText: 'قياس زمن الاستجابة',
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        _buildTestSection(
          title: 'قياس استهلاك الذاكرة',
          icon: Icons.memory,
          onPressed:
              controller.isRunningTests.value ? null : controller.measureMemory,
          buttonText: 'قياس استهلاك الذاكرة',
        ),
      ],
    );
  }

  /// بناء قسم اختبار
  Widget _buildTestSection({
    required String title,
    required IconData icon,
    required VoidCallback? onPressed,
    required String buttonText,
  }) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: EnhancedAppTheme.primaryColor,
                  size: ScreenSize.isSmallScreen ? 20 : 24,
                ),
                SizedBox(width: ScreenSize.getPadding(8)),
                TextUtils.responsiveText(
                  title,
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.bold,
                    color: EnhancedAppTheme.textPrimary,
                  ),
                  maxLines: 1,
                ),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Center(
              child: ElevatedButton.icon(
                onPressed: onPressed,
                icon: Icon(
                  Icons.play_arrow,
                  size: ScreenSize.isSmallScreen ? 18 : 20,
                ),
                label: Text(buttonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: EnhancedAppTheme.primaryColor,
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء نتائج الاختبارات
  Widget _buildTestResults() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'نتائج الاختبارات',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
                color: EnhancedAppTheme.textPrimary,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Obx(() {
              if (controller.databaseTestResults.isNotEmpty) {
                return _buildDatabaseTestResults();
              }
              if (controller.uiTestResults.isNotEmpty) {
                return _buildUITestResults();
              }
              if (controller.memoryUsage.value > 0) {
                return _buildMemoryUsageResult();
              }
              return Center(
                child: TextUtils.responsiveText(
                  'لا توجد نتائج اختبارات بعد',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                    color: EnhancedAppTheme.textSecondary,
                  ),
                  maxLines: 1,
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// بناء نتائج اختبارات قاعدة البيانات
  Widget _buildDatabaseTestResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildResultItem(
          'استعلام المخزون',
          controller.formatResponseTime(
            controller.databaseTestResults['inventory_query'] ?? 0.0,
          ),
        ),
        _buildResultItem(
          'استعلام الإنتاج',
          controller.formatResponseTime(
            controller.databaseTestResults['production_query'] ?? 0.0,
          ),
        ),
        _buildResultItem(
          'استعلام المبيعات',
          controller.formatResponseTime(
            controller.databaseTestResults['sales_query'] ?? 0.0,
          ),
        ),
        const Divider(),
        TextUtils.responsiveText(
          'اختبار الضغط العالي',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
            color: EnhancedAppTheme.textPrimary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(8)),
        if (controller.databaseTestResults['stress_test'] != null) ...[
          _buildResultItem(
            'استعلام بسيط',
            controller.formatResponseTime(
              controller.databaseTestResults['stress_test']['simple_query'] ??
                  0.0,
            ),
          ),
          _buildResultItem(
            'استعلام معقد',
            controller.formatResponseTime(
              controller.databaseTestResults['stress_test']['complex_query'] ??
                  0.0,
            ),
          ),
          _buildResultItem(
            'إدراج متعدد',
            controller.formatResponseTime(
              controller.databaseTestResults['stress_test']['bulk_insert'] ??
                  0.0,
            ),
          ),
        ],
      ],
    );
  }

  /// بناء نتائج اختبارات واجهة المستخدم
  Widget _buildUITestResults() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children:
          controller.uiTestResults.entries.map((entry) {
            return _buildResultItem(
              entry.key,
              controller.formatResponseTime(entry.value ?? 0.0),
            );
          }).toList(),
    );
  }

  /// بناء نتيجة استهلاك الذاكرة
  Widget _buildMemoryUsageResult() {
    return _buildResultItem(
      'استهلاك الذاكرة',
      controller.formatMemoryUsage(controller.memoryUsage.value),
    );
  }

  /// بناء عنصر نتيجة
  Widget _buildResultItem(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextUtils.responsiveText(
            label,
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              color: EnhancedAppTheme.textPrimary,
            ),
            maxLines: 1,
          ),
          TextUtils.responsiveText(
            value,
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              fontWeight: FontWeight.bold,
              color: EnhancedAppTheme.primaryColor,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  /// بناء قسم التقرير
  Widget _buildReportSection() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'تقرير الأداء',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
                color: EnhancedAppTheme.textPrimary,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed:
                      controller.isRunningTests.value
                          ? null
                          : controller.generatePerformanceReport,
                  icon: Icon(
                    Icons.assessment,
                    size: ScreenSize.isSmallScreen ? 18 : 20,
                  ),
                  label: const Text('إنشاء تقرير'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: EnhancedAppTheme.primaryColor,
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(16),
                      vertical: ScreenSize.getPadding(12),
                    ),
                  ),
                ),
                Obx(() {
                  return ElevatedButton.icon(
                    onPressed:
                        controller.reportPath.value.isEmpty
                            ? null
                            : controller.sharePerformanceReport,
                    icon: Icon(
                      Icons.share,
                      size: ScreenSize.isSmallScreen ? 18 : 20,
                    ),
                    label: const Text('مشاركة التقرير'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: EnhancedAppTheme.accentColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(16),
                        vertical: ScreenSize.getPadding(12),
                      ),
                    ),
                  );
                }),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            Obx(() {
              if (controller.reportPath.value.isNotEmpty) {
                return Container(
                  padding: EdgeInsets.all(ScreenSize.getPadding(12)),
                  decoration: BoxDecoration(
                    color: EnhancedAppTheme.successColor.withAlpha(30),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextUtils.responsiveText(
                        'تم إنشاء التقرير بنجاح',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                          fontWeight: FontWeight.bold,
                          color: EnhancedAppTheme.successColor,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(4)),
                      TextUtils.responsiveText(
                        controller.reportPath.value,
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          color: EnhancedAppTheme.textSecondary,
                        ),
                        maxLines: 2,
                      ),
                    ],
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
      ),
    );
  }

  /// عرض مربع حوار اختبار زمن استجابة واجهة المستخدم
  void _showUIResponseTestDialog() {
    final TextEditingController screenNameController = TextEditingController();

    Get.dialog(
      AlertDialog(
        title: const Text('قياس زمن استجابة واجهة المستخدم'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('أدخل اسم الشاشة المراد قياس زمن استجابتها:'),
            SizedBox(height: ScreenSize.getPadding(8)),
            TextField(
              controller: screenNameController,
              decoration: const InputDecoration(
                hintText: 'اسم الشاشة',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              final screenName = screenNameController.text.trim();
              if (screenName.isNotEmpty) {
                Get.back();
                controller.measureUIResponseTime(screenName);
              }
            },
            child: const Text('قياس'),
          ),
        ],
      ),
    );
  }
}
