import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:mostafa_final/data/repositories/executive_dashboard_repository.dart';

/// خدمة التنبيهات المتقدمة
class NotificationService extends GetxService {
  static NotificationService get instance => Get.find<NotificationService>();
  
  final ExecutiveDashboardRepository _dashboardRepository = ExecutiveDashboardRepository();
  
  // قائمة التنبيهات النشطة
  final activeNotifications = <SmartNotification>[].obs;
  
  // إعدادات التنبيهات
  final notificationSettings = <String, NotificationThreshold>{}.obs;
  
  // حالة الخدمة
  final isServiceEnabled = true.obs;
  final lastCheckTime = Rxn<DateTime>();

  @override
  void onInit() {
    super.onInit();
    _initializeService();
  }

  /// تهيئة الخدمة
  Future<void> _initializeService() async {
    await _loadNotificationSettings();
    _startPeriodicCheck();
  }

  /// تحميل إعدادات التنبيهات
  Future<void> _loadNotificationSettings() async {
    try {
      final settings = await _dashboardRepository.getAllUserSettings('default_user');
      
      // تحميل العتبات الافتراضية إذا لم تكن موجودة
      if (settings.isEmpty) {
        await _setDefaultThresholds();
      } else {
        _parseNotificationSettings(settings);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات التنبيهات: $e');
      await _setDefaultThresholds();
    }
  }

  /// تعيين العتبات الافتراضية
  Future<void> _setDefaultThresholds() async {
    final defaultThresholds = {
      'production_efficiency': NotificationThreshold(
        kpiId: 'production_efficiency',
        minValue: 70.0,
        maxValue: 100.0,
        criticalMin: 50.0,
        warningMin: 60.0,
        isEnabled: true,
      ),
      'availability': NotificationThreshold(
        kpiId: 'availability',
        minValue: 85.0,
        maxValue: 100.0,
        criticalMin: 70.0,
        warningMin: 80.0,
        isEnabled: true,
      ),
      'profit_margin': NotificationThreshold(
        kpiId: 'profit_margin',
        minValue: 15.0,
        maxValue: 50.0,
        criticalMin: 5.0,
        warningMin: 10.0,
        isEnabled: true,
      ),
      'defect_rate': NotificationThreshold(
        kpiId: 'defect_rate',
        minValue: 0.0,
        maxValue: 3.0,
        criticalMax: 8.0,
        warningMax: 5.0,
        isEnabled: true,
        isReversed: true, // كلما قل كان أفضل
      ),
    };

    notificationSettings.value = defaultThresholds;
    
    // حفظ في قاعدة البيانات
    for (var entry in defaultThresholds.entries) {
      await _dashboardRepository.saveDashboardSetting(
        'default_user',
        'threshold_${entry.key}',
        entry.value.toJson(),
      );
    }
  }

  /// تحليل إعدادات التنبيهات من قاعدة البيانات
  void _parseNotificationSettings(Map<String, String> settings) {
    final thresholds = <String, NotificationThreshold>{};
    
    settings.forEach((key, value) {
      if (key.startsWith('threshold_')) {
        final kpiId = key.substring(10);
        try {
          thresholds[kpiId] = NotificationThreshold.fromJson(value);
        } catch (e) {
          debugPrint('خطأ في تحليل عتبة التنبيه $kpiId: $e');
        }
      }
    });
    
    notificationSettings.value = thresholds;
  }

  /// بدء الفحص الدوري
  void _startPeriodicCheck() {
    // فحص كل 5 دقائق
    Stream.periodic(const Duration(minutes: 5)).listen((_) {
      if (isServiceEnabled.value) {
        checkKPIsForNotifications();
      }
    });
  }

  /// فحص المؤشرات للتنبيهات
  Future<void> checkKPIsForNotifications([List<KPI>? kpis]) async {
    try {
      lastCheckTime.value = DateTime.now();
      
      // الحصول على المؤشرات إذا لم يتم تمريرها
      kpis ??= await _dashboardRepository.getAllKPIs();
      
      final newNotifications = <SmartNotification>[];
      
      for (var kpi in kpis) {
        final threshold = notificationSettings[kpi.id];
        if (threshold == null || !threshold.isEnabled) continue;
        
        final notification = _checkKPIThreshold(kpi, threshold);
        if (notification != null) {
          newNotifications.add(notification);
        }
      }
      
      // إضافة التنبيهات الجديدة
      for (var notification in newNotifications) {
        _addNotification(notification);
      }
      
    } catch (e) {
      debugPrint('خطأ في فحص المؤشرات للتنبيهات: $e');
    }
  }

  /// فحص عتبة مؤشر واحد
  SmartNotification? _checkKPIThreshold(KPI kpi, NotificationThreshold threshold) {
    final value = kpi.currentValue;
    NotificationLevel? level;
    String? message;
    
    if (threshold.isReversed) {
      // للمؤشرات التي كلما قلت كانت أفضل (مثل معدل العيوب)
      if (threshold.criticalMax != null && value >= threshold.criticalMax!) {
        level = NotificationLevel.critical;
        message = 'تحذير حرج: ${kpi.name} وصل إلى ${value.toStringAsFixed(1)}${kpi.unit.symbol} (الحد الأقصى المسموح: ${threshold.criticalMax!.toStringAsFixed(1)})';
      } else if (threshold.warningMax != null && value >= threshold.warningMax!) {
        level = NotificationLevel.warning;
        message = 'تحذير: ${kpi.name} وصل إلى ${value.toStringAsFixed(1)}${kpi.unit.symbol} (الحد المفضل: ${threshold.warningMax!.toStringAsFixed(1)})';
      }
    } else {
      // للمؤشرات العادية
      if (threshold.criticalMin != null && value <= threshold.criticalMin!) {
        level = NotificationLevel.critical;
        message = 'تحذير حرج: ${kpi.name} انخفض إلى ${value.toStringAsFixed(1)}${kpi.unit.symbol} (الحد الأدنى الحرج: ${threshold.criticalMin!.toStringAsFixed(1)})';
      } else if (threshold.warningMin != null && value <= threshold.warningMin!) {
        level = NotificationLevel.warning;
        message = 'تحذير: ${kpi.name} انخفض إلى ${value.toStringAsFixed(1)}${kpi.unit.symbol} (الحد الأدنى المفضل: ${threshold.warningMin!.toStringAsFixed(1)})';
      } else if (value < threshold.minValue) {
        level = NotificationLevel.info;
        message = 'معلومات: ${kpi.name} أقل من المستوى المطلوب (${value.toStringAsFixed(1)}${kpi.unit.symbol} < ${threshold.minValue.toStringAsFixed(1)})';
      }
    }
    
    if (level != null && message != null) {
      return SmartNotification(
        id: '${kpi.id}_${DateTime.now().millisecondsSinceEpoch}',
        title: _getNotificationTitle(level),
        message: message,
        level: level,
        kpiId: kpi.id,
        kpiName: kpi.name,
        currentValue: value,
        threshold: threshold,
        createdAt: DateTime.now(),
      );
    }
    
    return null;
  }

  /// إضافة تنبيه جديد
  void _addNotification(SmartNotification notification) {
    // تجنب التنبيهات المكررة
    final existingIndex = activeNotifications.indexWhere(
      (n) => n.kpiId == notification.kpiId && n.level == notification.level,
    );
    
    if (existingIndex != -1) {
      // تحديث التنبيه الموجود
      activeNotifications[existingIndex] = notification;
    } else {
      // إضافة تنبيه جديد
      activeNotifications.add(notification);
      
      // عرض التنبيه للمستخدم
      _showNotificationToUser(notification);
    }
    
    // الحفاظ على حد أقصى 50 تنبيه
    if (activeNotifications.length > 50) {
      activeNotifications.removeRange(0, activeNotifications.length - 50);
    }
  }

  /// عرض التنبيه للمستخدم
  void _showNotificationToUser(SmartNotification notification) {
    final color = _getNotificationColor(notification.level);
    final icon = _getNotificationIcon(notification.level);
    
    Get.snackbar(
      notification.title,
      notification.message,
      backgroundColor: color.withValues(alpha: 0.9),
      colorText: Colors.white,
      icon: Icon(icon, color: Colors.white),
      duration: Duration(
        seconds: notification.level == NotificationLevel.critical ? 10 : 5,
      ),
      snackPosition: SnackPosition.TOP,
      margin: const EdgeInsets.all(16),
      borderRadius: 8,
      isDismissible: true,
      forwardAnimationCurve: Curves.easeOutBack,
      reverseAnimationCurve: Curves.easeInBack,
      onTap: (_) => _showNotificationDetails(notification),
    );
  }

  /// عرض تفاصيل التنبيه
  void _showNotificationDetails(SmartNotification notification) {
    Get.dialog(
      AlertDialog(
        title: Row(
          children: [
            Icon(
              _getNotificationIcon(notification.level),
              color: _getNotificationColor(notification.level),
            ),
            const SizedBox(width: 8),
            Text(notification.title),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('المؤشر: ${notification.kpiName}'),
            const SizedBox(height: 8),
            Text('القيمة الحالية: ${notification.currentValue.toStringAsFixed(1)}'),
            const SizedBox(height: 8),
            Text('الوقت: ${_formatDateTime(notification.createdAt)}'),
            const SizedBox(height: 16),
            Text(
              notification.message,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
          ElevatedButton(
            onPressed: () {
              markAsRead(notification.id);
              Get.back();
            },
            child: const Text('تم القراءة'),
          ),
        ],
      ),
    );
  }

  /// تحديث عتبة تنبيه
  Future<void> updateThreshold(String kpiId, NotificationThreshold threshold) async {
    try {
      notificationSettings[kpiId] = threshold;
      
      await _dashboardRepository.saveDashboardSetting(
        'default_user',
        'threshold_$kpiId',
        threshold.toJson(),
      );
      
      Get.snackbar('نجح', 'تم تحديث إعدادات التنبيه بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث إعدادات التنبيه: $e');
    }
  }

  /// تمكين/تعطيل الخدمة
  void toggleService(bool enabled) {
    isServiceEnabled.value = enabled;
    
    if (enabled) {
      checkKPIsForNotifications();
    }
  }

  /// وضع علامة مقروء على التنبيه
  void markAsRead(String notificationId) {
    final index = activeNotifications.indexWhere((n) => n.id == notificationId);
    if (index != -1) {
      activeNotifications[index] = activeNotifications[index].copyWith(isRead: true);
    }
  }

  /// حذف تنبيه
  void dismissNotification(String notificationId) {
    activeNotifications.removeWhere((n) => n.id == notificationId);
  }

  /// مسح جميع التنبيهات
  void clearAllNotifications() {
    activeNotifications.clear();
  }

  /// الحصول على عدد التنبيهات غير المقروءة
  int get unreadCount => activeNotifications.where((n) => !n.isRead).length;

  /// الحصول على التنبيهات حسب المستوى
  List<SmartNotification> getNotificationsByLevel(NotificationLevel level) {
    return activeNotifications.where((n) => n.level == level).toList();
  }

  /// طرق مساعدة
  String _getNotificationTitle(NotificationLevel level) {
    switch (level) {
      case NotificationLevel.critical:
        return 'تحذير حرج';
      case NotificationLevel.warning:
        return 'تحذير';
      case NotificationLevel.info:
        return 'معلومات';
      case NotificationLevel.success:
        return 'نجح';
    }
  }

  Color _getNotificationColor(NotificationLevel level) {
    switch (level) {
      case NotificationLevel.critical:
        return Colors.red;
      case NotificationLevel.warning:
        return Colors.orange;
      case NotificationLevel.info:
        return Colors.blue;
      case NotificationLevel.success:
        return Colors.green;
    }
  }

  IconData _getNotificationIcon(NotificationLevel level) {
    switch (level) {
      case NotificationLevel.critical:
        return Icons.error;
      case NotificationLevel.warning:
        return Icons.warning;
      case NotificationLevel.info:
        return Icons.info;
      case NotificationLevel.success:
        return Icons.check_circle;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}

/// نموذج التنبيه الذكي
class SmartNotification {
  final String id;
  final String title;
  final String message;
  final NotificationLevel level;
  final String kpiId;
  final String kpiName;
  final double currentValue;
  final NotificationThreshold threshold;
  final DateTime createdAt;
  final bool isRead;

  SmartNotification({
    required this.id,
    required this.title,
    required this.message,
    required this.level,
    required this.kpiId,
    required this.kpiName,
    required this.currentValue,
    required this.threshold,
    required this.createdAt,
    this.isRead = false,
  });

  SmartNotification copyWith({
    String? id,
    String? title,
    String? message,
    NotificationLevel? level,
    String? kpiId,
    String? kpiName,
    double? currentValue,
    NotificationThreshold? threshold,
    DateTime? createdAt,
    bool? isRead,
  }) {
    return SmartNotification(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      level: level ?? this.level,
      kpiId: kpiId ?? this.kpiId,
      kpiName: kpiName ?? this.kpiName,
      currentValue: currentValue ?? this.currentValue,
      threshold: threshold ?? this.threshold,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
    );
  }
}

/// مستويات التنبيه
enum NotificationLevel {
  critical,
  warning,
  info,
  success,
}

/// عتبة التنبيه
class NotificationThreshold {
  final String kpiId;
  final double minValue;
  final double maxValue;
  final double? criticalMin;
  final double? warningMin;
  final double? criticalMax;
  final double? warningMax;
  final bool isEnabled;
  final bool isReversed; // للمؤشرات التي كلما قلت كانت أفضل

  NotificationThreshold({
    required this.kpiId,
    required this.minValue,
    required this.maxValue,
    this.criticalMin,
    this.warningMin,
    this.criticalMax,
    this.warningMax,
    this.isEnabled = true,
    this.isReversed = false,
  });

  String toJson() {
    return '$kpiId|$minValue|$maxValue|${criticalMin ?? ''}|${warningMin ?? ''}|${criticalMax ?? ''}|${warningMax ?? ''}|$isEnabled|$isReversed';
  }

  factory NotificationThreshold.fromJson(String json) {
    final parts = json.split('|');
    return NotificationThreshold(
      kpiId: parts[0],
      minValue: double.parse(parts[1]),
      maxValue: double.parse(parts[2]),
      criticalMin: parts[3].isNotEmpty ? double.parse(parts[3]) : null,
      warningMin: parts[4].isNotEmpty ? double.parse(parts[4]) : null,
      criticalMax: parts[5].isNotEmpty ? double.parse(parts[5]) : null,
      warningMax: parts[6].isNotEmpty ? double.parse(parts[6]) : null,
      isEnabled: parts[7] == 'true',
      isReversed: parts.length > 8 ? parts[8] == 'true' : false,
    );
  }
}
