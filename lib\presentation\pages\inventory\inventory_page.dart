import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/inventory.dart';
import 'package:mostafa_final/presentation/controllers/inventory_controller.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/app_theme.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

class InventoryPage extends StatelessWidget {
  const InventoryPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final controller = Get.put(InventoryController());

    return ResponsivePage(
      title: 'إدارة المخزون',
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => controller.fetchInventoryItems(),
        ),
        IconButton(
          icon: Icon(
            Icons.notifications_outlined,
            size: ScreenSize.isSmallScreen ? 20 : 24,
          ),
          onPressed: () => _showAlertsDialog(context, controller),
        ),
      ],
      body: Column(
        children: [
          _buildSearchBar(controller),
          _buildInventoryFilters(),
          Expanded(
            child: Obx(() {
              if (controller.isLoading.value) {
                return Center(
                  child: CircularProgressIndicator(
                    strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
                  ),
                );
              }

              if (controller.errorMessage.value.isNotEmpty) {
                return Center(
                  child: TextUtils.responsiveText(
                    controller.errorMessage.value,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                    ),
                    maxLines: 3,
                    textAlign: TextAlign.center,
                  ),
                );
              }

              if (controller.filteredItems.isEmpty) {
                return Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.inventory_2_outlined,
                        size: ScreenSize.isSmallScreen ? 36 : 48,
                        color: Colors.grey.shade400,
                      ),
                      SizedBox(height: ScreenSize.getPadding(16)),
                      TextUtils.responsiveText(
                        'لا توجد عناصر في المخزون',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        ),
                        maxLines: 2,
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: ScreenSize.getPadding(16)),
                      ElevatedButton(
                        onPressed:
                            () => _showAddItemDialog(context, controller),
                        style: ElevatedButton.styleFrom(
                          padding: EdgeInsets.symmetric(
                            horizontal: ScreenSize.getPadding(16),
                            vertical: ScreenSize.getPadding(8),
                          ),
                        ),
                        child: TextUtils.responsiveText(
                          'إضافة عنصر جديد',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          ),
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: EdgeInsets.symmetric(
                  vertical: ScreenSize.getPadding(4),
                ),
                itemCount: controller.filteredItems.length,
                itemBuilder: (context, index) {
                  final item = controller.filteredItems[index];
                  return _buildInventoryItemCard(context, item, controller);
                },
              );
            }),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddItemDialog(context, controller),
        child: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 20 : 24),
      ),
    );
  }

  Widget _buildSearchBar(InventoryController controller) {
    return Padding(
      padding: EdgeInsets.all(ScreenSize.getPadding(16)),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'بحث عن عنصر...',
          prefixIcon: Icon(
            Icons.search,
            size: ScreenSize.isSmallScreen ? 20 : 24,
          ),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(ScreenSize.getPadding(8)),
          ),
          contentPadding: EdgeInsets.symmetric(
            vertical: ScreenSize.getPadding(12),
            horizontal: ScreenSize.getPadding(16),
          ),
        ),
        onChanged: (value) => controller.searchQuery.value = value,
        style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
      ),
    );
  }

  Widget _buildInventoryFilters() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: ScreenSize.getPadding(16)),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            Chip(
              label: TextUtils.responsiveText(
                'الكل',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 10 : 12),
                maxLines: 1,
              ),
              backgroundColor: AppTheme.primaryColor.withValues(
                red: 25,
                green: 118,
                blue: 210,
                alpha: 51,
              ),
              labelPadding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: 0,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: ScreenSize.isSmallScreen ? 0 : 2,
              ),
            ),
            SizedBox(width: ScreenSize.getPadding(8)),
            Chip(
              label: TextUtils.responsiveText(
                'المواد الخام',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 10 : 12),
                maxLines: 1,
              ),
              backgroundColor: Colors.grey.shade200,
              labelPadding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: 0,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: ScreenSize.isSmallScreen ? 0 : 2,
              ),
            ),
            SizedBox(width: ScreenSize.getPadding(8)),
            Chip(
              label: TextUtils.responsiveText(
                'المنتجات',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 10 : 12),
                maxLines: 1,
              ),
              backgroundColor: Colors.grey.shade200,
              labelPadding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: 0,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: ScreenSize.isSmallScreen ? 0 : 2,
              ),
            ),
            SizedBox(width: ScreenSize.getPadding(8)),
            Chip(
              label: TextUtils.responsiveText(
                'مستوى منخفض',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 10 : 12),
                maxLines: 1,
              ),
              backgroundColor: Colors.grey.shade200,
              labelPadding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: 0,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(4),
                vertical: ScreenSize.isSmallScreen ? 0 : 2,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInventoryItemCard(
    BuildContext context,
    Inventory item,
    InventoryController controller,
  ) {
    Color statusColor;
    switch (item.status) {
      case InventoryStatus.normal:
        statusColor = Colors.green;
        break;
      case InventoryStatus.low:
        statusColor = Colors.orange;
        break;
      case InventoryStatus.critical:
        statusColor = Colors.red;
        break;
    }

    String formattedDate = DateFormat('yyyy-MM-dd').format(item.lastUpdate);
    String itemType = item.itemType == 'raw_material' ? 'مادة خام' : 'منتج';

    return Card(
      margin: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(16),
        vertical: ScreenSize.getPadding(8),
      ),
      child: InkWell(
        onTap: () => _showItemDetailsDialog(context, item, controller),
        child: Padding(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        Container(
                          width: ScreenSize.isSmallScreen ? 10 : 12,
                          height: ScreenSize.isSmallScreen ? 10 : 12,
                          decoration: BoxDecoration(
                            color: statusColor,
                            shape: BoxShape.circle,
                          ),
                        ),
                        SizedBox(width: ScreenSize.getPadding(8)),
                        Expanded(
                          child: TextUtils.responsiveText(
                            item.itemName,
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextUtils.responsiveText(
                    item.itemCode,
                    style: TextStyle(
                      color: Colors.grey.shade600,
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
              SizedBox(height: ScreenSize.getPadding(8)),
              ScreenSize.isSmallScreen
                  ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextUtils.responsiveText(
                        'النوع: $itemType',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(4)),
                      TextUtils.responsiveText(
                        'آخر تحديث: $formattedDate',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                    ],
                  )
                  : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextUtils.responsiveText(
                        'النوع: $itemType',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                      TextUtils.responsiveText(
                        'آخر تحديث: $formattedDate',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                    ],
                  ),
              SizedBox(height: ScreenSize.getPadding(8)),
              ScreenSize.isSmallScreen
                  ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.inventory,
                            size: ScreenSize.isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: ScreenSize.getPadding(4)),
                          TextUtils.responsiveText(
                            '${item.currentQuantity} ${item.unit}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                      SizedBox(height: ScreenSize.getPadding(4)),
                      Row(
                        children: [
                          Icon(
                            Icons.attach_money,
                            size: ScreenSize.isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: ScreenSize.getPadding(4)),
                          TextUtils.responsiveText(
                            '${item.unitCost} جنيه/${item.unit}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ],
                  )
                  : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.inventory,
                            size: ScreenSize.isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: ScreenSize.getPadding(4)),
                          TextUtils.responsiveText(
                            '${item.currentQuantity} ${item.unit}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          Icon(
                            Icons.attach_money,
                            size: ScreenSize.isSmallScreen ? 14 : 16,
                          ),
                          SizedBox(width: ScreenSize.getPadding(4)),
                          TextUtils.responsiveText(
                            '${item.unitCost} جنيه/${item.unit}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ],
                  ),
              SizedBox(height: ScreenSize.getPadding(8)),
              LinearProgressIndicator(
                value: _calculateProgressValue(
                  item.currentQuantity,
                  item.minimumLevel,
                  item.reorderLevel,
                ),
                color: statusColor,
                backgroundColor: Colors.grey.shade200,
                minHeight: ScreenSize.isSmallScreen ? 3 : 4,
              ),
              SizedBox(height: ScreenSize.getPadding(4)),
              ScreenSize.isSmallScreen
                  ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      TextUtils.responsiveText(
                        'الحد الأدنى: ${item.minimumLevel} ${item.unit}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(2)),
                      TextUtils.responsiveText(
                        'مستوى إعادة الطلب: ${item.reorderLevel} ${item.unit}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                        ),
                        maxLines: 1,
                      ),
                    ],
                  )
                  : Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextUtils.responsiveText(
                        'الحد الأدنى: ${item.minimumLevel} ${item.unit}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                        ),
                        maxLines: 1,
                      ),
                      TextUtils.responsiveText(
                        'مستوى إعادة الطلب: ${item.reorderLevel} ${item.unit}',
                        style: TextStyle(
                          color: Colors.grey.shade600,
                          fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                        ),
                        maxLines: 1,
                      ),
                    ],
                  ),
            ],
          ),
        ),
      ),
    );
  }

  double _calculateProgressValue(
    double current,
    double minimum,
    double reorder,
  ) {
    if (current <= minimum) return 0.2;
    if (current <= reorder) return 0.5;
    return 1.0;
  }

  void _showAlertsDialog(BuildContext context, InventoryController controller) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'تنبيهات المخزون',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(16),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Obx(() {
                if (controller.activeAlerts.isEmpty) {
                  return Center(
                    child: TextUtils.responsiveText(
                      'لا توجد تنبيهات نشطة',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        color: Colors.grey.shade600,
                      ),
                      maxLines: 1,
                      textAlign: TextAlign.center,
                    ),
                  );
                }

                return ListView.builder(
                  shrinkWrap: true,
                  itemCount: controller.activeAlerts.length,
                  itemBuilder: (context, index) {
                    final alert = controller.activeAlerts[index];
                    return ListTile(
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                      leading: Icon(
                        Icons.warning_amber_rounded,
                        color:
                            alert.alertType.contains('حرج')
                                ? Colors.red
                                : Colors.orange,
                        size: ScreenSize.isSmallScreen ? 20 : 24,
                      ),
                      title: TextUtils.responsiveText(
                        alert.itemName,
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                      ),
                      subtitle: TextUtils.responsiveText(
                        '${alert.alertType} - المستوى الحالي: ${alert.currentLevel}',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                        ),
                        maxLines: 2,
                      ),
                      trailing: IconButton(
                        icon: Icon(
                          Icons.check_circle_outline,
                          size: ScreenSize.isSmallScreen ? 20 : 24,
                        ),
                        onPressed: () {
                          controller.resolveAlert(alert.id!);
                          Navigator.pop(context);
                        },
                      ),
                    );
                  },
                );
              }),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إغلاق',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
            ],
          ),
    );
  }

  void _showItemDetailsDialog(
    BuildContext context,
    Inventory item,
    InventoryController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    controller.selectInventoryItem(item.id!);

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              item.itemName,
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(16),
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildResponsiveDetailRow('الكود', item.itemCode),
                  _buildResponsiveDetailRow(
                    'النوع',
                    item.itemType == 'raw_material' ? 'مادة خام' : 'منتج',
                  ),
                  _buildResponsiveDetailRow(
                    'الكمية الحالية',
                    '${item.currentQuantity} ${item.unit}',
                  ),
                  _buildResponsiveDetailRow(
                    'سعر الوحدة',
                    '${item.unitCost} جنيه/${item.unit}',
                  ),
                  _buildResponsiveDetailRow(
                    'الحد الأدنى',
                    '${item.minimumLevel} ${item.unit}',
                  ),
                  _buildResponsiveDetailRow(
                    'مستوى إعادة الطلب',
                    '${item.reorderLevel} ${item.unit}',
                  ),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  TextUtils.responsiveText(
                    'حركات المخزون:',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                    ),
                    maxLines: 1,
                  ),
                  SizedBox(height: ScreenSize.getPadding(8)),
                  SizedBox(
                    height: ScreenSize.isSmallScreen ? 150 : 200,
                    child: Obx(() {
                      if (controller.isLoading.value) {
                        return Center(
                          child: CircularProgressIndicator(
                            strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
                          ),
                        );
                      }

                      if (controller.movements.isEmpty) {
                        return Center(
                          child: TextUtils.responsiveText(
                            'لا توجد حركات',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              color: Colors.grey.shade600,
                            ),
                            maxLines: 1,
                          ),
                        );
                      }

                      return ListView.builder(
                        shrinkWrap: true,
                        itemCount: controller.movements.length,
                        itemBuilder: (context, index) {
                          final movement = controller.movements[index];
                          final isIn = movement.type == MovementType.incoming;
                          final date = DateFormat(
                            'yyyy-MM-dd',
                          ).format(movement.date);

                          return ListTile(
                            contentPadding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(8),
                              vertical: ScreenSize.getPadding(4),
                            ),
                            leading: Icon(
                              isIn ? Icons.add_circle : Icons.remove_circle,
                              color: isIn ? Colors.green : Colors.red,
                              size: ScreenSize.isSmallScreen ? 20 : 24,
                            ),
                            title: TextUtils.responsiveText(
                              isIn ? 'وارد' : 'صادر',
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                fontWeight: FontWeight.bold,
                              ),
                              maxLines: 1,
                            ),
                            subtitle: TextUtils.responsiveText(
                              '${movement.quantity} ${item.unit} - $date\n${movement.notes}',
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                              ),
                              maxLines: 2,
                            ),
                            trailing: TextUtils.responsiveText(
                              '${movement.totalCost} جنيه',
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                              ),
                              maxLines: 1,
                            ),
                          );
                        },
                      );
                    }),
                  ),
                ],
              ),
            ),
            actions: [
              Wrap(
                spacing: ScreenSize.getPadding(4),
                runSpacing: ScreenSize.getPadding(4),
                alignment: WrapAlignment.center,
                children: [
                  TextButton(
                    onPressed:
                        () => _showAddMovementDialog(
                          context,
                          item,
                          controller,
                          true, // isIncoming
                        ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                    child: TextUtils.responsiveText(
                      'إضافة وارد',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  TextButton(
                    onPressed:
                        () => _showAddMovementDialog(
                          context,
                          item,
                          controller,
                          false, // isIncoming
                        ),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                    child: TextUtils.responsiveText(
                      'إضافة صادر',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  TextButton(
                    onPressed:
                        () => _showEditItemDialog(context, item, controller),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                    child: TextUtils.responsiveText(
                      'تعديل',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        horizontal: ScreenSize.getPadding(8),
                        vertical: ScreenSize.getPadding(4),
                      ),
                    ),
                    child: TextUtils.responsiveText(
                      'إغلاق',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                ],
              ),
            ],
          ),
    );
  }

  Widget _buildResponsiveDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(4)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextUtils.responsiveText(
            '$label: ',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
            ),
            maxLines: 1,
          ),
          Expanded(
            child: TextUtils.responsiveText(
              value,
              style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              maxLines: 2,
            ),
          ),
        ],
      ),
    );
  }

  void _showAddItemDialog(
    BuildContext context,
    InventoryController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final formKey = GlobalKey<FormState>();
    final TextEditingController nameController = TextEditingController();
    final TextEditingController codeController = TextEditingController();
    final TextEditingController typeController = TextEditingController(
      text: 'raw_material',
    );
    final TextEditingController quantityController = TextEditingController(
      text: '0',
    );
    final TextEditingController minimumController = TextEditingController(
      text: '50',
    );
    final TextEditingController reorderController = TextEditingController(
      text: '100',
    );
    final TextEditingController unitController = TextEditingController(
      text: 'كجم',
    );
    final TextEditingController costController = TextEditingController(
      text: '0',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: TextUtils.responsiveText(
              'إضافة عنصر مخزون جديد',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(16),
            ),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DropdownButtonFormField<String>(
                      decoration: InputDecoration(
                        labelText: 'نوع العنصر',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      value: 'raw_material',
                      items: [
                        DropdownMenuItem(
                          value: 'raw_material',
                          child: TextUtils.responsiveText(
                            'مادة خام',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'product',
                          child: TextUtils.responsiveText(
                            'منتج',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'package',
                          child: TextUtils.responsiveText(
                            'مستلزمات تغليف',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                        DropdownMenuItem(
                          value: 'spare_part',
                          child: TextUtils.responsiveText(
                            'قطع غيار',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ),
                      ],
                      onChanged: (value) {
                        typeController.text = value ?? 'raw_material';
                      },
                      validator:
                          (value) =>
                              value == null ? 'يرجى اختيار نوع العنصر' : null,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      icon: Icon(
                        Icons.arrow_drop_down,
                        size: ScreenSize.isSmallScreen ? 20 : 24,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: nameController,
                      decoration: InputDecoration(
                        labelText: 'اسم العنصر',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال اسم العنصر'
                                  : null,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: codeController,
                      decoration: InputDecoration(
                        labelText: 'كود العنصر',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال كود العنصر'
                                  : null,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية الحالية',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الكمية';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: minimumController,
                      decoration: InputDecoration(
                        labelText: 'الحد الأدنى',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الحد الأدنى';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: reorderController,
                      decoration: InputDecoration(
                        labelText: 'مستوى إعادة الطلب',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال مستوى إعادة الطلب';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: unitController,
                      decoration: InputDecoration(
                        labelText: 'وحدة القياس',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال وحدة القياس'
                                  : null,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(12)),
                    TextFormField(
                      controller: costController,
                      decoration: InputDecoration(
                        labelText: 'تكلفة الوحدة',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال تكلفة الوحدة';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(8),
                  ),
                ),
                child: TextUtils.responsiveText(
                  'إلغاء',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  maxLines: 1,
                ),
              ),
              Obx(
                () => ElevatedButton(
                  onPressed:
                      controller.isSubmitting.value
                          ? null
                          : () async {
                            if (formKey.currentState!.validate()) {
                              // تنفيذ إنشاء العنصر
                              final success = await controller
                                  .createInventoryItem(
                                    itemId:
                                        const Uuid().v4(), // إنشاء معرف جديد
                                    itemType: typeController.text,
                                    itemName: nameController.text,
                                    itemCode: codeController.text,
                                    currentQuantity: double.parse(
                                      quantityController.text,
                                    ),
                                    minimumLevel: double.parse(
                                      minimumController.text,
                                    ),
                                    reorderLevel: double.parse(
                                      reorderController.text,
                                    ),
                                    unit: unitController.text,
                                    unitCost: double.parse(costController.text),
                                  );

                              if (success && context.mounted) {
                                Navigator.pop(context);
                              }
                            }
                          },
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(16),
                      vertical: ScreenSize.getPadding(8),
                    ),
                  ),
                  child:
                      controller.isSubmitting.value
                          ? SizedBox(
                            width: ScreenSize.isSmallScreen ? 16 : 20,
                            height: ScreenSize.isSmallScreen ? 16 : 20,
                            child: CircularProgressIndicator(
                              strokeWidth: ScreenSize.isSmallScreen ? 2 : 3,
                            ),
                          )
                          : TextUtils.responsiveText(
                            'حفظ',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                ),
              ),
            ],
          ),
    );
  }

  void _showEditItemDialog(
    BuildContext context,
    Inventory item,
    InventoryController controller,
  ) {
    final formKey = GlobalKey<FormState>();
    final TextEditingController nameController = TextEditingController(
      text: item.itemName,
    );
    final TextEditingController codeController = TextEditingController(
      text: item.itemCode,
    );
    final TextEditingController minimumController = TextEditingController(
      text: item.minimumLevel.toString(),
    );
    final TextEditingController reorderController = TextEditingController(
      text: item.reorderLevel.toString(),
    );
    final TextEditingController unitController = TextEditingController(
      text: item.unit,
    );
    final TextEditingController costController = TextEditingController(
      text: item.unitCost.toString(),
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تعديل عنصر مخزون'),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    TextFormField(
                      controller: nameController,
                      decoration: const InputDecoration(
                        labelText: 'اسم العنصر',
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال اسم العنصر'
                                  : null,
                    ),
                    TextFormField(
                      controller: codeController,
                      decoration: const InputDecoration(
                        labelText: 'كود العنصر',
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال كود العنصر'
                                  : null,
                    ),
                    TextFormField(
                      controller: minimumController,
                      decoration: const InputDecoration(
                        labelText: 'الحد الأدنى',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الحد الأدنى';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: reorderController,
                      decoration: const InputDecoration(
                        labelText: 'مستوى إعادة الطلب',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال مستوى إعادة الطلب';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                    ),
                    TextFormField(
                      controller: unitController,
                      decoration: const InputDecoration(
                        labelText: 'وحدة القياس',
                      ),
                      validator:
                          (value) =>
                              value?.isEmpty ?? true
                                  ? 'يرجى إدخال وحدة القياس'
                                  : null,
                    ),
                    TextFormField(
                      controller: costController,
                      decoration: const InputDecoration(
                        labelText: 'تكلفة الوحدة',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال تكلفة الوحدة';
                        }
                        if (double.tryParse(value) == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }
                        return null;
                      },
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              Obx(
                () => ElevatedButton(
                  onPressed:
                      controller.isSubmitting.value
                          ? null
                          : () async {
                            if (formKey.currentState!.validate()) {
                              // تنفيذ تحديث العنصر
                              final success = await controller
                                  .updateInventoryItem(
                                    id: item.id!,
                                    itemName: nameController.text,
                                    itemCode: codeController.text,
                                    minimumLevel: double.parse(
                                      minimumController.text,
                                    ),
                                    reorderLevel: double.parse(
                                      reorderController.text,
                                    ),
                                    unit: unitController.text,
                                    unitCost: double.parse(costController.text),
                                  );

                              if (success && context.mounted) {
                                Navigator.pop(context);
                                Navigator.pop(
                                  context,
                                ); // إغلاق شاشة التفاصيل أيضاً
                              }
                            }
                          },
                  child:
                      controller.isSubmitting.value
                          ? const CircularProgressIndicator()
                          : const Text('حفظ'),
                ),
              ),
            ],
          ),
    );
  }

  void _showAddMovementDialog(
    BuildContext context,
    Inventory item,
    InventoryController controller,
    bool isIncoming,
  ) {
    final formKey = GlobalKey<FormState>();
    final TextEditingController quantityController = TextEditingController();
    final TextEditingController costController = TextEditingController(
      text: item.unitCost.toString(),
    );
    final TextEditingController notesController = TextEditingController();
    final TextEditingController referenceTypeController = TextEditingController(
      text: isIncoming ? 'purchase' : 'production',
    );

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(isIncoming ? 'إضافة وارد' : 'إضافة صادر'),
            content: Form(
              key: formKey,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      'العنصر: ${item.itemName} (${item.itemCode})',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 16),
                    TextFormField(
                      controller: quantityController,
                      decoration: InputDecoration(
                        labelText: 'الكمية (${item.unit})',
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الكمية';
                        }

                        final quantity = double.tryParse(value);
                        if (quantity == null) {
                          return 'يرجى إدخال قيمة رقمية صحيحة';
                        }

                        if (quantity <= 0) {
                          return 'يجب أن تكون الكمية أكبر من صفر';
                        }

                        // التحقق من توفر الكمية في حالة الصادر
                        if (!isIncoming && quantity > item.currentQuantity) {
                          return 'الكمية المطلوبة أكبر من المتاح (${item.currentQuantity} ${item.unit})';
                        }

                        return null;
                      },
                    ),
                    if (isIncoming)
                      TextFormField(
                        controller: costController,
                        decoration: const InputDecoration(
                          labelText: 'سعر الوحدة',
                        ),
                        keyboardType: TextInputType.number,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'يرجى إدخال سعر الوحدة';
                          }

                          final cost = double.tryParse(value);
                          if (cost == null) {
                            return 'يرجى إدخال قيمة رقمية صحيحة';
                          }

                          if (cost <= 0) {
                            return 'يجب أن يكون السعر أكبر من صفر';
                          }

                          return null;
                        },
                      ),
                    DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'نوع المرجع',
                      ),
                      value: isIncoming ? 'purchase' : 'production',
                      items: [
                        if (isIncoming) ...[
                          const DropdownMenuItem(
                            value: 'purchase',
                            child: Text('شراء'),
                          ),
                          const DropdownMenuItem(
                            value: 'return',
                            child: Text('مرتجع'),
                          ),
                          const DropdownMenuItem(
                            value: 'adjustment',
                            child: Text('تسوية'),
                          ),
                        ] else ...[
                          const DropdownMenuItem(
                            value: 'production',
                            child: Text('إنتاج'),
                          ),
                          const DropdownMenuItem(
                            value: 'sales',
                            child: Text('مبيعات'),
                          ),
                          const DropdownMenuItem(
                            value: 'adjustment',
                            child: Text('تسوية'),
                          ),
                          const DropdownMenuItem(
                            value: 'wastage',
                            child: Text('تالف/فاقد'),
                          ),
                        ],
                      ],
                      onChanged: (value) {
                        referenceTypeController.text =
                            value ?? (isIncoming ? 'purchase' : 'production');
                      },
                    ),
                    TextFormField(
                      controller: notesController,
                      decoration: const InputDecoration(labelText: 'ملاحظات'),
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              Obx(
                () => ElevatedButton(
                  onPressed:
                      controller.isSubmitting.value
                          ? null
                          : () async {
                            if (formKey.currentState!.validate()) {
                              final quantity = double.parse(
                                quantityController.text,
                              );
                              final success =
                                  isIncoming
                                      ? await controller.recordInventoryIn(
                                        inventoryId: item.id!,
                                        quantity: quantity,
                                        referenceType:
                                            referenceTypeController.text,
                                        referenceId: const Uuid().v4(),
                                        costPerUnit: double.parse(
                                          costController.text,
                                        ),
                                        notes: notesController.text,
                                      )
                                      : await controller.recordInventoryOut(
                                        inventoryId: item.id!,
                                        quantity: quantity,
                                        referenceType:
                                            referenceTypeController.text,
                                        referenceId: const Uuid().v4(),
                                        notes: notesController.text,
                                      );

                              if (success && context.mounted) {
                                Navigator.pop(context);
                                Navigator.pop(
                                  context,
                                ); // إغلاق شاشة التفاصيل أيضاً
                              }
                            }
                          },
                  child:
                      controller.isSubmitting.value
                          ? const CircularProgressIndicator()
                          : const Text('حفظ'),
                ),
              ),
            ],
          ),
    );
  }
}
