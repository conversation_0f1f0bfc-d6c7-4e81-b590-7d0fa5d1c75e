/// نموذج بيانات الطاقة الإنتاجية للماكينة
class MachineCapacity {
  final String? id;
  final String machineId;
  final String moldId;
  final double theoreticalCycleTime; // وقت الدورة النظري (ثانية)
  final double actualCycleTime; // وقت الدورة الفعلي (ثانية)
  final double efficiency; // كفاءة الماكينة (%)
  final int partsPerCycle; // عدد القطع في الدورة الواحدة
  final double setupTime; // وقت الإعداد (دقائق)
  final DateTime calculatedAt; // تاريخ الحساب
  final int sampleSize; // عدد العينات المستخدمة في الحساب

  MachineCapacity({
    this.id,
    required this.machineId,
    required this.moldId,
    required this.theoreticalCycleTime,
    required this.actualCycleTime,
    required this.efficiency,
    required this.partsPerCycle,
    required this.setupTime,
    required this.calculatedAt,
    required this.sampleSize,
  });

  // حساب الإنتاج النظري في الساعة
  double get theoreticalPartsPerHour {
    return (3600 / theoreticalCycleTime) * partsPerCycle;
  }

  // حساب الإنتاج الفعلي في الساعة
  double get actualPartsPerHour {
    return (3600 / actualCycleTime) * partsPerCycle;
  }

  // حساب الإنتاج الفعلي مع مراعاة الكفاءة
  double get effectivePartsPerHour {
    return actualPartsPerHour * (efficiency / 100);
  }

  // حساب الإنتاج اليومي (8 ساعات عمل)
  double get dailyCapacity {
    return effectivePartsPerHour * 8;
  }

  // حساب الإنتاج اليومي مع عدد ورديات مخصص
  double dailyCapacityWithShifts(double shifts) {
    return effectivePartsPerHour * 8 * shifts;
  }

  // تحويل إلى Map لحفظ في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'machineId': machineId,
      'moldId': moldId,
      'theoreticalCycleTime': theoreticalCycleTime,
      'actualCycleTime': actualCycleTime,
      'efficiency': efficiency,
      'partsPerCycle': partsPerCycle,
      'setupTime': setupTime,
      'calculatedAt': calculatedAt.toIso8601String(),
      'sampleSize': sampleSize,
    };
  }

  // إنشاء من Map
  factory MachineCapacity.fromMap(Map<String, dynamic> map) {
    return MachineCapacity(
      id: map['id'],
      machineId: map['machineId'],
      moldId: map['moldId'],
      theoreticalCycleTime: map['theoreticalCycleTime'],
      actualCycleTime: map['actualCycleTime'],
      efficiency: map['efficiency'],
      partsPerCycle: map['partsPerCycle'],
      setupTime: map['setupTime'],
      calculatedAt: DateTime.parse(map['calculatedAt']),
      sampleSize: map['sampleSize'],
    );
  }

  // إنشاء نسخة معدلة
  MachineCapacity copyWith({
    String? id,
    String? machineId,
    String? moldId,
    double? theoreticalCycleTime,
    double? actualCycleTime,
    double? efficiency,
    int? partsPerCycle,
    double? setupTime,
    DateTime? calculatedAt,
    int? sampleSize,
  }) {
    return MachineCapacity(
      id: id ?? this.id,
      machineId: machineId ?? this.machineId,
      moldId: moldId ?? this.moldId,
      theoreticalCycleTime: theoreticalCycleTime ?? this.theoreticalCycleTime,
      actualCycleTime: actualCycleTime ?? this.actualCycleTime,
      efficiency: efficiency ?? this.efficiency,
      partsPerCycle: partsPerCycle ?? this.partsPerCycle,
      setupTime: setupTime ?? this.setupTime,
      calculatedAt: calculatedAt ?? this.calculatedAt,
      sampleSize: sampleSize ?? this.sampleSize,
    );
  }
}

/// نموذج بيانات الطلبية المعلقة
class PendingOrder {
  final String? id;
  final String productCode;
  final String productName;
  final int requiredQuantity;
  final int completedQuantity;
  final DateTime dueDate;
  final int priority; // 1 = عالي، 2 = متوسط، 3 = منخفض
  final String? customerName;
  final DateTime createdAt;

  PendingOrder({
    this.id,
    required this.productCode,
    required this.productName,
    required this.requiredQuantity,
    this.completedQuantity = 0,
    required this.dueDate,
    required this.priority,
    this.customerName,
    required this.createdAt,
  });

  // الكمية المتبقية
  int get remainingQuantity => requiredQuantity - completedQuantity;

  // هل الطلبية مكتملة؟
  bool get isCompleted => completedQuantity >= requiredQuantity;

  // هل الطلبية متأخرة؟
  bool get isOverdue => DateTime.now().isAfter(dueDate) && !isCompleted;

  // عدد الأيام المتبقية
  int get daysRemaining => dueDate.difference(DateTime.now()).inDays;

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'productCode': productCode,
      'productName': productName,
      'requiredQuantity': requiredQuantity,
      'completedQuantity': completedQuantity,
      'dueDate': dueDate.toIso8601String(),
      'priority': priority,
      'customerName': customerName,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory PendingOrder.fromMap(Map<String, dynamic> map) {
    return PendingOrder(
      id: map['id'],
      productCode: map['productCode'],
      productName: map['productName'],
      requiredQuantity: map['requiredQuantity'],
      completedQuantity: map['completedQuantity'] ?? 0,
      dueDate: DateTime.parse(map['dueDate']),
      priority: map['priority'],
      customerName: map['customerName'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  // إنشاء نسخة معدلة
  PendingOrder copyWith({
    String? id,
    String? productCode,
    String? productName,
    int? requiredQuantity,
    int? completedQuantity,
    DateTime? dueDate,
    int? priority,
    String? customerName,
    DateTime? createdAt,
  }) {
    return PendingOrder(
      id: id ?? this.id,
      productCode: productCode ?? this.productCode,
      productName: productName ?? this.productName,
      requiredQuantity: requiredQuantity ?? this.requiredQuantity,
      completedQuantity: completedQuantity ?? this.completedQuantity,
      dueDate: dueDate ?? this.dueDate,
      priority: priority ?? this.priority,
      customerName: customerName ?? this.customerName,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// أولويات الطلبيات
class OrderPriority {
  static const int high = 1;
  static const int medium = 2;
  static const int low = 3;

  static String getPriorityName(int priority) {
    switch (priority) {
      case high:
        return 'عالي';
      case medium:
        return 'متوسط';
      case low:
        return 'منخفض';
      default:
        return 'غير محدد';
    }
  }
}
