import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/utils/pdf_generator.dart';
import 'package:mostafa_final/utils/logger.dart';

class DailyProductionController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();
  final ProductionRepository _productionRepository =
      Get.find<ProductionRepository>();
  final InventoryRepository _inventoryRepository =
      Get.find<InventoryRepository>();

  final formKey = GlobalKey<FormState>();

  // متغيرات للتحميل
  final RxBool isLoading = true.obs;
  final RxBool isLoadingHistory = false.obs;

  // متغيرات للحفظ
  final TextEditingController rawMaterialUsedController =
      TextEditingController();
  final TextEditingController partsProducedController = TextEditingController();
  final TextEditingController cycleTimeController = TextEditingController();

  // متغير لعدد الورديات
  final RxDouble selectedShiftsCount = 1.0.obs;
  final List<double> availableShifts = [1.0, 1.5, 2.0, 3.0];

  // متغيرات للاختيار
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final RxString selectedMachineId = ''.obs;
  final RxString selectedMoldId = ''.obs;
  final RxString selectedRawMaterialId = ''.obs;

  // متغيرات لعرض البيانات المحسوبة
  final RxDouble calculatedWeight = 0.0.obs;
  final RxDouble calculatedScrap = 0.0.obs;

  // قوائم البيانات
  final RxList<Machine> machines = <Machine>[].obs;
  final RxList<Mold> molds = <Mold>[].obs;
  final RxList<Mold> availableMolds = <Mold>[].obs;
  final RxList<RawMaterial> rawMaterials = <RawMaterial>[].obs;
  final RxList<Map<String, dynamic>> dailyProduction =
      <Map<String, dynamic>>[].obs;

  // ثوابت
  final double electricityCostPerKwh =
      1.5; // سعر الكيلوواط في الساعة بالجنيه المصري
  final double operatorCostPerHour =
      20.0; // تكلفة المشغل بالساعة بالجنيه المصري

  @override
  void onInit() {
    super.onInit();
    loadInitialData();

    // إضافة المستمعين لحقول الإدخال لحساب عدد القطع عند تغيير الوزن
    rawMaterialUsedController.addListener(calculatePartsFromWeight);
  }

  @override
  void onClose() {
    rawMaterialUsedController.dispose();
    partsProducedController.dispose();
    cycleTimeController.dispose();
    super.onClose();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;

    try {
      await Future.wait([fetchMachines(), fetchMolds(), fetchRawMaterials()]);

      await fetchDailyProduction();
    } catch (e) {
      AppLogger.e('Error loading initial data', e);
    } finally {
      isLoading.value = false;
    }
  }

  // جلب قائمة الماكينات
  Future<void> fetchMachines() async {
    try {
      final machinesList = await _machineRepository.getAllMachines();
      machines.assignAll(machinesList);

      // اختيار أول ماكينة افتراضياً إذا كانت القائمة غير فارغة
      if (machines.isNotEmpty && selectedMachineId.isEmpty) {
        selectMachine(machines.first.id!);
      }
    } catch (e) {
      AppLogger.e('Error fetching machines', e);
    }
  }

  // جلب قائمة الإسطمبات
  Future<void> fetchMolds() async {
    try {
      final moldsList = await _moldRepository.getAllMolds();
      molds.assignAll(moldsList);

      // تحديث قائمة الإسطمبات المتاحة إذا تم اختيار ماكينة
      if (selectedMachineId.isNotEmpty) {
        updateAvailableMolds();
      }
    } catch (e) {
      AppLogger.e('Error fetching molds', e);
    }
  }

  // جلب قائمة المواد الخام
  Future<void> fetchRawMaterials() async {
    try {
      final materialsList = await _rawMaterialRepository.getAllRawMaterials();
      rawMaterials.assignAll(materialsList);

      // اختيار أول مادة خام افتراضياً إذا كانت القائمة غير فارغة
      if (rawMaterials.isNotEmpty && selectedRawMaterialId.isEmpty) {
        selectRawMaterial(rawMaterials.first.id!);
      }
    } catch (e) {
      AppLogger.e('Error fetching raw materials', e);
    }
  }

  // جلب إنتاج اليوم المحدد
  Future<void> fetchDailyProduction() async {
    isLoadingHistory.value = true;

    try {
      final dailyProductionList = await _productionRepository
          .getProductionByDate(selectedDate.value);
      dailyProduction.assignAll(dailyProductionList);
    } catch (e) {
      AppLogger.e('Error fetching daily production', e);
    } finally {
      isLoadingHistory.value = false;
    }
  }

  // تحديث قائمة الإسطمبات المتاحة للماكينة المحددة
  void updateAvailableMolds() {
    if (selectedMachineId.isEmpty) {
      availableMolds.clear();
      return;
    }

    // إيجاد الإسطمبة المركبة حالياً على الماكينة
    final currentMachine = machines.firstWhereOrNull(
      (m) => m.id == selectedMachineId.value,
    );

    if (currentMachine != null) {
      // الإسطمبات المتاحة هي الإسطمبات المتاحة أو المركبة حالياً على الماكينة المحددة
      final availableMoldsList =
          molds
              .where(
                (mold) =>
                    mold.status == MoldStatus.available ||
                    (mold.currentMachineId != null &&
                        mold.currentMachineId == currentMachine.id),
              )
              .toList();

      availableMolds.assignAll(availableMoldsList);

      // اختيار الإسطمبة المركبة حالياً على الماكينة
      if (currentMachine.currentMoldId != null) {
        selectedMoldId.value = currentMachine.currentMoldId!;
        _calculateWeight(); // إعادة حساب الوزن عند تغيير الإسطمبة
      } else if (availableMolds.isNotEmpty) {
        selectedMoldId.value = availableMolds.first.id!;
        _calculateWeight(); // إعادة حساب الوزن عند تغيير الإسطمبة
      } else {
        selectedMoldId.value = '';
        calculatedWeight.value = 0.0; // إعادة تعيين الوزن المحسوب
      }
    }
  }

  // حساب عدد القطع من الوزن الإجمالي
  void calculatePartsFromWeight() {
    if (selectedMoldId.isEmpty || rawMaterialUsedController.text.isEmpty) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    // البحث عن الإسطمبة المحددة
    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    // استخراج الوزن الإجمالي المستخدم
    final double? totalWeight = double.tryParse(rawMaterialUsedController.text);
    if (totalWeight == null || totalWeight <= 0) {
      partsProducedController.text = '';
      calculatedScrap.value = 0.0;
      return;
    }

    // حساب عدد القطع المنتجة باستخدام وزن القطعة الواحدة وعدد التجاويف
    // الوزن الإجمالي (كجم) × 1000 (للتحويل إلى جرام) ÷ وزن القطعة الواحدة (جرام)
    final double weightPerPart = selectedMold.singlePartWeight;
    final int cavityCount = selectedMold.cavityCount;

    // نسبة الفاقد المقدرة (0.5%)
    final double scrapRate = 0.005;

    // عدد القطع = (الوزن الإجمالي بالكيلوجرام × 1000) ÷ (وزن القطعة الواحدة بالجرام × (1 + نسبة الفاقد))
    final double adjustedWeightPerPart = weightPerPart * (1 + scrapRate);
    final int estimatedParts =
        ((totalWeight * 1000) / adjustedWeightPerPart).round();

    // تعديل عدد القطع ليكون من مضاعفات عدد التجاويف
    final int adjustedParts = (estimatedParts ~/ cavityCount) * cavityCount;

    // تعيين عدد القطع المحسوب في حقل الإدخال
    partsProducedController.text = adjustedParts.toString();

    // حساب الوزن النظري للقطع المنتجة (بدون فاقد)
    final double theoreticalWeight = (adjustedParts * weightPerPart) / 1000;

    // حساب الفاقد (0.5% من وزن القطع المنتجة)
    calculatedScrap.value = theoreticalWeight * scrapRate;

    // حساب وقت الدورة تلقائيًا
    calculateCycleTime(adjustedParts);
  }

  // حساب وقت الدورة تلقائيًا
  void calculateCycleTime(int partsCount) {
    if (partsCount <= 0 || selectedMoldId.isEmpty) {
      cycleTimeController.text = '';
      return;
    }

    // البحث عن الإسطمبة المحددة
    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      cycleTimeController.text = '';
      return;
    }

    // عدد التجاويف في الإسطمبة
    final int cavityCount = selectedMold.cavityCount;

    // عدد الدورات = عدد القطع ÷ عدد التجاويف
    final int cycleCount = partsCount ~/ cavityCount;

    // وقت الوردية بالثواني = 8 ساعات × 60 دقيقة × 60 ثانية = 28800 ثانية
    final double shiftTimeInSeconds = 28800.0 * selectedShiftsCount.value;

    // وقت الدورة = وقت الوردية ÷ عدد الدورات
    final double calculatedCycleTime = shiftTimeInSeconds / cycleCount;

    // تعيين وقت الدورة المحسوب في حقل الإدخال (تقريب إلى رقمين عشريين)
    cycleTimeController.text = calculatedCycleTime.toStringAsFixed(2);
  }

  // حساب الوزن النظري للإنتاج (للتوافق مع الكود القديم)
  void _calculateWeight() {
    if (selectedMoldId.isEmpty || partsProducedController.text.isEmpty) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    // البحث عن الإسطمبة المحددة
    final Mold? selectedMold = molds.firstWhereOrNull(
      (mold) => mold.id == selectedMoldId.value,
    );

    if (selectedMold == null) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    // استخراج عدد القطع المنتجة
    final int? partsProduced = int.tryParse(partsProducedController.text);
    if (partsProduced == null || partsProduced <= 0) {
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;
      return;
    }

    // حساب الوزن النظري للإنتاج باستخدام وزن القطعة الواحدة وعدد التجاويف
    final double theoreticalWeight =
        (selectedMold.singlePartWeight * partsProduced) /
        1000; // التحويل من جرام إلى كيلوجرام
    calculatedWeight.value = theoreticalWeight;

    // حساب الفاقد من خلال مقارنة الوزن النظري بالوزن الفعلي المستخدم
    final double? rawMaterialUsed = double.tryParse(
      rawMaterialUsedController.text,
    );
    if (rawMaterialUsed != null && rawMaterialUsed > 0) {
      calculatedScrap.value = rawMaterialUsed - theoreticalWeight;
    } else {
      calculatedScrap.value = 0.0;
    }
  }

  // اختيار تاريخ الإنتاج
  Future<void> selectDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate: selectedDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime(2100),
      builder: (context, child) {
        return Theme(
          data: ThemeData.light().copyWith(
            primaryColor: Colors.blue,
            colorScheme: const ColorScheme.light(primary: Colors.blue),
            buttonTheme: const ButtonThemeData(
              textTheme: ButtonTextTheme.primary,
            ),
          ),
          child: child!,
        );
      },
    );

    if (pickedDate != null && pickedDate != selectedDate.value) {
      selectedDate.value = pickedDate;
      await fetchDailyProduction();
    }
  }

  // اختيار ماكينة
  void selectMachine(String machineId) {
    selectedMachineId.value = machineId;
    updateAvailableMolds();
  }

  // اختيار إسطمبة
  void selectMold(String moldId) {
    selectedMoldId.value = moldId;
    _calculateWeight(); // إعادة حساب الوزن عند تغيير الإسطمبة
  }

  // اختيار مادة خام
  void selectRawMaterial(String materialId) {
    selectedRawMaterialId.value = materialId;
  }

  // البحث عن مادة خام حسب المعرف
  RawMaterial? findRawMaterialById(String id) {
    return rawMaterials.firstWhereOrNull((material) => material.id == id);
  }

  // البحث عن إسطمبة حسب المعرف
  Mold? findMoldById(String id) {
    return molds.firstWhereOrNull((mold) => mold.id == id);
  }

  // حساب الكمية المستخدمة تلقائياً
  void calculateRawMaterialUsed() {
    if (selectedMoldId.isEmpty || partsProducedController.text.isEmpty) {
      return;
    }

    // حساب الوزن النظري للإنتاج
    _calculateWeight();

    // إضافة هامش للفاقد (10% مثلاً)
    double estimatedUsage = calculatedWeight.value * 1.1;

    // تعيين القيمة في حقل الإدخال
    rawMaterialUsedController.text = estimatedUsage.toStringAsFixed(2);
  }

  // حفظ الإنتاج
  Future<void> saveProduction() async {
    if (!formKey.currentState!.validate()) {
      return;
    }

    // التحقق من توفر البيانات المطلوبة
    if (selectedMachineId.isEmpty ||
        selectedMoldId.isEmpty ||
        selectedRawMaterialId.isEmpty) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار الماكينة والإسطمبة والمادة الخام',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    try {
      // استخراج القيم من حقول الإدخال
      final double rawMaterialUsed = double.parse(
        rawMaterialUsedController.text,
      );
      final int partsProduced = int.parse(partsProducedController.text);
      final double cycleTime = double.parse(cycleTimeController.text);

      // الحصول على بيانات الماكينة
      final Machine? machine = machines.firstWhereOrNull(
        (m) => m.id == selectedMachineId.value,
      );

      if (machine == null) {
        throw Exception('لم يتم العثور على الماكينة');
      }

      // الحصول على بيانات الإسطمبة
      final Mold? mold = findMoldById(selectedMoldId.value);
      if (mold == null) {
        throw Exception('لم يتم العثور على الإسطمبة');
      }

      // حساب تكاليف الإنتاج
      // التكلفة بالساعة = استهلاك الطاقة بالكيلوواط × سعر الكيلوواط في الساعة
      final double machineHourlyCost =
          machine.powerConsumption * electricityCostPerKwh;

      // وقت التشغيل بالساعات = (وقت الدورة بالثواني × عدد القطع) / 3600
      final double operatingHours = (cycleTime * partsProduced) / 3600;

      // تكلفة الكهرباء = تكلفة الماكينة بالساعة × عدد ساعات التشغيل
      final double electricityCost = machineHourlyCost * operatingHours;

      // تكلفة المشغلين = تكلفة المشغل بالساعة × عدد ساعات التشغيل
      final double operatorCost = operatorCostPerHour * operatingHours;

      // إنشاء كائن الإنتاج
      final production = Production(
        date: DateTime.now(),
        machineId: selectedMachineId.value,
        moldId: selectedMoldId.value,
        rawMaterialId: selectedRawMaterialId.value,
        rawMaterialUsed: rawMaterialUsed,
        partsProduced: partsProduced,
        cycleTime: cycleTime,
        electricityCost: electricityCost,
        operatorCost: operatorCost,
        shiftsCount: selectedShiftsCount.value,
      );

      // حفظ الإنتاج
      await _productionRepository.createProduction(production);

      // تحديث المخزون بالقطع المنتجة
      await _updateInventoryWithProducedParts(
        mold,
        partsProduced,
        rawMaterialUsed,
        selectedRawMaterialId.value,
      );

      // تحديث قائمة الإنتاج اليومي
      await fetchDailyProduction();

      // تحديث قائمة المواد الخام
      await fetchRawMaterials();

      // إعادة تعيين حقول الإدخال
      rawMaterialUsedController.clear();
      partsProducedController.clear();
      cycleTimeController.clear();
      calculatedWeight.value = 0.0;
      calculatedScrap.value = 0.0;

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجاح',
        'تم حفظ الإنتاج بنجاح',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ الإنتاج: $e',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // تحديث المخزون بالقطع المنتجة
  Future<void> _updateInventoryWithProducedParts(
    Mold mold,
    int partsProduced,
    double rawMaterialUsed,
    String rawMaterialId,
  ) async {
    try {
      // 1. إضافة القطع المنتجة إلى المخزون
      await _inventoryRepository.addPlasticPartToInventory(
        mold.id!,
        mold.name,
        mold.productCode,
        partsProduced,
        (partsProduced * mold.singlePartWeight) / 1000, // وزن القطع بالكيلوجرام
        rawMaterialUsed,
        rawMaterialId,
        'إنتاج',
        null,
      );

      // 2. خصم المواد الخام المستخدمة من المخزون
      await _updateRawMaterialInventory(rawMaterialId, rawMaterialUsed);
    } catch (e) {
      AppLogger.e('Error updating inventory with produced parts', e);
      // لا نريد أن نوقف عملية الحفظ إذا فشل تحديث المخزون
      // سنسجل الخطأ فقط للتصحيح لاحقاً
    }
  }

  // خصم المواد الخام المستخدمة من المخزون
  Future<void> _updateRawMaterialInventory(
    String rawMaterialId,
    double usedQuantity,
  ) async {
    try {
      // البحث عن المادة الخام في المخزون
      final RawMaterial? rawMaterial = findRawMaterialById(rawMaterialId);
      if (rawMaterial == null) {
        throw Exception('لم يتم العثور على المادة الخام');
      }

      // تحديث كمية المادة الخام في المخزون (خصم الكمية المستخدمة)
      final updatedMaterial = rawMaterial.copyWith(
        availableQuantity: rawMaterial.availableQuantity - usedQuantity,
      );

      // حفظ التغييرات
      await _rawMaterialRepository.updateRawMaterial(updatedMaterial);

      // تسجيل حركة المخزون
      final inventoryItem = await _inventoryRepository
          .getInventoryItemByItemIdAndType(rawMaterialId, 'raw_material');

      if (inventoryItem != null) {
        // تسجيل حركة صرف من المخزون (القيمة سالبة)
        await _inventoryRepository.updateInventoryQuantity(
          inventoryItem.id!,
          -usedQuantity, // القيمة السالبة للإشارة إلى الصرف
          'إنتاج',
          'production',
          notes:
              'استخدام في الإنتاج - إسطمبة: ${findMoldById(selectedMoldId.value)?.name ?? "غير معروف"}',
        );
      }
    } catch (e) {
      AppLogger.e('Error updating raw material inventory', e);
      throw Exception('فشل تحديث مخزون المواد الخام: $e');
    }
  }

  // تنسيق التاريخ
  String formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  // تنسيق الوقت فقط
  String formatTimeOnly(DateTime dateTime) {
    return DateFormat('hh:mm a').format(dateTime);
  }

  // تحديث سجل إنتاج
  Future<void> updateProduction(
    Production updatedProduction,
    double rawMaterialDiff,
    int partsDiff,
  ) async {
    try {
      isLoadingHistory.value = true;

      // 1. تحديث سجل الإنتاج في قاعدة البيانات
      await _productionRepository.updateProduction(updatedProduction);

      // 2. تحديث المخزون بالفرق في القطع المنتجة
      if (partsDiff != 0) {
        // الحصول على بيانات الإسطمبة
        final Mold? mold = findMoldById(updatedProduction.moldId);
        if (mold == null) {
          throw Exception('لم يتم العثور على الإسطمبة');
        }

        // إضافة أو خصم الفرق في القطع المنتجة من المخزون
        await _inventoryRepository.addPlasticPartToInventory(
          mold.id!,
          mold.name,
          mold.productCode,
          partsDiff, // يمكن أن يكون موجباً أو سالباً
          (partsDiff * mold.singlePartWeight) / 1000, // وزن القطع بالكيلوجرام
          rawMaterialDiff,
          updatedProduction.rawMaterialId,
          'تعديل إنتاج',
          null,
        );
      }

      // 3. تحديث مخزون المواد الخام بالفرق في الكمية المستخدمة
      if (rawMaterialDiff != 0) {
        // البحث عن المادة الخام في المخزون
        final RawMaterial? rawMaterial = findRawMaterialById(updatedProduction.rawMaterialId);
        if (rawMaterial == null) {
          throw Exception('لم يتم العثور على المادة الخام');
        }

        // تحديث كمية المادة الخام في المخزون (إضافة أو خصم الفرق)
        final updatedMaterial = rawMaterial.copyWith(
          availableQuantity: rawMaterial.availableQuantity - rawMaterialDiff,
        );

        // حفظ التغييرات
        await _rawMaterialRepository.updateRawMaterial(updatedMaterial);

        // تسجيل حركة المخزون
        final inventoryItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(updatedProduction.rawMaterialId, 'raw_material');

        if (inventoryItem != null) {
          // تسجيل حركة في المخزون (القيمة سالبة للصرف، موجبة للإضافة)
          await _inventoryRepository.updateInventoryQuantity(
            inventoryItem.id!,
            -rawMaterialDiff, // سالب لأننا نريد عكس الفرق (إذا كان الفرق موجباً نخصم، وإذا كان سالباً نضيف)
            'تعديل إنتاج',
            'production_update',
            notes:
                'تعديل استخدام في الإنتاج - إسطمبة: ${findMoldById(updatedProduction.moldId)?.name ?? "غير معروف"}',
          );
        }
      }

      // 4. تحديث قائمة الإنتاج اليومي
      await fetchDailyProduction();

      // 5. تحديث قائمة المواد الخام
      await fetchRawMaterials();

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجاح',
        'تم تحديث الإنتاج بنجاح',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الإنتاج: $e',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoadingHistory.value = false;
    }
  }

  // حذف سجل إنتاج
  Future<void> deleteProduction(String id) async {
    try {
      isLoadingHistory.value = true;

      // 1. الحصول على بيانات الإنتاج قبل الحذف
      final production = dailyProduction.firstWhere((p) => p['id'] == id);
      final double rawMaterialUsed = production['rawMaterialUsed'];
      final int partsProduced = production['partsProduced'];
      final String moldId = production['moldId'];
      final String rawMaterialId = production['rawMaterialId'];

      // 2. حذف سجل الإنتاج من قاعدة البيانات
      await _productionRepository.deleteProduction(id);

      // 3. تحديث المخزون بإزالة القطع المنتجة
      // الحصول على بيانات الإسطمبة
      final Mold? mold = findMoldById(moldId);
      if (mold != null) {
        // خصم القطع المنتجة من المخزون (القيمة سالبة للإشارة إلى الخصم)
        await _inventoryRepository.addPlasticPartToInventory(
          mold.id!,
          mold.name,
          mold.productCode,
          -partsProduced, // القيمة السالبة للإشارة إلى الخصم
          -(partsProduced * mold.singlePartWeight) / 1000, // وزن القطع بالكيلوجرام
          -rawMaterialUsed,
          rawMaterialId,
          'حذف إنتاج',
          null,
        );
      }

      // 4. إعادة المواد الخام المستخدمة إلى المخزون
      // البحث عن المادة الخام في المخزون
      final RawMaterial? rawMaterial = findRawMaterialById(rawMaterialId);
      if (rawMaterial != null) {
        // تحديث كمية المادة الخام في المخزون (إضافة الكمية المستخدمة)
        final updatedMaterial = rawMaterial.copyWith(
          availableQuantity: rawMaterial.availableQuantity + rawMaterialUsed,
        );

        // حفظ التغييرات
        await _rawMaterialRepository.updateRawMaterial(updatedMaterial);

        // تسجيل حركة المخزون
        final inventoryItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(rawMaterialId, 'raw_material');

        if (inventoryItem != null) {
          // تسجيل حركة إضافة إلى المخزون (القيمة موجبة للإشارة إلى الإضافة)
          await _inventoryRepository.updateInventoryQuantity(
            inventoryItem.id!,
            rawMaterialUsed, // القيمة موجبة للإشارة إلى الإضافة
            'حذف إنتاج',
            'production_delete',
            notes:
                'إعادة خامات من حذف إنتاج - إسطمبة: ${mold?.name ?? "غير معروف"}',
          );
        }
      }

      // 5. تحديث قائمة الإنتاج اليومي
      await fetchDailyProduction();

      // 6. تحديث قائمة المواد الخام
      await fetchRawMaterials();

      // إظهار رسالة نجاح
      Get.snackbar(
        'نجاح',
        'تم حذف الإنتاج بنجاح',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حذف الإنتاج: $e',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } finally {
      isLoadingHistory.value = false;
    }
  }

  // توليد تقرير PDF للإنتاج اليومي
  Future<void> generateProductionReport() async {
    try {
      // عنوان التقرير
      String reportTitle =
          'تقرير الإنتاج اليومي - ${formatDate(selectedDate.value)}';

      // توليد التقرير
      final file = await PdfGenerator.generateDailyProductionReport(
        selectedDate.value,
        dailyProduction,
        reportTitle,
      );

      // عرض رسالة نجاح
      Get.snackbar(
        'تم إنشاء التقرير',
        'يمكنك الآن عرض أو مشاركة التقرير',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 5),
        mainButton: TextButton(
          onPressed: () => PdfGenerator.sharePdf(file),
          child: const Text(
            'مشاركة',
            style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
          ),
        ),
      );

      // فتح نافذة طباعة التقرير
      await PdfGenerator.printPdf(file);
    } catch (e) {
      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إنشاء التقرير: $e',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}
