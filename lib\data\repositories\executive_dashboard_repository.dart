import 'package:flutter/foundation.dart';
import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:uuid/uuid.dart';

class ExecutiveDashboardRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  static const String tableKPIs = 'executive_kpis';
  static const String tableReports = 'executive_reports';
  static const String tableDashboardSettings = 'dashboard_settings';

  // إنشاء المستودع وضمان وجود الجداول
  ExecutiveDashboardRepository() {
    _initializeTables();
  }

  // تهيئة الجداول
  Future<void> _initializeTables() async {
    try {
      await createTables();
    } catch (e) {
      debugPrint('خطأ في تهيئة جداول لوحة التحكم التنفيذية: $e');
    }
  }

  // إنشاء الجداول المطلوبة
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    // جدول المؤشرات الرئيسية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableKPIs (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        currentValue REAL NOT NULL,
        targetValue REAL NOT NULL,
        previousValue REAL NOT NULL,
        type INTEGER NOT NULL,
        unit INTEGER NOT NULL,
        trend INTEGER NOT NULL,
        lastUpdated TEXT NOT NULL,
        icon TEXT,
        color TEXT
      )
    ''');

    // جدول التقارير التنفيذية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableReports (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        type INTEGER NOT NULL,
        period INTEGER NOT NULL,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        data TEXT NOT NULL,
        insights TEXT NOT NULL,
        recommendations TEXT NOT NULL,
        generatedAt TEXT NOT NULL,
        generatedBy TEXT NOT NULL
      )
    ''');

    // جدول إعدادات لوحة التحكم
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableDashboardSettings (
        id TEXT PRIMARY KEY,
        userId TEXT NOT NULL,
        settingKey TEXT NOT NULL,
        settingValue TEXT NOT NULL,
        lastUpdated TEXT NOT NULL
      )
    ''');

    // إنشاء الفهارس
    await _createIndexes(db);
  }

  // إنشاء الفهارس لتحسين الأداء
  Future<void> _createIndexes(db) async {
    // فهارس جدول المؤشرات
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_kpis_type ON $tableKPIs (type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_kpis_last_updated ON $tableKPIs (lastUpdated)',
    );

    // فهارس جدول التقارير
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_reports_type ON $tableReports (type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_reports_period ON $tableReports (period)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_reports_generated_at ON $tableReports (generatedAt)',
    );

    // فهارس جدول الإعدادات
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_settings_user ON $tableDashboardSettings (userId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_settings_key ON $tableDashboardSettings (settingKey)',
    );
  }

  // === طرق إدارة المؤشرات الرئيسية ===

  // حفظ أو تحديث مؤشر
  Future<String> saveKPI(KPI kpi) async {
    final kpiWithId = kpi.id.isEmpty ? 
      KPI(
        id: _uuid.v4(),
        name: kpi.name,
        description: kpi.description,
        currentValue: kpi.currentValue,
        targetValue: kpi.targetValue,
        previousValue: kpi.previousValue,
        type: kpi.type,
        unit: kpi.unit,
        trend: kpi.trend,
        lastUpdated: DateTime.now(),
        icon: kpi.icon,
        color: kpi.color,
      ) : kpi;

    final existingKPI = await getKPIById(kpiWithId.id);
    
    if (existingKPI != null) {
      await _databaseHelper.update(tableKPIs, kpiWithId.toMap());
    } else {
      await _databaseHelper.insert(tableKPIs, kpiWithId.toMap());
    }

    return kpiWithId.id;
  }

  // الحصول على مؤشر بالمعرف
  Future<KPI?> getKPIById(String id) async {
    final map = await _databaseHelper.queryRow(tableKPIs, id);
    if (map == null) return null;
    return KPI.fromMap(map);
  }

  // الحصول على جميع المؤشرات
  Future<List<KPI>> getAllKPIs() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableKPIs ORDER BY lastUpdated DESC',
    );
    return maps.map((map) => KPI.fromMap(map)).toList();
  }

  // الحصول على المؤشرات حسب النوع
  Future<List<KPI>> getKPIsByType(KPIType type) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableKPIs WHERE type = ? ORDER BY lastUpdated DESC',
      [type.index],
    );
    return maps.map((map) => KPI.fromMap(map)).toList();
  }

  // حذف مؤشر
  Future<int> deleteKPI(String id) async {
    return await _databaseHelper.delete(tableKPIs, id);
  }

  // === طرق إدارة التقارير التنفيذية ===

  // حفظ تقرير تنفيذي
  Future<String> saveExecutiveReport(ExecutiveReport report) async {
    final reportWithId = report.id.isEmpty ?
      ExecutiveReport(
        id: _uuid.v4(),
        title: report.title,
        description: report.description,
        type: report.type,
        period: report.period,
        startDate: report.startDate,
        endDate: report.endDate,
        data: report.data,
        insights: report.insights,
        recommendations: report.recommendations,
        generatedAt: DateTime.now(),
        generatedBy: report.generatedBy,
      ) : report;

    // تحويل البيانات المعقدة إلى JSON strings
    final reportMap = reportWithId.toMap();
    reportMap['data'] = _encodeJsonData(reportWithId.data);
    reportMap['insights'] = reportWithId.insights.join('|');
    reportMap['recommendations'] = reportWithId.recommendations.join('|');

    await _databaseHelper.insert(tableReports, reportMap);
    return reportWithId.id;
  }

  // الحصول على تقرير بالمعرف
  Future<ExecutiveReport?> getReportById(String id) async {
    final map = await _databaseHelper.queryRow(tableReports, id);
    if (map == null) return null;

    // تحويل البيانات من JSON strings
    map['data'] = _decodeJsonData(map['data']);
    map['insights'] = (map['insights'] as String).split('|');
    map['recommendations'] = (map['recommendations'] as String).split('|');

    return ExecutiveReport.fromMap(map);
  }

  // الحصول على جميع التقارير
  Future<List<ExecutiveReport>> getAllReports() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableReports ORDER BY generatedAt DESC',
    );
    
    return maps.map((map) {
      map['data'] = _decodeJsonData(map['data']);
      map['insights'] = (map['insights'] as String).split('|');
      map['recommendations'] = (map['recommendations'] as String).split('|');
      return ExecutiveReport.fromMap(map);
    }).toList();
  }

  // الحصول على التقارير حسب النوع
  Future<List<ExecutiveReport>> getReportsByType(ReportType type) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableReports WHERE type = ? ORDER BY generatedAt DESC',
      [type.index],
    );
    
    return maps.map((map) {
      map['data'] = _decodeJsonData(map['data']);
      map['insights'] = (map['insights'] as String).split('|');
      map['recommendations'] = (map['recommendations'] as String).split('|');
      return ExecutiveReport.fromMap(map);
    }).toList();
  }

  // الحصول على التقارير في فترة زمنية
  Future<List<ExecutiveReport>> getReportsInPeriod(DateTime startDate, DateTime endDate) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableReports WHERE generatedAt >= ? AND generatedAt <= ? ORDER BY generatedAt DESC',
      [startDate.toIso8601String(), endDate.toIso8601String()],
    );
    
    return maps.map((map) {
      map['data'] = _decodeJsonData(map['data']);
      map['insights'] = (map['insights'] as String).split('|');
      map['recommendations'] = (map['recommendations'] as String).split('|');
      return ExecutiveReport.fromMap(map);
    }).toList();
  }

  // حذف تقرير
  Future<int> deleteReport(String id) async {
    return await _databaseHelper.delete(tableReports, id);
  }

  // === طرق إدارة إعدادات لوحة التحكم ===

  // حفظ إعداد
  Future<void> saveDashboardSetting(String userId, String key, String value) async {
    final settingId = _uuid.v4();
    final setting = {
      'id': settingId,
      'userId': userId,
      'settingKey': key,
      'settingValue': value,
      'lastUpdated': DateTime.now().toIso8601String(),
    };

    // حذف الإعداد القديم إن وجد
    await _databaseHelper.rawQuery(
      'DELETE FROM $tableDashboardSettings WHERE userId = ? AND settingKey = ?',
      [userId, key],
    );

    // إدراج الإعداد الجديد
    await _databaseHelper.insert(tableDashboardSettings, setting);
  }

  // الحصول على إعداد
  Future<String?> getDashboardSetting(String userId, String key) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT settingValue FROM $tableDashboardSettings WHERE userId = ? AND settingKey = ?',
      [userId, key],
    );

    return maps.isNotEmpty ? maps.first['settingValue'] as String : null;
  }

  // الحصول على جميع إعدادات المستخدم
  Future<Map<String, String>> getAllUserSettings(String userId) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT settingKey, settingValue FROM $tableDashboardSettings WHERE userId = ?',
      [userId],
    );

    final settings = <String, String>{};
    for (var map in maps) {
      settings[map['settingKey'] as String] = map['settingValue'] as String;
    }

    return settings;
  }

  // === طرق مساعدة ===

  // تشفير البيانات المعقدة إلى JSON string
  String _encodeJsonData(Map<String, dynamic> data) {
    try {
      // تحويل بسيط للبيانات - يمكن تحسينه باستخدام json.encode
      return data.entries.map((e) => '${e.key}:${e.value}').join(',');
    } catch (e) {
      return '';
    }
  }

  // فك تشفير البيانات من JSON string
  Map<String, dynamic> _decodeJsonData(String jsonString) {
    try {
      final data = <String, dynamic>{};
      if (jsonString.isNotEmpty) {
        final pairs = jsonString.split(',');
        for (var pair in pairs) {
          final keyValue = pair.split(':');
          if (keyValue.length == 2) {
            data[keyValue[0]] = keyValue[1];
          }
        }
      }
      return data;
    } catch (e) {
      return {};
    }
  }

  // تنظيف البيانات القديمة
  Future<void> cleanupOldData({int daysToKeep = 90}) async {
    final cutoffDate = DateTime.now().subtract(Duration(days: daysToKeep));
    
    // حذف التقارير القديمة
    await _databaseHelper.rawQuery(
      'DELETE FROM $tableReports WHERE generatedAt < ?',
      [cutoffDate.toIso8601String()],
    );

    // حذف المؤشرات القديمة
    await _databaseHelper.rawQuery(
      'DELETE FROM $tableKPIs WHERE lastUpdated < ?',
      [cutoffDate.toIso8601String()],
    );
  }

  // الحصول على إحصائيات لوحة التحكم
  Future<Map<String, dynamic>> getDashboardStatistics() async {
    final db = await _databaseHelper.database;

    // عدد المؤشرات
    final kpiCount = await db.rawQuery('SELECT COUNT(*) as count FROM $tableKPIs');
    
    // عدد التقارير
    final reportCount = await db.rawQuery('SELECT COUNT(*) as count FROM $tableReports');
    
    // آخر تحديث للمؤشرات
    final lastKPIUpdate = await db.rawQuery(
      'SELECT MAX(lastUpdated) as lastUpdate FROM $tableKPIs',
    );
    
    // آخر تقرير تم إنشاؤه
    final lastReport = await db.rawQuery(
      'SELECT MAX(generatedAt) as lastReport FROM $tableReports',
    );

    return {
      'totalKPIs': (kpiCount.first['count'] as num).toInt(),
      'totalReports': (reportCount.first['count'] as num).toInt(),
      'lastKPIUpdate': lastKPIUpdate.first['lastUpdate'],
      'lastReportGenerated': lastReport.first['lastReport'],
    };
  }
}
