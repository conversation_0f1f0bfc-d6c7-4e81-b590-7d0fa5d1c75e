# إصلاح مشكلة JAVA_HOME في بناء تطبيق Flutter

## المشكلة

عند محاولة بناء تطبيق Flutter باستخدام الأمر `flutter build apk`، ظهرت الرسالة التالية:

```
FAILURE: Build failed with an exception.

* What went wrong:
Value '${JAVA_HOME}' given for org.gradle.java.home Gradle property is invalid (Java home supplied is invalid)
```

هذه المشكلة تحدث عندما يكون هناك إعداد غير صحيح لمتغير `JAVA_HOME` في ملف `gradle.properties`.

## الحل

تم تنفيذ الإصلاحات التالية:

### 1. تعديل ملف gradle.properties

تم تعليق السطر الذي يحدد `org.gradle.java.home` في ملف `android/gradle.properties`:

```properties
# تعليق هذا السطر لأنه يسبب مشكلة
# org.gradle.java.home=${JAVA_HOME}
```

### 2. إضافة مسار JDK إلى ملف local.properties

تم إضافة مسار JDK إلى ملف `android/local.properties`:

```properties
# إضافة مسار JDK
java.home=C:\\Program Files\\Java\\jdk-17
```

## لماذا حدثت المشكلة؟

المشكلة حدثت لأن القيمة `${JAVA_HOME}` في ملف `gradle.properties` لم يتم استبدالها بالمسار الفعلي لـ JDK على الجهاز. هذا يمكن أن يحدث لعدة أسباب:

1. متغير البيئة `JAVA_HOME` غير محدد على الجهاز.
2. Gradle لا يستطيع الوصول إلى متغيرات البيئة بهذه الطريقة في ملف `gradle.properties`.
3. صيغة `${JAVA_HOME}` غير مدعومة في ملف `gradle.properties`.

## كيفية تجنب هذه المشكلة في المستقبل

هناك عدة طرق لتجنب هذه المشكلة في المستقبل:

### 1. استخدام ملف local.properties

ملف `local.properties` هو المكان المناسب لتحديد المسارات الخاصة بالجهاز مثل SDK و JDK. يمكن إضافة مسار JDK إلى هذا الملف كما تم في الحل أعلاه.

### 2. تعيين متغير البيئة JAVA_HOME

يمكن تعيين متغير البيئة `JAVA_HOME` على الجهاز بشكل صحيح، ثم استخدام الأمر التالي في ملف `gradle.properties`:

```properties
org.gradle.java.home=C:\\Program Files\\Java\\jdk-17
```

### 3. استخدام gradle.properties المحلي

يمكن إنشاء ملف `gradle.properties` محلي في مجلد `~/.gradle/` وتحديد مسار JDK فيه:

```properties
org.gradle.java.home=C:\\Program Files\\Java\\jdk-17
```

## ملاحظات إضافية

- تأكد من تثبيت JDK 17 على جهازك، حيث أن Flutter يتطلب JDK 17 أو أحدث.
- يمكن التحقق من إصدار JDK المثبت باستخدام الأمر `java -version`.
- إذا كنت تستخدم نظام تشغيل مختلف (مثل Linux أو macOS)، فستحتاج إلى تعديل مسار JDK وفقًا لذلك.

## المراجع

- [Gradle Properties](https://docs.gradle.org/current/userguide/build_environment.html#sec:gradle_configuration_properties)
- [Flutter Android Setup](https://flutter.dev/docs/get-started/install/windows#android-setup)
- [Java Development Kit](https://www.oracle.com/java/technologies/downloads/)
