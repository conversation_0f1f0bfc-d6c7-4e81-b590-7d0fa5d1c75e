class Sale {
  final String? id;
  final String invoiceNumber;
  final DateTime date;
  final String customerName;
  final String customerPhone;
  final String shippingAddress;
  final List<SaleItem> items;
  final double totalAmount;
  final String shippingStatus; // قيد التجهيز، قيد الشحن، تم التسليم، ملغي

  Sale({
    this.id,
    required this.invoiceNumber,
    required this.date,
    required this.customerName,
    required this.customerPhone,
    required this.shippingAddress,
    required this.items,
    required this.totalAmount,
    required this.shippingStatus,
  });

  // تحويل Sale إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'invoiceNumber': invoiceNumber,
      'date': date.toIso8601String(),
      'customerName': customerName,
      'customerPhone': customerPhone,
      'shippingAddress': shippingAddress,
      'totalAmount': totalAmount,
      'shippingStatus': shippingStatus,
    };
  }

  // إنشاء Sale من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Sale.fromMap(Map<String, dynamic> map, List<SaleItem> items) {
    return Sale(
      id: map['id'],
      invoiceNumber: map['invoiceNumber'],
      date: DateTime.parse(map['date']),
      customerName: map['customerName'],
      customerPhone: map['customerPhone'],
      shippingAddress: map['shippingAddress'],
      items: items,
      totalAmount: map['totalAmount'],
      shippingStatus: map['shippingStatus'],
    );
  }
}

class SaleItem {
  final String? id;
  final String? saleId;
  final String productId;
  final String productName;
  final int quantity;
  final double unitPrice;
  final double totalPrice;

  SaleItem({
    this.id,
    this.saleId,
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.unitPrice,
    required this.totalPrice,
  });

  // تحويل SaleItem إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'saleId': saleId,
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'unitPrice': unitPrice,
      'totalPrice': totalPrice,
    };
  }

  // إنشاء SaleItem من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory SaleItem.fromMap(Map<String, dynamic> map) {
    return SaleItem(
      id: map['id'],
      saleId: map['saleId'],
      productId: map['productId'],
      productName: map['productName'],
      quantity: map['quantity'],
      unitPrice: map['unitPrice'],
      totalPrice: map['totalPrice'],
    );
  }
}
