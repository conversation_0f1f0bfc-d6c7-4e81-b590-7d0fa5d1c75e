class Mold {
  final String? id;
  final String name;
  final String productCode; // كود المنتج المرتبط بالإسطمبة
  final String status; // حالة الإسطمبة (متاحة، قيد الاستخدام، صيانة)
  final String? currentMachineId; // الماكينة المركبة عليها حالياً
  final int cavityCount; // عدد التجاويف في الإسطمبة
  final double singlePartWeight; // وزن القطعة الواحدة بالجرام
  final DateTime createdAt; // تاريخ إنشاء الإسطمبة

  Mold({
    this.id,
    required this.name,
    required this.productCode,
    required this.status,
    this.currentMachineId,
    required this.cavityCount,
    required this.singlePartWeight,
    required this.createdAt,
  });

  // تحويل Mold إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'productCode': productCode,
      'status': status,
      'currentMachineId': currentMachineId,
      'cavityCount': cavityCount,
      'singlePartWeight': singlePartWeight,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // إنشاء Mold من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Mold.fromMap(Map<String, dynamic> map) {
    return Mold(
      id: map['id'],
      name: map['name'],
      productCode: map['productCode'],
      status: map['status'],
      currentMachineId: map['currentMachineId'],
      cavityCount: map['cavityCount'],
      singlePartWeight: map['singlePartWeight'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  // إنشاء نسخة معدلة من Mold
  Mold copyWith({
    String? id,
    String? name,
    String? productCode,
    String? status,
    String? currentMachineId,
    int? cavityCount,
    double? singlePartWeight,
    DateTime? createdAt,
  }) {
    return Mold(
      id: id ?? this.id,
      name: name ?? this.name,
      productCode: productCode ?? this.productCode,
      status: status ?? this.status,
      currentMachineId: currentMachineId ?? this.currentMachineId,
      cavityCount: cavityCount ?? this.cavityCount,
      singlePartWeight: singlePartWeight ?? this.singlePartWeight,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

// حالات الإسطمبة
class MoldStatus {
  static const String available = 'متاحة';
  static const String inUse = 'قيد الاستخدام';
  static const String maintenance = 'صيانة';
}
