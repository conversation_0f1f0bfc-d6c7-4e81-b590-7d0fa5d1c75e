import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/data/repositories/injection_cost_repository.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/data/models/injection_cost_result.dart';

class InjectionCostController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;

  // القيم المدخلة
  final RxString selectedMachineId = RxString('');
  final RxString selectedMoldId = RxString('');
  final RxString selectedMaterialId = RxString('');

  // الماكينات والإسطمبات والمواد الخام
  final RxList<Machine> machines = <Machine>[].obs;
  final RxList<Mold> molds = <Mold>[].obs;
  final RxList<RawMaterial> materials = <RawMaterial>[].obs;

  // بيانات حساب التكلفة
  final RxDouble electricityCostPerHour = 0.0.obs;
  final RxDouble operatorCostPerHour = 0.0.obs;
  final RxDouble overheadCostPerHour = 0.0.obs;
  final RxDouble maintenanceCostPerHour = 0.0.obs;
  final RxDouble cycleTimeInSeconds = 0.0.obs;
  final RxInt cavityCount = 1.obs;
  final RxDouble partWeightInGrams = 0.0.obs;
  final RxDouble materialCostPerKg = 0.0.obs;
  final RxDouble scrapRate = 0.0.obs; // نسبة الهالك (٪)
  final RxDouble setupTimeInMinutes = 0.0.obs;
  final RxInt batchSize = 1000.obs; // حجم الدفعة

  // نتائج الحساب
  final RxDouble totalMaterialCost = 0.0.obs;
  final RxDouble totalElectricityCost = 0.0.obs;
  final RxDouble totalOperatorCost = 0.0.obs;
  final RxDouble totalOverheadCost = 0.0.obs;
  final RxDouble totalMaintenanceCost = 0.0.obs;
  final RxDouble totalSetupCost = 0.0.obs;
  final RxDouble costPerPart = 0.0.obs;
  final RxDouble productionTimeInHours = 0.0.obs;
  final RxInt partsPerHour = 0.obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([loadMachines(), loadMolds(), loadMaterials()]);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل الماكينات
  Future<void> loadMachines() async {
    final machinesList = await _machineRepository.getAllMachines();
    machines.assignAll(machinesList);
  }

  // تحميل الإسطمبات
  Future<void> loadMolds() async {
    final moldsList = await _moldRepository.getAllMolds();
    molds.assignAll(moldsList);
  }

  // تحميل المواد الخام
  Future<void> loadMaterials() async {
    final materialsList = await _rawMaterialRepository.getAllRawMaterials();
    materials.assignAll(materialsList);
  }

  // تحديث قيم الإدخال عند اختيار ماكينة
  void onMachineSelected(String machineId) {
    selectedMachineId.value = machineId;
    final machine = machines.firstWhere(
      (m) => m.id == machineId,
      orElse:
          () => Machine(
            name: '',
            model: '',
            powerConsumption: 0,
            status: MachineStatus.stopped,
          ),
    );

    // تحديث التكاليف بناءً على الماكينة المختارة
    electricityCostPerHour.value =
        machine.powerConsumption * 1.5; // سعر الكيلوواط * ساعة
    operatorCostPerHour.value = 25.0; // تكلفة المشغل في الساعة
    // تكلفة الصيانة والنفقات الإضافية معتمدة على حجم الماكينة
    final maintenanceRate = machine.powerConsumption > 100 ? 0.2 : 0.15;
    final overheadRate = machine.powerConsumption > 100 ? 0.3 : 0.25;
    maintenanceCostPerHour.value = machine.powerConsumption * maintenanceRate;
    overheadCostPerHour.value = machine.powerConsumption * overheadRate;
  }

  // تحديث قيم الإدخال عند اختيار إسطمبة
  void onMoldSelected(String moldId) {
    selectedMoldId.value = moldId;
    final mold = molds.firstWhere(
      (m) => m.id == moldId,
      orElse:
          () => Mold(
            name: '',
            productCode: '',
            status: MoldStatus.available,
            cavityCount: 1,
            singlePartWeight: 0,
            createdAt: DateTime.now(),
          ),
    );

    // تحديث القيم بناءً على الإسطمبة المختارة
    cavityCount.value = mold.cavityCount;
    partWeightInGrams.value = mold.singlePartWeight;
    setupTimeInMinutes.value = 30; // وقت افتراضي لتجهيز الإسطمبة
  }

  // تحديث قيم الإدخال عند اختيار مادة خام
  void onMaterialSelected(String materialId) {
    selectedMaterialId.value = materialId;
    final material = materials.firstWhere(
      (m) => m.id == materialId,
      orElse:
          () => RawMaterial(
            name: '',
            code: '',
            color: '',
            costPerKg: 0,
            availableQuantity: 0,
          ),
    );

    // تحديث التكلفة بناءً على المادة الخام المختارة
    materialCostPerKg.value = material.costPerKg;
  }

  // حساب التكلفة
  void calculateCost() {
    try {
      if (selectedMachineId.value.isEmpty ||
          selectedMoldId.value.isEmpty ||
          selectedMaterialId.value.isEmpty) {
        _showError('الرجاء اختيار الماكينة والإسطمبة والمادة الخام');
        return;
      }

      if (partWeightInGrams.value <= 0 ||
          materialCostPerKg.value <= 0 ||
          cycleTimeInSeconds.value <= 0) {
        _showError('الرجاء إدخال جميع البيانات المطلوبة بشكل صحيح');
        return;
      }

      // حساب تكلفة المواد الخام
      // تحويل الجرام إلى كيلوجرام (قسمة على 1000)
      final partWeightKg = partWeightInGrams.value / 1000;
      // حساب تكلفة المادة الخام للقطعة الواحدة مع احتساب الهالك
      final materialCostPerPart =
          partWeightKg *
          materialCostPerKg.value *
          (1 + (scrapRate.value / 100));
      totalMaterialCost.value = materialCostPerPart;

      // حساب الوقت
      // عدد القطع في الدورة الواحدة = عدد التجاويف
      final partsPerCycle = cavityCount.value;
      // عدد القطع في الساعة = (3600 ثانية / وقت الدورة) * عدد التجاويف
      partsPerHour.value =
          ((3600 / cycleTimeInSeconds.value) * partsPerCycle).round();

      // وقت الإنتاج بالساعات = حجم الدفعة / عدد القطع في الساعة
      productionTimeInHours.value = batchSize.value / partsPerHour.value;

      // تكلفة الكهرباء للقطعة الواحدة = (تكلفة الكهرباء / ساعة) / عدد القطع في الساعة
      totalElectricityCost.value =
          electricityCostPerHour.value / partsPerHour.value;

      // تكلفة المشغل للقطعة الواحدة = (تكلفة المشغل / ساعة) / عدد القطع في الساعة
      totalOperatorCost.value = operatorCostPerHour.value / partsPerHour.value;

      // تكلفة النفقات الإضافية للقطعة الواحدة = (تكلفة النفقات / ساعة) / عدد القطع في الساعة
      totalOverheadCost.value = overheadCostPerHour.value / partsPerHour.value;

      // تكلفة الصيانة للقطعة الواحدة = (تكلفة الصيانة / ساعة) / عدد القطع في الساعة
      totalMaintenanceCost.value =
          maintenanceCostPerHour.value / partsPerHour.value;

      // تكلفة الإعداد للقطعة الواحدة = (تكلفة الإعداد / ساعة * وقت الإعداد / 60) / حجم الدفعة
      final setupCostPerBatch =
          (operatorCostPerHour.value * (setupTimeInMinutes.value / 60));
      totalSetupCost.value = setupCostPerBatch / batchSize.value;

      // إجمالي تكلفة القطعة الواحدة
      costPerPart.value =
          totalMaterialCost.value +
          totalElectricityCost.value +
          totalOperatorCost.value +
          totalOverheadCost.value +
          totalMaintenanceCost.value +
          totalSetupCost.value;

      // عرض النتائج
      _showResults();
    } catch (e) {
      _showError('حدث خطأ أثناء حساب التكلفة: $e');
    }
  }

  // عرض نتائج الحساب
  void _showResults() {
    Get.dialog(
      AlertDialog(
        title: const Text('نتائج حساب تكلفة الحقن'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildResultItem(
                'تكلفة المادة الخام للقطعة:',
                '${totalMaterialCost.value.toStringAsFixed(3)} جنيه',
              ),
              _buildResultItem(
                'تكلفة الكهرباء للقطعة:',
                '${totalElectricityCost.value.toStringAsFixed(3)} جنيه',
              ),
              _buildResultItem(
                'تكلفة المشغل للقطعة:',
                '${totalOperatorCost.value.toStringAsFixed(3)} جنيه',
              ),
              _buildResultItem(
                'تكلفة النفقات الإضافية للقطعة:',
                '${totalOverheadCost.value.toStringAsFixed(3)} جنيه',
              ),
              _buildResultItem(
                'تكلفة الصيانة للقطعة:',
                '${totalMaintenanceCost.value.toStringAsFixed(3)} جنيه',
              ),
              _buildResultItem(
                'تكلفة الإعداد للقطعة:',
                '${totalSetupCost.value.toStringAsFixed(3)} جنيه',
              ),
              const Divider(),
              _buildResultItem(
                'إجمالي تكلفة القطعة:',
                '${costPerPart.value.toStringAsFixed(3)} جنيه',
                isTotal: true,
              ),
              const Divider(),
              _buildResultItem(
                'وقت إنتاج الدفعة:',
                '${productionTimeInHours.value.toStringAsFixed(2)} ساعة',
              ),
              _buildResultItem(
                'عدد القطع في الساعة:',
                '${partsPerHour.value} قطعة',
              ),
            ],
          ),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
          TextButton(onPressed: _saveResults, child: const Text('حفظ')),
        ],
      ),
    );
  }

  // بناء عنصر نتيجة
  Widget _buildResultItem(String label, String value, {bool isTotal = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              fontSize: isTotal ? 16 : 14,
              color: isTotal ? Colors.blue.shade800 : Colors.black,
            ),
          ),
        ],
      ),
    );
  }

  // حفظ نتائج الحساب
  Future<void> _saveResults() async {
    try {
      final repository = Get.find<InjectionCostRepository>();

      // الحصول على أسماء العناصر المختارة
      final machine = machines.firstWhere(
        (m) => m.id == selectedMachineId.value,
      );
      final mold = molds.firstWhere((m) => m.id == selectedMoldId.value);
      final material = materials.firstWhere(
        (m) => m.id == selectedMaterialId.value,
      );

      // إنشاء كائن نتيجة الحساب
      final result = InjectionCostResult(
        machineId: selectedMachineId.value,
        machineName: machine.name,
        moldId: selectedMoldId.value,
        moldName: mold.name,
        materialId: selectedMaterialId.value,
        materialName: material.name,
        cycleTimeInSeconds: cycleTimeInSeconds.value,
        cavityCount: cavityCount.value,
        partWeightInGrams: partWeightInGrams.value,
        materialCostPerKg: materialCostPerKg.value,
        scrapRate: scrapRate.value,
        setupTimeInMinutes: setupTimeInMinutes.value,
        batchSize: batchSize.value,
        electricityCostPerHour: electricityCostPerHour.value,
        operatorCostPerHour: operatorCostPerHour.value,
        overheadCostPerHour: overheadCostPerHour.value,
        maintenanceCostPerHour: maintenanceCostPerHour.value,
        totalMaterialCost: totalMaterialCost.value,
        totalElectricityCost: totalElectricityCost.value,
        totalOperatorCost: totalOperatorCost.value,
        totalOverheadCost: totalOverheadCost.value,
        totalMaintenanceCost: totalMaintenanceCost.value,
        totalSetupCost: totalSetupCost.value,
        costPerPart: costPerPart.value,
        productionTimeInHours: productionTimeInHours.value,
        partsPerHour: partsPerHour.value,
        calculationDate: DateTime.now(),
      );

      // حفظ النتيجة في قاعدة البيانات
      await repository.saveInjectionCostResult(result);

      Get.back();
      Get.snackbar(
        'تم الحفظ',
        'تم حفظ نتائج حساب التكلفة بنجاح',
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      _showError('فشل في حفظ النتائج: $e');
    }
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
