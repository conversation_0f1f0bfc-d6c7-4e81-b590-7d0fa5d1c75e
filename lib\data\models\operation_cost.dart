/// نموذج بيانات تكاليف التشغيل
class OperationCost {
  final int? id;
  final String name; // اسم عنصر التكلفة
  final String category; // فئة التكلفة (كهرباء، عمالة، صيانة، نفقات إضافية)
  final double value; // قيمة التكلفة
  final String unit; // وحدة القياس (جنيه/ساعة، جنيه/كيلوواط، نسبة مئوية)
  final bool isDefault; // هل هي القيمة الافتراضية
  final DateTime lastUpdate; // تاريخ آخر تحديث

  OperationCost({
    this.id,
    required this.name,
    required this.category,
    required this.value,
    required this.unit,
    required this.isDefault,
    required this.lastUpdate,
  });

  // تحويل OperationCost إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'name': name,
      'category': category,
      'value': value,
      'unit': unit,
      'isDefault': isDefault ? 1 : 0,
      'lastUpdate': lastUpdate.toIso8601String(),
    };
  }

  // إنشاء OperationCost من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory OperationCost.fromMap(Map<String, dynamic> map) {
    return OperationCost(
      id: map['id'],
      name: map['name'],
      category: map['category'],
      value: map['value'],
      unit: map['unit'],
      isDefault: map['isDefault'] == 1,
      lastUpdate: DateTime.parse(map['lastUpdate']),
    );
  }

  // إنشاء نسخة معدلة من OperationCost
  OperationCost copyWith({
    int? id,
    String? name,
    String? category,
    double? value,
    String? unit,
    bool? isDefault,
    DateTime? lastUpdate,
  }) {
    return OperationCost(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      value: value ?? this.value,
      unit: unit ?? this.unit,
      isDefault: isDefault ?? this.isDefault,
      lastUpdate: lastUpdate ?? this.lastUpdate,
    );
  }
}

// فئات التكاليف
class CostCategory {
  static const String electricity = 'كهرباء';
  static const String labor = 'عمالة';
  static const String maintenance = 'صيانة';
  static const String overhead = 'نفقات إضافية';
}

// وحدات القياس
class CostUnit {
  static const String perHour = 'جنيه/ساعة';
  static const String perKw = 'جنيه/كيلوواط';
  static const String percentage = '%';
}
