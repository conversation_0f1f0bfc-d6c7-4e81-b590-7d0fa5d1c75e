import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine_capacity.dart';
import 'package:mostafa_final/data/models/smart_schedule.dart';
import 'package:mostafa_final/data/repositories/smart_planning_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/services/smart_planning_service.dart';

class SmartPlanningController extends GetxController {
  final SmartPlanningRepository _planningRepository = Get.find<SmartPlanningRepository>();
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final InventoryRepository _inventoryRepository = Get.find<InventoryRepository>();
  
  late final SmartPlanningService _planningService;

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isGeneratingSchedule = false.obs;

  // البيانات
  final RxList<PendingOrder> pendingOrders = <PendingOrder>[].obs;
  final RxList<MachineCapacity> machineCapacities = <MachineCapacity>[].obs;
  final RxList<ScheduleAlert> alerts = <ScheduleAlert>[].obs;
  final Rx<SmartSchedule?> currentSchedule = Rx<SmartSchedule?>(null);

  // إعدادات الجدولة
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final RxDouble shiftsCount = 1.0.obs;

  // إحصائيات الأداء
  final RxMap<String, dynamic> performanceStats = <String, dynamic>{}.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeService();
    loadInitialData();
  }

  void _initializeService() {
    _planningService = SmartPlanningService(
      planningRepository: _planningRepository,
      machineRepository: _machineRepository,
      moldRepository: _moldRepository,
      inventoryRepository: _inventoryRepository,
    );
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      // إنشاء الجداول إذا لم تكن موجودة
      await _planningRepository.createTables();
      
      // تحميل البيانات
      await Future.wait([
        loadPendingOrders(),
        loadMachineCapacities(),
        loadAlerts(),
        loadCurrentSchedule(),
        loadPerformanceStats(),
      ]);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل الطلبيات المعلقة
  Future<void> loadPendingOrders() async {
    try {
      final orders = await _planningRepository.getPendingOrders();
      pendingOrders.assignAll(orders);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل الطلبيات المعلقة: $e');
    }
  }

  // تحميل الطاقات الإنتاجية
  Future<void> loadMachineCapacities() async {
    try {
      final capacities = await _planningRepository.getAllMachineCapacities();
      machineCapacities.assignAll(capacities);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل الطاقات الإنتاجية: $e');
    }
  }

  // تحميل التنبيهات
  Future<void> loadAlerts() async {
    try {
      final unreadAlerts = await _planningRepository.getUnreadAlerts();
      alerts.assignAll(unreadAlerts);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل التنبيهات: $e');
    }
  }

  // تحميل الجدولة الحالية
  Future<void> loadCurrentSchedule() async {
    try {
      final schedule = await _planningRepository.getActiveSchedule();
      currentSchedule.value = schedule;
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل الجدولة الحالية: $e');
    }
  }

  // تحميل إحصائيات الأداء
  Future<void> loadPerformanceStats() async {
    try {
      final stats = await _planningService.getPerformanceStats();
      performanceStats.assignAll(stats);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل إحصائيات الأداء: $e');
    }
  }

  // إنشاء جدولة ذكية جديدة
  Future<void> generateSmartSchedule() async {
    isGeneratingSchedule.value = true;
    try {
      final schedule = await _planningService.generateSmartSchedule(
        selectedDate.value,
        shiftsCount: shiftsCount.value,
      );
      
      currentSchedule.value = schedule;
      
      // إعادة تحميل التنبيهات
      await loadAlerts();
      
      _showSuccess('تم إنشاء الجدولة الذكية بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إنشاء الجدولة الذكية: $e');
    } finally {
      isGeneratingSchedule.value = false;
    }
  }

  // إضافة طلبية معلقة جديدة
  Future<void> addPendingOrder(
    String productCode,
    String productName,
    int requiredQuantity,
    DateTime dueDate,
    int priority,
    String? customerName,
  ) async {
    try {
      final order = PendingOrder(
        productCode: productCode,
        productName: productName,
        requiredQuantity: requiredQuantity,
        dueDate: dueDate,
        priority: priority,
        customerName: customerName,
        createdAt: DateTime.now(),
      );

      await _planningRepository.createPendingOrder(order);
      await loadPendingOrders();
      
      _showSuccess('تمت إضافة الطلبية بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة الطلبية: $e');
    }
  }

  // تحديث تقدم الطلبية
  Future<void> updateOrderProgress(String orderId, int completedQuantity) async {
    try {
      await _planningRepository.updateOrderProgress(orderId, completedQuantity);
      await loadPendingOrders();
      
      _showSuccess('تم تحديث تقدم الطلبية بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث تقدم الطلبية: $e');
    }
  }

  // تحديد التنبيه كمقروء
  Future<void> markAlertAsRead(String alertId) async {
    try {
      await _planningRepository.markAlertAsRead(alertId);
      await loadAlerts();
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث التنبيه: $e');
    }
  }

  // تغيير التاريخ المحدد
  void changeSelectedDate(DateTime newDate) {
    selectedDate.value = newDate;
  }

  // تغيير عدد الورديات
  void changeShiftsCount(double newShiftsCount) {
    shiftsCount.value = newShiftsCount;
  }

  // عرض نافذة إضافة طلبية جديدة
  void showAddOrderDialog() {
    final productCodeController = TextEditingController();
    final productNameController = TextEditingController();
    final quantityController = TextEditingController();
    final customerController = TextEditingController();
    final selectedDueDate = DateTime.now().add(const Duration(days: 7)).obs;
    final selectedPriority = OrderPriority.medium.obs;

    Get.dialog(
      AlertDialog(
        title: const Text('إضافة طلبية جديدة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: productCodeController,
                decoration: const InputDecoration(
                  labelText: 'كود المنتج',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: productNameController,
                decoration: const InputDecoration(
                  labelText: 'اسم المنتج',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: quantityController,
                decoration: const InputDecoration(
                  labelText: 'الكمية المطلوبة',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              const SizedBox(height: 16),
              TextField(
                controller: customerController,
                decoration: const InputDecoration(
                  labelText: 'اسم العميل (اختياري)',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Obx(
                () => ListTile(
                  title: const Text('تاريخ التسليم'),
                  subtitle: Text('${selectedDueDate.value.day}/${selectedDueDate.value.month}/${selectedDueDate.value.year}'),
                  trailing: const Icon(Icons.calendar_today),
                  onTap: () async {
                    final date = await showDatePicker(
                      context: Get.context!,
                      initialDate: selectedDueDate.value,
                      firstDate: DateTime.now(),
                      lastDate: DateTime.now().add(const Duration(days: 365)),
                    );
                    if (date != null) {
                      selectedDueDate.value = date;
                    }
                  },
                ),
              ),
              const SizedBox(height: 16),
              Obx(
                () => DropdownButtonFormField<int>(
                  decoration: const InputDecoration(
                    labelText: 'الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedPriority.value,
                  items: [
                    DropdownMenuItem(
                      value: OrderPriority.high,
                      child: Text(OrderPriority.getPriorityName(OrderPriority.high)),
                    ),
                    DropdownMenuItem(
                      value: OrderPriority.medium,
                      child: Text(OrderPriority.getPriorityName(OrderPriority.medium)),
                    ),
                    DropdownMenuItem(
                      value: OrderPriority.low,
                      child: Text(OrderPriority.getPriorityName(OrderPriority.low)),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      selectedPriority.value = value;
                    }
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final productCode = productCodeController.text.trim();
              final productName = productNameController.text.trim();
              final quantityText = quantityController.text.trim();
              final customer = customerController.text.trim();

              if (productCode.isEmpty || productName.isEmpty || quantityText.isEmpty) {
                _showError('الرجاء إدخال جميع البيانات المطلوبة');
                return;
              }

              final quantity = int.tryParse(quantityText);
              if (quantity == null || quantity <= 0) {
                _showError('الرجاء إدخال كمية صحيحة');
                return;
              }

              addPendingOrder(
                productCode,
                productName,
                quantity,
                selectedDueDate.value,
                selectedPriority.value,
                customer.isEmpty ? null : customer,
              );

              Get.back();
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // عرض رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
