enum ComponentType { plasticPart, accessory }

class Product {
  final String? id;
  final String name;
  final String code;
  final List<ProductComponent>
  components; // مكونات المنتج (أجزاء بلاستيكية وإكسسوارات)
  final double assemblyTime; // وقت التجميع بالدقائق
  final double packagingCost; // تكلفة التغليف

  Product({
    this.id,
    required this.name,
    required this.code,
    required this.components,
    required this.assemblyTime,
    required this.packagingCost,
  });

  // حساب التكلفة الكلية للمنتج
  double calculateTotalCost(double laborCostPerMinute) {
    double accessoriesCost = components
        .where((c) => c.type == ComponentType.accessory)
        .fold(0, (sum, comp) => sum + comp.cost * comp.quantity);

    double injectionCost = components
        .where((c) => c.type == ComponentType.plasticPart)
        .fold(0, (sum, comp) => sum + comp.cost * comp.quantity);

    double assemblyCost = assemblyTime * laborCostPerMinute;

    return accessoriesCost + injectionCost + assemblyCost + packagingCost;
  }

  // تحويل Product إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'assemblyTime': assemblyTime,
      'packagingCost': packagingCost,
    };
  }

  // إنشاء Product من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Product.fromMap(
    Map<String, dynamic> map,
    List<ProductComponent> components,
  ) {
    return Product(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      components: components,
      assemblyTime: map['assemblyTime'],
      packagingCost: map['packagingCost'],
    );
  }
}

class ProductComponent {
  final String? id;
  final String? productId;
  final String name;
  final String
  partId; // معرّف الجزء (إما إكسسوار مستورد أو جزء بلاستيكي منتج محلياً)
  final ComponentType type; // نوع المكون (جزء بلاستيكي أو إكسسوار)
  final int quantity; // عدد القطع المطلوبة في المنتج الواحد
  final double cost; // تكلفة القطعة الواحدة

  ProductComponent({
    this.id,
    this.productId,
    required this.name,
    required this.partId,
    required this.type,
    required this.quantity,
    required this.cost,
  });

  // تحويل ProductComponent إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'name': name,
      'partId': partId,
      'type': type.index,
      'quantity': quantity,
      'cost': cost,
    };
  }

  // إنشاء ProductComponent من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory ProductComponent.fromMap(Map<String, dynamic> map) {
    return ProductComponent(
      id: map['id'],
      productId: map['productId'],
      name: map['name'],
      partId: map['partId'],
      type: ComponentType.values[map['type']],
      quantity: map['quantity'],
      cost: map['cost'],
    );
  }
}
