// نوع الحركة (وارد، صادر)
enum MovementType { incoming, outgoing }

// حالة المخزون
enum InventoryStatus { normal, low, critical }

// نموذج المخزون الرئيسي
class Inventory {
  final String? id;
  final String itemId; // معرف العنصر (مادة خام، منتج نهائي)
  final String itemType; // نوع العنصر (مادة خام، منتج نهائي)
  final String itemName; // اسم العنصر
  final String itemCode; // كود العنصر
  final double currentQuantity; // الكمية الحالية
  final double minimumLevel; // الحد الأدنى للمخزون
  final double reorderLevel; // مستوى إعادة الطلب
  final String unit; // وحدة القياس (كجم، قطعة)
  final double unitCost; // تكلفة الوحدة
  final DateTime lastUpdate; // آخر تحديث
  final InventoryStatus status; // حالة المخزون

  Inventory({
    this.id,
    required this.itemId,
    required this.itemType,
    required this.itemName,
    required this.itemCode,
    required this.currentQuantity,
    required this.minimumLevel,
    required this.reorderLevel,
    required this.unit,
    required this.unitCost,
    required this.lastUpdate,
    required this.status,
  });

  // إنشاء نسخة معدلة من Inventory
  Inventory copyWith({
    String? id,
    String? itemId,
    String? itemType,
    String? itemName,
    String? itemCode,
    double? currentQuantity,
    double? minimumLevel,
    double? reorderLevel,
    String? unit,
    double? unitCost,
    DateTime? lastUpdate,
    InventoryStatus? status,
  }) {
    return Inventory(
      id: id ?? this.id,
      itemId: itemId ?? this.itemId,
      itemType: itemType ?? this.itemType,
      itemName: itemName ?? this.itemName,
      itemCode: itemCode ?? this.itemCode,
      currentQuantity: currentQuantity ?? this.currentQuantity,
      minimumLevel: minimumLevel ?? this.minimumLevel,
      reorderLevel: reorderLevel ?? this.reorderLevel,
      unit: unit ?? this.unit,
      unitCost: unitCost ?? this.unitCost,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      status: status ?? this.status,
    );
  }

  // تحويل Inventory إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'itemId': itemId,
      'itemType': itemType,
      'itemName': itemName,
      'itemCode': itemCode,
      'currentQuantity': currentQuantity,
      'minimumLevel': minimumLevel,
      'reorderLevel': reorderLevel,
      'unit': unit,
      'unitCost': unitCost,
      'lastUpdate': lastUpdate.toIso8601String(),
      'status': status.index,
    };
  }

  // إنشاء Inventory من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Inventory.fromMap(Map<String, dynamic> map) {
    return Inventory(
      id: map['id'],
      itemId: map['itemId'],
      itemType: map['itemType'],
      itemName: map['itemName'],
      itemCode: map['itemCode'],
      currentQuantity: map['currentQuantity'],
      minimumLevel: map['minimumLevel'],
      reorderLevel: map['reorderLevel'],
      unit: map['unit'],
      unitCost: map['unitCost'],
      lastUpdate: DateTime.parse(map['lastUpdate']),
      status: InventoryStatus.values[map['status']],
    );
  }
}

// نموذج حركة المخزون
class InventoryMovement {
  final String? id;
  final String inventoryId; // معرف المخزون
  final DateTime date; // تاريخ الحركة
  final MovementType type; // نوع الحركة (وارد، صادر)
  final double quantity; // الكمية
  final double costPerUnit; // تكلفة الوحدة
  final String referenceType; // نوع المرجع (إنتاج، طلبية، مرتجع)
  final String referenceId; // معرف المرجع
  final String notes; // ملاحظات

  InventoryMovement({
    this.id,
    required this.inventoryId,
    required this.date,
    required this.type,
    required this.quantity,
    required this.costPerUnit,
    required this.referenceType,
    required this.referenceId,
    required this.notes,
  });

  // إجمالي تكلفة الحركة
  double get totalCost => quantity * costPerUnit;

  // تحويل InventoryMovement إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'inventoryId': inventoryId,
      'date': date.toIso8601String(),
      'type': type.index,
      'quantity': quantity,
      'costPerUnit': costPerUnit,
      'referenceType': referenceType,
      'referenceId': referenceId,
      'notes': notes,
    };
  }

  // إنشاء InventoryMovement من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory InventoryMovement.fromMap(Map<String, dynamic> map) {
    return InventoryMovement(
      id: map['id'],
      inventoryId: map['inventoryId'],
      date: DateTime.parse(map['date']),
      type: MovementType.values[map['type']],
      quantity: map['quantity'],
      costPerUnit: map['costPerUnit'],
      referenceType: map['referenceType'],
      referenceId: map['referenceId'],
      notes: map['notes'],
    );
  }
}

// نموذج إشعار المخزون
class InventoryAlert {
  final String? id;
  final String inventoryId; // معرف المخزون
  final String itemName; // اسم العنصر
  final String itemCode; // كود العنصر
  final DateTime date; // تاريخ الإشعار
  final String alertType; // نوع الإشعار (مستوى منخفض، تحت مستوى إعادة الطلب)
  final double currentLevel; // المستوى الحالي
  final double thresholdLevel; // مستوى العتبة
  final bool isResolved; // هل تم حل المشكلة

  InventoryAlert({
    this.id,
    required this.inventoryId,
    required this.itemName,
    required this.itemCode,
    required this.date,
    required this.alertType,
    required this.currentLevel,
    required this.thresholdLevel,
    required this.isResolved,
  });

  // تحويل InventoryAlert إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'inventoryId': inventoryId,
      'itemName': itemName,
      'itemCode': itemCode,
      'date': date.toIso8601String(),
      'alertType': alertType,
      'currentLevel': currentLevel,
      'thresholdLevel': thresholdLevel,
      'isResolved': isResolved ? 1 : 0,
    };
  }

  // إنشاء InventoryAlert من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory InventoryAlert.fromMap(Map<String, dynamic> map) {
    return InventoryAlert(
      id: map['id'],
      inventoryId: map['inventoryId'],
      itemName: map['itemName'],
      itemCode: map['itemCode'],
      date: DateTime.parse(map['date']),
      alertType: map['alertType'],
      currentLevel: map['currentLevel'],
      thresholdLevel: map['thresholdLevel'],
      isResolved: map['isResolved'] == 1,
    );
  }
}
