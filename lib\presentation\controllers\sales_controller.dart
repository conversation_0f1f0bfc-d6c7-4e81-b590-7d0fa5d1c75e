import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:mostafa_final/data/models/sale.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/utils/format_utils.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:uuid/uuid.dart';
import 'package:flutter/services.dart';
import 'package:fl_chart/fl_chart.dart';

class SalesController extends GetxController {
  // المستودعات
  final SalesRepository _salesRepository = Get.find<SalesRepository>();
  final ProductRepository _productRepository = Get.find<ProductRepository>();
  final InventoryRepository _inventoryRepository = Get.find<InventoryRepository>();
  final _uuid = const Uuid();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isLoadingReports = false.obs;

  // قائمة المبيعات
  final RxList<Sale> sales = <Sale>[].obs;
  final Rx<Sale?> selectedSale = Rx<Sale?>(null);
  final RxString selectedStatus = 'قيد التجهيز'.obs;

  // بيانات التقارير
  final Rx<DateTime> reportStartDate =
      DateTime.now().subtract(const Duration(days: 30)).obs;
  final Rx<DateTime> reportEndDate = DateTime.now().obs;
  final RxDouble totalSalesAmount = 0.0.obs;
  final RxInt totalSalesCount = 0.obs;
  final RxDouble averageOrderValue = 0.0.obs;
  final RxList<FlSpot> salesChartData = <FlSpot>[].obs;
  final RxList<String> salesChartLabels = <String>[].obs;
  final RxList<Map<String, dynamic>> topSellingProducts =
      <Map<String, dynamic>>[].obs;
  final RxMap<String, int> salesStatusData = <String, int>{}.obs;

  // قائمة المنتجات المتاحة للبيع
  final RxList<Product> availableProducts = <Product>[].obs;

  // بيانات البيع الجديد
  final RxString newSaleCustomerName = ''.obs;
  final RxString newSaleCustomerPhone = ''.obs;
  final RxString newSaleShippingAddress = ''.obs;
  final RxList<SaleItem> newSaleItems = <SaleItem>[].obs;
  final Rx<Product?> selectedProduct = Rx<Product?>(null);
  final RxInt selectedQuantity = 1.obs;

  @override
  void onInit() {
    super.onInit();
    fetchAllData();
  }

  // جلب جميع البيانات
  Future<void> fetchAllData() async {
    isLoading.value = true;
    try {
      await Future.wait([fetchSales(), fetchAvailableProducts()]);
    } catch (e) {
      debugPrint('Error fetching sales data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // جلب قائمة المبيعات
  Future<void> fetchSales() async {
    try {
      final allSales = await _salesRepository.getAllSales();
      sales.assignAll(allSales);
    } catch (e) {
      debugPrint('Error fetching sales: $e');
    }
  }

  // جلب قائمة المنتجات المتاحة للبيع
  Future<void> fetchAvailableProducts() async {
    try {
      // جلب المنتجات من المخزون
      final products = await _productRepository.getAllProducts();

      // تصفية المنتجات التي لها كمية متاحة في المخزون
      final filteredProducts = <Product>[];

      for (final product in products) {
        final inventory = await _inventoryRepository
            .getInventoryItemByItemIdAndType(product.id!, 'product');

        if (inventory != null && inventory.currentQuantity > 0) {
          filteredProducts.add(product);
        }
      }

      availableProducts.assignAll(filteredProducts);

      // تعيين المنتج الافتراضي
      if (availableProducts.isNotEmpty && selectedProduct.value == null) {
        selectedProduct.value = availableProducts.first;
      }
    } catch (e) {
      debugPrint('Error fetching available products: $e');
    }
  }

  // اختيار عملية بيع
  void selectSale(Sale sale) {
    selectedSale.value = sale;
    selectedStatus.value = sale.shippingStatus;
  }

  // تصفية المبيعات حسب التاريخ
  Future<void> filterSalesByDate(DateTime date) async {
    isLoading.value = true;
    try {
      final filteredSales = await _salesRepository.getSalesByDate(date);
      sales.assignAll(filteredSales);
    } catch (e) {
      debugPrint('Error filtering sales by date: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث حالة البيع
  Future<void> updateSaleStatus(String saleId, String status) async {
    try {
      await _salesRepository.updateSaleStatus(saleId, status);

      // تحديث القائمة
      await fetchSales();

      // تحديث البيع المحدد
      if (selectedSale.value != null && selectedSale.value!.id == saleId) {
        final updatedSale = await _salesRepository.getSaleById(saleId);
        if (updatedSale != null) {
          selectedSale.value = updatedSale;
        }
      }

      Get.snackbar(
        'نجاح',
        'تم تحديث حالة البيع بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('Error updating sale status: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة البيع',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // حذف عملية بيع
  Future<void> deleteSale(String saleId) async {
    try {
      // التحقق من أن البيع ليس بحالة "تم التسليم"
      if (selectedSale.value != null &&
          selectedSale.value!.shippingStatus == 'تم التسليم') {
        Get.dialog(
          AlertDialog(
            title: const Text('تنبيه'),
            content: const Text(
              'لا يمكن حذف عملية بيع تم تسليمها بالفعل. يمكنك فقط تغيير حالتها إلى "ملغي".',
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('موافق'),
              ),
            ],
          ),
        );
        return;
      }

      // طلب تأكيد الحذف
      Get.dialog(
        AlertDialog(
          title: const Text('تأكيد الحذف'),
          content: const Text('هل أنت متأكد من رغبتك في حذف عملية البيع هذه؟'),
          actions: [
            TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
            TextButton(
              onPressed: () async {
                Get.back();

                // إرجاع المنتجات إلى المخزون
                if (selectedSale.value != null) {
                  await _returnProductsToInventory(selectedSale.value!);
                }

                // حذف عملية البيع
                await _salesRepository.deleteSale(saleId);

                // إعادة تحميل البيانات
                await fetchSales();

                // إعادة تعيين البيع المحدد
                selectedSale.value = null;

                Get.snackbar(
                  'نجاح',
                  'تم حذف عملية البيع بنجاح',
                  backgroundColor: Colors.green,
                  colorText: Colors.white,
                );
              },
              child: const Text('حذف', style: TextStyle(color: Colors.red)),
            ),
          ],
        ),
      );
    } catch (e) {
      debugPrint('Error deleting sale: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حذف عملية البيع',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // إرجاع المنتجات إلى المخزون
  Future<void> _returnProductsToInventory(Sale sale) async {
    try {
      for (final item in sale.items) {
        // البحث عن المنتج في المخزون
        final inventoryItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(item.productId, 'product');

        if (inventoryItem != null) {
          // إضافة المنتج إلى المخزون مرة أخرى
          await _inventoryRepository.updateInventoryQuantity(
            inventoryItem.id!,
            item.quantity.toDouble(),
            'sales_return',
            sale.id!,
            notes: 'إرجاع من عملية بيع ملغاة',
          );
        }
      }
    } catch (e) {
      debugPrint('Error returning products to inventory: $e');
    }
  }

  // إعادة تعيين بيانات البيع الجديد
  void resetNewSale() {
    newSaleCustomerName.value = '';
    newSaleCustomerPhone.value = '';
    newSaleShippingAddress.value = '';
    newSaleItems.clear();
    selectedQuantity.value = 1;
    if (availableProducts.isNotEmpty) {
      selectedProduct.value = availableProducts.first;
    }
  }

  // إضافة منتج إلى البيع
  void addProductToSale() {
    if (selectedProduct.value == null || selectedQuantity.value <= 0) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار منتج وكمية صحيحة',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return;
    }

    // التحقق من توفر الكمية في المخزون
    _checkProductAvailability(
      selectedProduct.value!.id!,
      selectedQuantity.value,
    ).then((available) {
      if (available) {
        // حساب سعر الوحدة (بسيط: التكلفة + 30% هامش ربح)
        final unitCost = selectedProduct.value!.calculateTotalCost(
          2.0,
        ); // افتراض تكلفة عمل 2 جنيه/دقيقة
        final unitPrice = unitCost * 1.3; // هامش ربح 30%

        // إنشاء عنصر البيع
        final saleItem = SaleItem(
          productId: selectedProduct.value!.id!,
          productName: selectedProduct.value!.name,
          quantity: selectedQuantity.value,
          unitPrice: unitPrice,
          totalPrice: unitPrice * selectedQuantity.value,
        );

        // إضافة العنصر إلى القائمة
        newSaleItems.add(saleItem);

        // إعادة تعيين الكمية
        selectedQuantity.value = 1;
      } else {
        Get.snackbar(
          'خطأ',
          'الكمية المطلوبة غير متوفرة في المخزون',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    });
  }

  // التحقق من توفر المنتج في المخزون
  Future<bool> _checkProductAvailability(String productId, int quantity) async {
    try {
      final inventoryItem = await _inventoryRepository
          .getInventoryItemByItemIdAndType(productId, 'product');

      if (inventoryItem == null) {
        return false;
      }

      return inventoryItem.currentQuantity >= quantity;
    } catch (e) {
      debugPrint('Error checking product availability: $e');
      return false;
    }
  }

  // إزالة منتج من البيع
  void removeProductFromSale(int index) {
    if (index >= 0 && index < newSaleItems.length) {
      newSaleItems.removeAt(index);
    }
  }

  // حساب إجمالي المبلغ
  double calculateTotalAmount() {
    return newSaleItems.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  // التحقق من صحة بيانات البيع الجديد
  bool validateNewSale() {
    if (newSaleCustomerName.value.isEmpty ||
        newSaleCustomerPhone.value.isEmpty ||
        newSaleShippingAddress.value.isEmpty ||
        newSaleItems.isEmpty) {
      return false;
    }

    return true;
  }

  // حفظ عملية البيع الجديدة
  Future<void> saveSale() async {
    try {
      // إنشاء رقم فاتورة
      final invoiceNumber = _generateInvoiceNumber();

      // إنشاء كائن البيع
      final sale = Sale(
        id: _uuid.v4(),
        invoiceNumber: invoiceNumber,
        date: DateTime.now(),
        customerName: newSaleCustomerName.value,
        customerPhone: newSaleCustomerPhone.value,
        shippingAddress: newSaleShippingAddress.value,
        items: newSaleItems,
        totalAmount: calculateTotalAmount(),
        shippingStatus: 'قيد التجهيز',
      );

      // حفظ البيع
      await _salesRepository.createSale(sale);

      // خصم المنتجات من المخزون
      await _deductProductsFromInventory(sale);

      // إعادة تحميل البيانات
      await fetchSales();
      await fetchAvailableProducts();

      // إعادة تعيين بيانات البيع الجديد
      resetNewSale();

      Get.snackbar(
        'نجاح',
        'تم حفظ عملية البيع بنجاح',
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
    } catch (e) {
      debugPrint('Error saving sale: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ عملية البيع',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // خصم المنتجات من المخزون
  Future<void> _deductProductsFromInventory(Sale sale) async {
    try {
      for (final item in sale.items) {
        // البحث عن المنتج في المخزون
        final inventoryItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(item.productId, 'product');

        if (inventoryItem != null) {
          // خصم المنتج من المخزون
          await _inventoryRepository.updateInventoryQuantity(
            inventoryItem.id!,
            -item.quantity.toDouble(),
            'sale',
            sale.id!,
            notes: 'بيع للعميل ${sale.customerName}',
          );
        }
      }
    } catch (e) {
      debugPrint('Error deducting products from inventory: $e');
    }
  }

  // إنشاء رقم فاتورة جديد
  String _generateInvoiceNumber() {
    final now = DateTime.now();
    final dateStr = DateFormat('yyyyMMdd').format(now);
    final randomNum = (1000 + now.millisecond + now.second * 10).toString();

    return 'INV-$dateStr-$randomNum';
  }

  // طباعة الفاتورة
  Future<void> printInvoice(Sale sale) async {
    final pdf = pw.Document();

    // إضافة خط عربي للتقرير
    final arabicFont = await rootBundle.load("assets/fonts/Cairo-Regular.ttf");
    final ttf = pw.Font.ttf(arabicFont);

    pdf.addPage(
      pw.Page(
        pageFormat: PdfPageFormat.a4,
        build: (pw.Context context) {
          return pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // ترويسة الفاتورة
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Text(
                        'نظام إدارة المصنع',
                        style: pw.TextStyle(
                          font: ttf,
                          fontSize: 20,
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'فاتورة بيع منتجات',
                        style: pw.TextStyle(font: ttf, fontSize: 16),
                      ),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.end,
                    children: [
                      pw.Text(
                        'رقم الفاتورة: ${sale.invoiceNumber}',
                        style: pw.TextStyle(font: ttf, fontSize: 14),
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'التاريخ: ${FormatUtils.formatDate(sale.date)}',
                        style: pw.TextStyle(font: ttf, fontSize: 14),
                      ),
                    ],
                  ),
                ],
              ),

              pw.SizedBox(height: 20),

              // معلومات العميل
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(border: pw.Border.all()),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'معلومات العميل',
                      style: pw.TextStyle(
                        font: ttf,
                        fontSize: 16,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 10),
                    pw.Row(
                      children: [
                        pw.Text('الاسم:', style: pw.TextStyle(font: ttf)),
                        pw.SizedBox(width: 10),
                        pw.Text(sale.customerName),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      children: [
                        pw.Text('رقم الهاتف:', style: pw.TextStyle(font: ttf)),
                        pw.SizedBox(width: 10),
                        pw.Text(sale.customerPhone),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      children: [
                        pw.Text('العنوان:', style: pw.TextStyle(font: ttf)),
                        pw.SizedBox(width: 10),
                        pw.Text(sale.shippingAddress),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 20),

              // جدول المنتجات
              pw.Text(
                'المنتجات',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                ),
              ),
              pw.SizedBox(height: 10),
              pw.Table(
                border: pw.TableBorder.all(),
                children: [
                  // رأس الجدول
                  pw.TableRow(
                    decoration: const pw.BoxDecoration(
                      color: PdfColors.grey300,
                    ),
                    children: [
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'المنتج',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'العدد',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'سعر الوحدة',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                      pw.Padding(
                        padding: const pw.EdgeInsets.all(5),
                        child: pw.Text(
                          'الإجمالي',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                          textAlign: pw.TextAlign.center,
                        ),
                      ),
                    ],
                  ),

                  // بيانات المنتجات
                  for (final item in sale.items)
                    pw.TableRow(
                      children: [
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            item.productName,
                            style: pw.TextStyle(font: ttf),
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            item.quantity.toString(),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(item.unitPrice),
                            textAlign: pw.TextAlign.center,
                          ),
                        ),
                        pw.Padding(
                          padding: const pw.EdgeInsets.all(5),
                          child: pw.Text(
                            FormatUtils.formatCurrency(item.totalPrice),
                            textAlign: pw.TextAlign.center,
                            style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                          ),
                        ),
                      ],
                    ),
                ],
              ),

              pw.SizedBox(height: 20),

              // الإجمالي
              pw.Container(
                alignment: pw.Alignment.centerRight,
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.end,
                  children: [
                    pw.Row(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        pw.Text(
                          'إجمالي الفاتورة:',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(width: 10),
                        pw.Text(
                          FormatUtils.formatCurrency(sale.totalAmount),
                          style: pw.TextStyle(fontWeight: pw.FontWeight.bold),
                        ),
                      ],
                    ),
                    pw.SizedBox(height: 5),
                    pw.Row(
                      mainAxisSize: pw.MainAxisSize.min,
                      children: [
                        pw.Text(
                          'حالة الشحن:',
                          style: pw.TextStyle(
                            font: ttf,
                            fontWeight: pw.FontWeight.bold,
                          ),
                        ),
                        pw.SizedBox(width: 10),
                        pw.Text(
                          sale.shippingStatus,
                          style: pw.TextStyle(font: ttf),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              pw.SizedBox(height: 30),

              // الشروط والأحكام
              pw.Container(
                padding: const pw.EdgeInsets.all(10),
                decoration: pw.BoxDecoration(
                  border: pw.Border.all(color: PdfColors.grey),
                ),
                child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Text(
                      'الشروط والأحكام:',
                      style: pw.TextStyle(
                        font: ttf,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.SizedBox(height: 5),
                    pw.Text(
                      '- يتم توصيل المنتجات خلال 3-5 أيام عمل.',
                      style: pw.TextStyle(font: ttf, fontSize: 10),
                    ),
                    pw.Text(
                      '- البضاعة المباعة لا ترد ولا تستبدل بعد استلامها.',
                      style: pw.TextStyle(font: ttf, fontSize: 10),
                    ),
                    pw.Text(
                      '- في حال وجود عيوب مصنعية، يتم استبدال المنتج خلال أسبوع من تاريخ الاستلام.',
                      style: pw.TextStyle(font: ttf, fontSize: 10),
                    ),
                  ],
                ),
              ),

              pw.Spacer(),

              // التوقيع
              pw.Row(
                mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                children: [
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Container(
                        width: 100,
                        height: 0.5,
                        color: PdfColors.black,
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'توقيع المستلم',
                        style: pw.TextStyle(font: ttf, fontSize: 10),
                      ),
                    ],
                  ),
                  pw.Column(
                    crossAxisAlignment: pw.CrossAxisAlignment.center,
                    children: [
                      pw.Container(
                        width: 100,
                        height: 0.5,
                        color: PdfColors.black,
                      ),
                      pw.SizedBox(height: 5),
                      pw.Text(
                        'ختم الشركة',
                        style: pw.TextStyle(font: ttf, fontSize: 10),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          );
        },
      ),
    );

    await Printing.layoutPdf(
      onLayout: (PdfPageFormat format) async => pdf.save(),
    );
  }

  // جلب بيانات تقارير المبيعات
  Future<void> fetchSalesReportData() async {
    isLoadingReports.value = true;
    try {
      // جلب المبيعات في نطاق التاريخ المحدد
      final salesInRange = await _salesRepository.getSalesByDateRange(
        reportStartDate.value,
        reportEndDate.value,
      );

      // حساب إجمالي المبيعات
      totalSalesAmount.value = salesInRange.fold(
        0,
        (sum, sale) => sum + sale.totalAmount,
      );

      // عدد المبيعات
      totalSalesCount.value = salesInRange.length;

      // متوسط قيمة الطلب
      averageOrderValue.value =
          totalSalesCount.value > 0
              ? totalSalesAmount.value / totalSalesCount.value
              : 0;

      // إعداد بيانات المخطط
      _prepareSalesChartData(salesInRange);

      // جلب المنتجات الأكثر مبيعاً
      await _fetchTopSellingProducts();

      // حساب توزيع حالات الطلبات
      _calculateSalesStatusDistribution(salesInRange);
    } catch (e) {
      debugPrint('Error fetching sales report data: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء جلب بيانات التقارير',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      isLoadingReports.value = false;
    }
  }

  // إعداد بيانات مخطط المبيعات
  void _prepareSalesChartData(List<Sale> salesInRange) {
    // تنظيم المبيعات حسب التاريخ
    final Map<String, double> dailySales = {};

    // حساب عدد الأيام بين تاريخ البداية والنهاية
    final int daysDifference =
        reportEndDate.value.difference(reportStartDate.value).inDays;

    // إذا كانت الفترة أكثر من 30 يوماً، نقوم بتجميع البيانات حسب الأسبوع
    bool groupByWeek = daysDifference > 30;

    // إذا كانت الفترة أكثر من 90 يوماً، نقوم بتجميع البيانات حسب الشهر
    bool groupByMonth = daysDifference > 90;

    for (var sale in salesInRange) {
      String key;

      if (groupByMonth) {
        // تجميع حسب الشهر
        key = DateFormat('yyyy-MM').format(sale.date);
      } else if (groupByWeek) {
        // تجميع حسب الأسبوع
        int weekNumber = (sale.date.day / 7).ceil();
        key = '${DateFormat('yyyy-MM').format(sale.date)}-W$weekNumber';
      } else {
        // تجميع حسب اليوم
        key = DateFormat('yyyy-MM-dd').format(sale.date);
      }

      dailySales[key] = (dailySales[key] ?? 0) + sale.totalAmount;
    }

    // تحويل البيانات إلى قائمة مرتبة حسب التاريخ
    final List<MapEntry<String, double>> sortedEntries =
        dailySales.entries.toList()..sort((a, b) => a.key.compareTo(b.key));

    // إعداد بيانات المخطط
    salesChartData.clear();
    salesChartLabels.clear();

    for (int i = 0; i < sortedEntries.length; i++) {
      salesChartData.add(FlSpot(i.toDouble(), sortedEntries[i].value));

      // تنسيق التسمية حسب نوع التجميع
      String label;
      if (groupByMonth) {
        label = DateFormat(
          'MM/yy',
        ).format(DateFormat('yyyy-MM').parse(sortedEntries[i].key));
      } else if (groupByWeek) {
        final parts = sortedEntries[i].key.split('-W');
        label =
            'W${parts[1]} ${parts[0].split('-')[1]}/${parts[0].split('-')[0].substring(2)}';
      } else {
        label = DateFormat(
          'dd/MM',
        ).format(DateFormat('yyyy-MM-dd').parse(sortedEntries[i].key));
      }

      salesChartLabels.add(label);
    }
  }

  // جلب المنتجات الأكثر مبيعاً
  Future<void> _fetchTopSellingProducts() async {
    try {
      final products = await _salesRepository.getTopSellingProducts(10);
      topSellingProducts.assignAll(products);
    } catch (e) {
      debugPrint('Error fetching top selling products: $e');
    }
  }

  // حساب توزيع حالات الطلبات
  void _calculateSalesStatusDistribution(List<Sale> salesInRange) {
    final Map<String, int> statusCounts = {};

    for (var sale in salesInRange) {
      statusCounts[sale.shippingStatus] =
          (statusCounts[sale.shippingStatus] ?? 0) + 1;
    }

    salesStatusData.value = statusCounts;
  }

  // طباعة تقرير المبيعات
  Future<void> printSalesReport() async {
    try {
      final pdf = pw.Document();

      // إضافة صفحة العنوان
      pdf.addPage(
        pw.Page(
          build: (pw.Context context) {
            return pw.Center(
              child: pw.Column(
                mainAxisAlignment: pw.MainAxisAlignment.center,
                children: [
                  pw.Text(
                    'تقرير المبيعات',
                    style: pw.TextStyle(
                      fontSize: 30,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Text(
                    'من ${DateFormat('yyyy-MM-dd').format(reportStartDate.value)} إلى ${DateFormat('yyyy-MM-dd').format(reportEndDate.value)}',
                    style: const pw.TextStyle(fontSize: 20),
                  ),
                  pw.SizedBox(height: 40),
                  pw.Text(
                    'إجمالي المبيعات: ${FormatUtils.formatCurrency(totalSalesAmount.value)}',
                    style: const pw.TextStyle(fontSize: 18),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'عدد الطلبات: ${totalSalesCount.value}',
                    style: const pw.TextStyle(fontSize: 18),
                  ),
                  pw.SizedBox(height: 10),
                  pw.Text(
                    'متوسط قيمة الطلب: ${FormatUtils.formatCurrency(averageOrderValue.value)}',
                    style: const pw.TextStyle(fontSize: 18),
                  ),
                ],
              ),
            );
          },
        ),
      );

      // إضافة صفحة المنتجات الأكثر مبيعاً
      if (topSellingProducts.isNotEmpty) {
        pdf.addPage(
          pw.Page(
            build: (pw.Context context) {
              return pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text(
                    'المنتجات الأكثر مبيعاً',
                    style: pw.TextStyle(
                      fontSize: 20,
                      fontWeight: pw.FontWeight.bold,
                    ),
                  ),
                  pw.SizedBox(height: 20),
                  pw.Table(
                    border: pw.TableBorder.all(),
                    children: [
                      // رأس الجدول
                      pw.TableRow(
                        children: [
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              'المنتج',
                              style: pw.TextStyle(
                                fontWeight: pw.FontWeight.bold,
                              ),
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              'الكمية',
                              style: pw.TextStyle(
                                fontWeight: pw.FontWeight.bold,
                              ),
                              textAlign: pw.TextAlign.center,
                            ),
                          ),
                          pw.Padding(
                            padding: const pw.EdgeInsets.all(8),
                            child: pw.Text(
                              'الإيرادات',
                              style: pw.TextStyle(
                                fontWeight: pw.FontWeight.bold,
                              ),
                              textAlign: pw.TextAlign.center,
                            ),
                          ),
                        ],
                      ),
                      // بيانات المنتجات
                      ...topSellingProducts.map(
                        (product) => pw.TableRow(
                          children: [
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text(product['productName'] as String),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text(
                                (product['totalQuantity'] as int).toString(),
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                            pw.Padding(
                              padding: const pw.EdgeInsets.all(8),
                              child: pw.Text(
                                FormatUtils.formatCurrency(
                                  product['totalRevenue'] as double,
                                ),
                                textAlign: pw.TextAlign.center,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              );
            },
          ),
        );
      }

      // طباعة التقرير
      await Printing.layoutPdf(
        onLayout: (PdfPageFormat format) async => pdf.save(),
      );
    } catch (e) {
      debugPrint('Error printing sales report: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء طباعة التقرير',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
