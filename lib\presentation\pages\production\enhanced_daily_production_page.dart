import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
// import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'enhanced_daily_production_controller.dart';

/// صفحة تسجيل الإنتاج اليومي المحسنة
class EnhancedDailyProductionPage extends StatelessWidget {
  const EnhancedDailyProductionPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(EnhancedDailyProductionController());

    return Scaffold(
      appBar: _buildAppBar(controller),
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return _buildLoadingIndicator();
        }

        return RefreshIndicator(
          onRefresh: controller.refreshData,
          child: SingleChildScrollView(
            physics: const AlwaysScrollableScrollPhysics(),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildQuickStatsSection(controller),
                const SizedBox(height: 20),
                _buildDateAndShiftSelector(controller),
                const SizedBox(height: 20),
                _buildProductionForm(controller),
                const SizedBox(height: 20),
                _buildProductionHistorySection(controller),
              ],
            ),
          ),
        );
      }),
      floatingActionButton: _buildFloatingActionButton(controller),
    );
  }

  /// شريط التطبيق المحسن
  PreferredSizeWidget _buildAppBar(
    EnhancedDailyProductionController controller,
  ) {
    return AppBar(
      title: const Text(
        'تسجيل الإنتاج اليومي',
        style: TextStyle(fontWeight: FontWeight.bold),
      ),
      centerTitle: true,
      elevation: 0,
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
      actions: [
        // زر البحث والفلترة
        IconButton(
          icon: const Icon(Icons.search),
          onPressed: () => _showSearchDialog(controller),
          tooltip: 'البحث والفلترة',
        ),
        // زر التصدير
        PopupMenuButton<String>(
          icon: const Icon(Icons.file_download),
          tooltip: 'تصدير البيانات',
          onSelected: (value) => _handleExportAction(controller, value),
          itemBuilder:
              (context) => [
                const PopupMenuItem(
                  value: 'pdf',
                  child: Row(
                    children: [
                      Icon(Icons.picture_as_pdf, color: Colors.red),
                      SizedBox(width: 8),
                      Text('تصدير PDF'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'excel',
                  child: Row(
                    children: [
                      Icon(Icons.table_chart, color: Colors.green),
                      SizedBox(width: 8),
                      Text('تصدير Excel'),
                    ],
                  ),
                ),
              ],
        ),
        // زر الإعدادات
        IconButton(
          icon: const Icon(Icons.settings),
          onPressed: () => _showSettingsDialog(controller),
          tooltip: 'الإعدادات',
        ),
      ],
    );
  }

  /// مؤشر التحميل المحسن
  Widget _buildLoadingIndicator() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            strokeWidth: 3,
            valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
          ),
          SizedBox(height: 16),
          Text(
            'جاري تحميل البيانات...',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection(EnhancedDailyProductionController controller) {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.white],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.analytics, color: Colors.blue.shade600, size: 24),
                const SizedBox(width: 8),
                const Text(
                  'إحصائيات سريعة',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              final stats = controller.quickStats;
              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                childAspectRatio: 2.5,
                crossAxisSpacing: 12,
                mainAxisSpacing: 12,
                children: [
                  _buildStatCard(
                    'إجمالي الإنتاج',
                    '${stats['totalProduction']} قطعة',
                    Icons.precision_manufacturing,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'المواد المستخدمة',
                    '${stats['totalMaterials'].toStringAsFixed(1)} كجم',
                    Icons.inventory,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'متوسط وقت الدورة',
                    '${stats['avgCycleTime'].toStringAsFixed(1)} ثانية',
                    Icons.timer,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'عدد السجلات',
                    '${stats['recordCount']} سجل',
                    Icons.list_alt,
                    Colors.purple,
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  title,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// قسم اختيار التاريخ والوردية المحسن
  Widget _buildDateAndShiftSelector(
    EnhancedDailyProductionController controller,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calendar_today, color: Colors.blue.shade600),
                const SizedBox(width: 8),
                const Text(
                  'التاريخ والوردية',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: InkWell(
                    onTap: () => controller.selectDate(Get.context!),
                    borderRadius: BorderRadius.circular(8),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey.shade300),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.event, color: Colors.blue.shade600),
                          const SizedBox(width: 8),
                          Obx(
                            () => Text(
                              controller.formatDate(
                                controller.selectedDate.value,
                              ),
                              style: const TextStyle(fontSize: 14),
                            ),
                          ),
                          const Spacer(),
                          Icon(
                            Icons.arrow_drop_down,
                            color: Colors.grey.shade600,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'عدد الورديات',
                        style: TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                      const SizedBox(height: 4),
                      Obx(
                        () => DropdownButtonFormField<double>(
                          value: controller.selectedShiftsCount.value,
                          decoration: InputDecoration(
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          items:
                              controller.availableShifts.map((shift) {
                                return DropdownMenuItem(
                                  value: shift,
                                  child: Text('${shift.toString()} وردية'),
                                );
                              }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              controller.selectedShiftsCount.value = value;
                              controller.calculatePartsFromWeight();
                            }
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// نموذج الإنتاج المحسن
  Widget _buildProductionForm(EnhancedDailyProductionController controller) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(Icons.add_box, color: Colors.green.shade600),
                  const SizedBox(width: 8),
                  const Text(
                    'إضافة سجل إنتاج جديد',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // اختيار الماكينة مع تحسينات
              _buildEnhancedDropdown<String>(
                label: 'الماكينة',
                icon: Icons.precision_manufacturing,
                value:
                    controller.selectedMachineId.value.isEmpty
                        ? null
                        : controller.selectedMachineId.value,
                items:
                    controller.machines.map((Machine machine) {
                      return DropdownMenuItem<String>(
                        value: machine.id!,
                        child: Row(
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color:
                                    machine.status == MachineStatus.operating
                                        ? Colors.green
                                        : Colors.orange,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                '${machine.name} - ${machine.model}',
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectMachine(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار الماكينة';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 16),

              // اختيار الإسطمبة مع تحسينات
              Obx(
                () => _buildEnhancedDropdown<String>(
                  label: 'الإسطمبة',
                  icon: Icons.view_module,
                  value:
                      controller.selectedMoldId.value.isEmpty
                          ? null
                          : controller.selectedMoldId.value,
                  items:
                      controller.availableMolds.map((Mold mold) {
                        return DropdownMenuItem<String>(
                          value: mold.id!,
                          child: Row(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                decoration: BoxDecoration(
                                  color:
                                      mold.status == MoldStatus.available
                                          ? Colors.green.shade100
                                          : Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Text(
                                  '${mold.cavityCount}',
                                  style: TextStyle(
                                    fontSize: 10,
                                    color:
                                        mold.status == MoldStatus.available
                                            ? Colors.green.shade700
                                            : Colors.orange.shade700,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${mold.name} - ${mold.productCode}',
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                  onChanged: (value) {
                    if (value != null && controller.availableMolds.isNotEmpty) {
                      controller.selectMold(value);
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار الإسطمبة';
                    }
                    return null;
                  },
                ),
              ),

              const SizedBox(height: 16),

              // اختيار المادة الخام مع تحسينات
              _buildEnhancedDropdown<String>(
                label: 'المادة الخام',
                icon: Icons.category,
                value:
                    controller.selectedRawMaterialId.value.isEmpty
                        ? null
                        : controller.selectedRawMaterialId.value,
                items:
                    controller.rawMaterials.map((RawMaterial material) {
                      final isLowStock = material.availableQuantity < 50;
                      return DropdownMenuItem<String>(
                        value: material.id!,
                        child: Row(
                          children: [
                            Container(
                              width: 12,
                              height: 12,
                              decoration: BoxDecoration(
                                color: Color(
                                  int.parse(
                                    '0xFF${material.color.replaceAll('#', '')}',
                                  ),
                                ),
                                shape: BoxShape.circle,
                                border: Border.all(color: Colors.grey.shade400),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    material.name,
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                  Text(
                                    '${material.availableQuantity.toStringAsFixed(1)} كجم متاح',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color:
                                          isLowStock ? Colors.red : Colors.grey,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (isLowStock)
                              Icon(
                                Icons.warning,
                                color: Colors.orange,
                                size: 16,
                              ),
                          ],
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectRawMaterial(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار المادة الخام';
                  }
                  return null;
                },
              ),

              const SizedBox(height: 20),

              // حقول الإدخال المحسنة
              Row(
                children: [
                  Expanded(
                    child: _buildEnhancedTextField(
                      controller: controller.rawMaterialUsedController,
                      label: 'الوزن الإجمالي',
                      suffix: 'كجم',
                      icon: Icons.scale,
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'مطلوب';
                        }
                        final double? amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'قيمة غير صحيحة';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildEnhancedTextField(
                      controller: controller.partsProducedController,
                      label: 'عدد القطع',
                      suffix: 'قطعة',
                      icon: Icons.inventory_2,
                      readOnly: true,
                      enabled: false,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 16),

              _buildEnhancedTextField(
                controller: controller.cycleTimeController,
                label: 'وقت الدورة (محسوب تلقائياً)',
                suffix: 'ثانية',
                icon: Icons.timer,
                readOnly: true,
                enabled: false,
              ),

              const SizedBox(height: 20),

              // عرض الحسابات
              _buildCalculationsDisplay(controller),

              const SizedBox(height: 24),

              // زر الحفظ المحسن
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton.icon(
                  onPressed: controller.saveProduction,
                  icon: const Icon(Icons.save, size: 20),
                  label: const Text(
                    'حفظ سجل الإنتاج',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10),
                    ),
                    elevation: 2,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// قائمة منسدلة محسنة
  Widget _buildEnhancedDropdown<T>({
    required String label,
    required IconData icon,
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required void Function(T?) onChanged,
    String? Function(T?)? validator,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey.shade600),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        DropdownButtonFormField<T>(
          value: value,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue.shade400, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
          ),
          items: items,
          onChanged: onChanged,
          validator: validator,
          isExpanded: true,
          icon: Icon(Icons.arrow_drop_down, color: Colors.grey.shade600),
        ),
      ],
    );
  }

  /// حقل نص محسن
  Widget _buildEnhancedTextField({
    required TextEditingController controller,
    required String label,
    required IconData icon,
    String? suffix,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    bool readOnly = false,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.grey.shade600),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        TextFormField(
          controller: controller,
          decoration: InputDecoration(
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.blue.shade400, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 8,
            ),
            filled: true,
            fillColor: enabled ? Colors.grey.shade50 : Colors.grey.shade100,
            suffixText: suffix,
            suffixStyle: TextStyle(color: Colors.grey.shade600, fontSize: 12),
          ),
          keyboardType: keyboardType,
          inputFormatters: inputFormatters,
          validator: validator,
          readOnly: readOnly,
          enabled: enabled,
          style: TextStyle(
            color: enabled ? Colors.black87 : Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// عرض الحسابات
  Widget _buildCalculationsDisplay(
    EnhancedDailyProductionController controller,
  ) {
    return Obx(
      () => Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.blue.shade50, Colors.blue.shade100],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.blue.shade200),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.calculate, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  'الحسابات التلقائية',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: _buildCalculationItem(
                    'الوزن النظري',
                    '${controller.calculatedWeight.value.toStringAsFixed(2)} كجم',
                    Icons.scale,
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildCalculationItem(
                    'الفاقد المقدر',
                    '${controller.calculatedScrap.value.toStringAsFixed(2)} كجم',
                    Icons.recycling,
                    Colors.orange,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// عنصر حساب
  Widget _buildCalculationItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 6),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  value,
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(
                  label,
                  style: const TextStyle(fontSize: 10, color: Colors.grey),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// قسم سجل الإنتاج المحسن
  Widget _buildProductionHistorySection(
    EnhancedDailyProductionController controller,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.history, color: Colors.blue.shade600),
                    const SizedBox(width: 8),
                    const Text(
                      'سجل الإنتاج اليومي',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    // زر الفلترة
                    IconButton(
                      icon: Icon(
                        Icons.filter_list,
                        color: Colors.blue.shade600,
                      ),
                      onPressed: () => _showFilterDialog(controller),
                      tooltip: 'فلترة السجلات',
                    ),
                    // زر التحديث
                    IconButton(
                      icon: Icon(Icons.refresh, color: Colors.green.shade600),
                      onPressed: controller.fetchDailyProduction,
                      tooltip: 'تحديث السجلات',
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),

            // شريط البحث
            _buildSearchBar(controller),

            const SizedBox(height: 16),

            // قائمة السجلات
            Obx(() {
              if (controller.isLoadingHistory.value) {
                return const Center(
                  child: Padding(
                    padding: EdgeInsets.all(32),
                    child: CircularProgressIndicator(),
                  ),
                );
              }

              final filteredRecords = controller.filteredDailyProduction;

              if (filteredRecords.isEmpty) {
                return _buildEmptyState();
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: filteredRecords.length,
                itemBuilder: (context, index) {
                  final production = filteredRecords[index];
                  return _buildProductionCard(controller, production, index);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  /// شريط البحث
  Widget _buildSearchBar(EnhancedDailyProductionController controller) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(25),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: TextField(
        controller: controller.searchController,
        decoration: InputDecoration(
          hintText: 'البحث في السجلات...',
          prefixIcon: Icon(Icons.search, color: Colors.grey.shade600),
          suffixIcon: Obx(
            () =>
                controller.searchQuery.value.isNotEmpty
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: controller.clearSearch,
                    )
                    : const SizedBox(),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 12,
          ),
        ),
        onChanged: controller.updateSearchQuery,
      ),
    );
  }

  /// حالة فارغة
  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            'لا توجد سجلات إنتاج',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإضافة سجل إنتاج جديد',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
          ),
        ],
      ),
    );
  }

  /// بطاقة سجل الإنتاج المحسنة
  Widget _buildProductionCard(
    EnhancedDailyProductionController controller,
    Map<String, dynamic> production,
    int index,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: ExpansionTile(
        leading: CircleAvatar(
          backgroundColor: Colors.blue.shade100,
          child: Text(
            '${index + 1}',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          production['machineName'] ?? 'غير معروف',
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Text(
          '${production['moldName']} - ${production['partsProduced']} قطعة',
          style: TextStyle(color: Colors.grey.shade600),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              controller.formatTimeOnly(DateTime.parse(production['date'])),
              style: TextStyle(color: Colors.grey.shade600, fontSize: 12),
            ),
            const SizedBox(width: 8),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              onSelected:
                  (value) => _handleRecordAction(controller, value, production),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit, color: Colors.blue),
                          SizedBox(width: 8),
                          Text('تعديل'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, color: Colors.red),
                          SizedBox(width: 8),
                          Text('حذف'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'duplicate',
                      child: Row(
                        children: [
                          Icon(Icons.copy, color: Colors.green),
                          SizedBox(width: 8),
                          Text('نسخ'),
                        ],
                      ),
                    ),
                  ],
            ),
          ],
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildDetailRow(
                  'المادة الخام',
                  '${production['rawMaterialName']} - ${production['rawMaterialColor']}',
                ),
                _buildDetailRow(
                  'الكمية المستخدمة',
                  '${(production['rawMaterialUsed'] as double).toStringAsFixed(2)} كجم',
                ),
                _buildDetailRow(
                  'وقت الدورة',
                  '${production['cycleTime']} ثانية',
                ),
                _buildDetailRow(
                  'تكلفة الكهرباء',
                  '${(production['electricityCost'] as double).toStringAsFixed(2)} جنيه',
                ),
                _buildDetailRow(
                  'تكلفة المشغلين',
                  '${(production['operatorCost'] as double).toStringAsFixed(2)} جنيه',
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    _buildMetricChip(
                      'الكفاءة',
                      '${((30.0 / production['cycleTime']) * 100).toStringAsFixed(1)}%',
                      Colors.green,
                    ),
                    const SizedBox(width: 8),
                    _buildMetricChip(
                      'الإنتاجية',
                      '${(production['partsProduced'] / (production['cycleTime'] / 3600)).toStringAsFixed(0)} قطعة/ساعة',
                      Colors.blue,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// صف تفاصيل
  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  /// رقاقة مقياس
  Widget _buildMetricChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 10,
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// زر الإجراء العائم
  Widget _buildFloatingActionButton(
    EnhancedDailyProductionController controller,
  ) {
    return FloatingActionButton.extended(
      onPressed: () => _scrollToForm(),
      icon: const Icon(Icons.add),
      label: const Text('إضافة سجل'),
      backgroundColor: Colors.blue.shade600,
      foregroundColor: Colors.white,
    );
  }

  // طرق مساعدة للحوارات والإجراءات

  void _scrollToForm() {
    // التمرير إلى نموذج الإدخال
  }

  void _showSearchDialog(EnhancedDailyProductionController controller) {
    // عرض حوار البحث المتقدم
  }

  void _handleExportAction(
    EnhancedDailyProductionController controller,
    String action,
  ) {
    switch (action) {
      case 'pdf':
        controller.exportToPDF();
        break;
      case 'excel':
        controller.exportToExcel();
        break;
    }
  }

  void _showSettingsDialog(EnhancedDailyProductionController controller) {
    // عرض حوار الإعدادات
  }

  void _showFilterDialog(EnhancedDailyProductionController controller) {
    // عرض حوار الفلترة
  }

  void _handleRecordAction(
    EnhancedDailyProductionController controller,
    String action,
    Map<String, dynamic> production,
  ) {
    switch (action) {
      case 'edit':
        controller.editProduction(production);
        break;
      case 'delete':
        controller.deleteProduction(production['id']);
        break;
      case 'duplicate':
        controller.duplicateProduction(production);
        break;
    }
  }
}
