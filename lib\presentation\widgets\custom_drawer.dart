import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      elevation: 4,
      child: Container(
        decoration: const BoxDecoration(
          color: EnhancedAppTheme.backgroundColor,
        ),
        child: Column(
          children: [
            _buildDrawerHeader(),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                children: [
                  _buildDrawerItem(
                    title: 'الرئيسية',
                    icon: Icons.dashboard,
                    onTap: () => Get.offAllNamed('/dashboard'),
                    isActive: Get.currentRoute == '/dashboard',
                  ),
                  _buildDrawerCategory('إدارة المواد'),
                  _buildDrawerItem(
                    title: 'المواد الخام',
                    icon: Icons.category,
                    onTap: () => Get.toNamed('/raw-materials'),
                    isActive: Get.currentRoute == '/raw-materials',
                  ),
                  _buildDrawerItem(
                    title: 'الماكينات',
                    icon: Icons.precision_manufacturing,
                    onTap: () => Get.toNamed('/machines'),
                    isActive: Get.currentRoute == '/machines',
                  ),
                  _buildDrawerItem(
                    title: 'الإسطمبات',
                    icon: Icons.settings,
                    onTap: () => Get.toNamed('/molds'),
                    isActive: Get.currentRoute == '/molds',
                  ),
                  _buildDrawerCategory('الإنتاج'),
                  _buildDrawerItem(
                    title: 'تسجيل الإنتاج اليومي',
                    icon: Icons.engineering,
                    onTap: () => Get.toNamed('/daily-production'),
                    isActive: Get.currentRoute == '/daily-production',
                  ),
                  _buildDrawerItem(
                    title: 'حساب تكلفة الحقن',
                    icon: Icons.calculate,
                    onTap: () => Get.toNamed('/injection-cost'),
                    isActive: Get.currentRoute == '/injection-cost',
                  ),
                  _buildDrawerItem(
                    title: 'أولويات الإنتاج',
                    icon: Icons.low_priority,
                    onTap: () => Get.toNamed('/production-priority'),
                    isActive: Get.currentRoute == '/production-priority',
                  ),
                  _buildDrawerItem(
                    title: 'التخطيط الذكي',
                    icon: Icons.auto_awesome,
                    onTap: () => Get.toNamed('/smart-planning'),
                    isActive: Get.currentRoute == '/smart-planning',
                  ),
                  _buildDrawerItem(
                    title: 'إدارة الصيانة',
                    icon: Icons.build_circle,
                    onTap: () => Get.toNamed('/maintenance'),
                    isActive: Get.currentRoute == '/maintenance',
                  ),
                  _buildDrawerItem(
                    title: 'لوحة التحكم التنفيذية',
                    icon: Icons.dashboard,
                    onTap: () => Get.toNamed('/executive-dashboard'),
                    isActive: Get.currentRoute == '/executive-dashboard',
                  ),
                  _buildDrawerItem(
                    title: 'التحليل التنبؤي والذكاء الاصطناعي',
                    icon: Icons.psychology,
                    onTap: () => Get.toNamed('/predictive-analytics'),
                    isActive: Get.currentRoute == '/predictive-analytics',
                  ),
                  _buildDrawerItem(
                    title: 'عمليات التجميع',
                    icon: Icons.build,
                    onTap: () => Get.toNamed('/assembly'),
                    isActive: Get.currentRoute == '/assembly',
                  ),
                  _buildDrawerItem(
                    title: 'تقارير التجميع',
                    icon: Icons.bar_chart,
                    onTap: () => Get.toNamed('/assembly-report'),
                    isActive: Get.currentRoute == '/assembly-report',
                  ),
                  _buildDrawerCategory('الطلبيات والمبيعات'),
                  _buildDrawerItem(
                    title: 'الطلبيات والاستيراد',
                    icon: Icons.shopping_bag,
                    onTap: () => Get.toNamed('/orders'),
                    isActive: Get.currentRoute == '/orders',
                  ),
                  _buildDrawerItem(
                    title: 'المبيعات والشحن',
                    icon: Icons.local_shipping,
                    onTap: () => Get.toNamed('/sales'),
                    isActive: Get.currentRoute == '/sales',
                  ),
                  _buildDrawerItem(
                    title: 'تقارير المبيعات',
                    icon: Icons.analytics,
                    onTap: () => Get.toNamed('/sales-report'),
                    isActive: Get.currentRoute == '/sales-report',
                  ),
                  _buildDrawerCategory('التقارير'),
                  _buildDrawerItem(
                    title: 'تقرير التكاليف',
                    icon: Icons.attach_money,
                    onTap: () => Get.toNamed('/cost-report'),
                    isActive: Get.currentRoute == '/cost-report',
                  ),
                  _buildDrawerCategory('المخزون والتقارير'),
                  _buildDrawerItem(
                    title: 'المخزون',
                    icon: Icons.inventory,
                    onTap: () => Get.toNamed('/inventory'),
                    isActive: Get.currentRoute == '/inventory',
                  ),
                  _buildDrawerItem(
                    title: 'تقرير المواد الخام',
                    icon: Icons.bar_chart,
                    onTap: () => Get.toNamed('/raw-material-report'),
                    isActive: Get.currentRoute == '/raw-material-report',
                  ),
                  _buildDrawerCategory('إعدادات النظام'),
                  _buildDrawerItem(
                    title: 'تكاليف التشغيل',
                    icon: Icons.money,
                    onTap: () => Get.toNamed('/operation-cost'),
                    isActive: Get.currentRoute == '/operation-cost',
                  ),
                  _buildDrawerItem(
                    title: 'النسخ الاحتياطي',
                    icon: Icons.backup,
                    onTap: () => Get.toNamed('/backup'),
                    isActive: Get.currentRoute == '/backup',
                  ),
                  _buildDrawerItem(
                    title: 'اختبارات الأداء',
                    icon: Icons.speed,
                    onTap: () => Get.toNamed('/performance-test'),
                    isActive: Get.currentRoute == '/performance-test',
                  ),
                ],
              ),
            ),
            const Divider(height: 1),
            _buildVersionInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeader() {
    // تهيئة أحجام الشاشة
    ScreenSize.init(Get.context!);

    return DrawerHeader(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            EnhancedAppTheme.primaryColor,
            EnhancedAppTheme.primaryColorDark,
          ],
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(ScreenSize.getPadding(2)),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(40),
                ),
                child: CircleAvatar(
                  backgroundColor: Colors.white,
                  radius: ScreenSize.isSmallScreen ? 20 : 24,
                  child: Icon(
                    Icons.factory,
                    color: EnhancedAppTheme.primaryColor,
                    size: ScreenSize.isSmallScreen ? 24 : 30,
                  ),
                ),
              ),
              SizedBox(width: ScreenSize.getPadding(12)),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextUtils.responsiveText(
                      'نظام إدارة المصنع',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: ScreenSize.isSmallScreen ? 16 : 20,
                        fontWeight: FontWeight.bold,
                        fontFamily: 'Cairo',
                      ),
                      maxLines: 1,
                    ),
                    TextUtils.responsiveText(
                      'الإنتاج والتكاليف',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        fontFamily: 'Cairo',
                      ),
                      maxLines: 1,
                    ),
                  ],
                ),
              ),
            ],
          ),
          const Spacer(),
          Row(
            children: [
              CircleAvatar(
                backgroundColor: Colors.white,
                radius: ScreenSize.isSmallScreen ? 14 : 16,
                child: Icon(
                  Icons.person,
                  color: EnhancedAppTheme.primaryColor,
                  size: ScreenSize.isSmallScreen ? 16 : 20,
                ),
              ),
              SizedBox(width: ScreenSize.getPadding(12)),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextUtils.responsiveText(
                    'مدير النظام',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Cairo',
                    ),
                    maxLines: 1,
                  ),
                  TextUtils.responsiveText(
                    'الجلسة الحالية',
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                      fontFamily: 'Cairo',
                    ),
                    maxLines: 1,
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerCategory(String title) {
    return Padding(
      padding: EdgeInsets.only(
        right: ScreenSize.getPadding(16),
        left: ScreenSize.getPadding(16),
        top: ScreenSize.getPadding(16),
        bottom: ScreenSize.getPadding(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextUtils.responsiveText(
              title,
              style: TextStyle(
                color: EnhancedAppTheme.textSecondary,
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                fontWeight: FontWeight.bold,
                fontFamily: 'Cairo',
              ),
              maxLines: 1,
            ),
          ),
          Container(
            height: 1,
            width: ScreenSize.isSmallScreen ? 60 : 80,
            color: EnhancedAppTheme.textLight,
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerItem({
    required String title,
    required IconData icon,
    required VoidCallback onTap,
    required bool isActive,
  }) {
    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(8),
        vertical: ScreenSize.getPadding(2),
      ),
      decoration: BoxDecoration(
        color: isActive ? EnhancedAppTheme.primaryColor.withAlpha(25) : null,
        borderRadius: BorderRadius.circular(8),
      ),
      child: ListTile(
        title: TextUtils.responsiveText(
          title,
          style: TextStyle(
            fontFamily: 'Cairo',
            fontSize: ScreenSize.isSmallScreen ? 13 : 15,
            color:
                isActive
                    ? EnhancedAppTheme.primaryColor
                    : EnhancedAppTheme.textPrimary,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
          maxLines: 1,
        ),
        leading: Icon(
          icon,
          color:
              isActive
                  ? EnhancedAppTheme.primaryColor
                  : EnhancedAppTheme.textSecondary,
          size: ScreenSize.isSmallScreen ? 20 : 24,
        ),
        onTap: onTap,
        dense: true,
        visualDensity:
            ScreenSize.isSmallScreen
                ? const VisualDensity(horizontal: -1, vertical: -1)
                : null,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  Widget _buildVersionInfo() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: ScreenSize.getPadding(8),
        horizontal: ScreenSize.getPadding(16),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.info_outline,
            size: ScreenSize.isSmallScreen ? 14 : 16,
            color: EnhancedAppTheme.textSecondary,
          ),
          SizedBox(width: ScreenSize.getPadding(8)),
          TextUtils.responsiveText(
            'الإصدار 1.0.0',
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 10 : 12,
              color: EnhancedAppTheme.textSecondary,
              fontFamily: 'Cairo',
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }
}
