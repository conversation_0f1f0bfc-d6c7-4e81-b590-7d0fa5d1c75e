import 'package:get/get.dart';
import 'package:mostafa_final/data/models/inventory.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/data/repositories/order_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/utils/logger.dart';

class DashboardController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final ProductionRepository _productionRepository =
      Get.find<ProductionRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();
  final InventoryRepository _inventoryRepository =
      Get.find<InventoryRepository>();
  final OrderRepository _orderRepository = Get.find<OrderRepository>();
  final SalesRepository _salesRepository = Get.find<SalesRepository>();

  // الاحتفاظ بـ isLoading كـ RxBool ولكن تحديث الواجهة عبر update()
  final RxBool isLoading = true.obs;

  // المتغيرات المستخدمة في صفحة لوحة التحكم
  int todayProduction = 0;
  int totalInventory = 0;
  int pendingOrders = 0;
  String monthlySales = '0';
  int activeMachines = 0;
  int totalMachines = 0;
  int expectedProduction = 0;
  int activeMolds = 0;
  int totalMolds = 0;
  int rawMaterialsCount = 0;
  int finishedProductsCount = 0;
  int lowStockCount = 0;

  // بيانات إضافية
  double totalRawMaterialsWeight = 0.0;

  // قوائم العناصر
  List<Map<String, dynamic>> recentProduction = <Map<String, dynamic>>[];
  List<Inventory> lowInventoryItems = <Inventory>[];

  @override
  void onInit() {
    super.onInit();
    fetchDashboardData();
  }

  // تحديث البيانات عند السحب للأسفل
  Future<void> refreshData() async {
    return fetchDashboardData();
  }

  // جلب بيانات لوحة التحكم
  Future<void> fetchDashboardData() async {
    isLoading.value = true;
    update(); // تحديث واجهة المستخدم

    try {
      await Future.wait([
        _fetchMachinesData(),
        _fetchMoldsData(),
        _fetchRawMaterialsData(),
        _fetchProductionData(),
        _fetchInventoryData(),
        _fetchOrdersData(),
        _fetchSalesData(),
      ]);
    } catch (e) {
      AppLogger.e('Error fetching dashboard data', e);
    } finally {
      isLoading.value = false;
      update(); // تحديث واجهة المستخدم مرة أخرى بعد الانتهاء
    }
  }

  // جلب بيانات الماكينات
  Future<void> _fetchMachinesData() async {
    try {
      final machines = await _machineRepository.getAllMachines();
      totalMachines = machines.length;
      activeMachines =
          machines.where((m) => m.status == MachineStatus.operating).length;
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching machines', e);
    }
  }

  // جلب بيانات الإسطمبات
  Future<void> _fetchMoldsData() async {
    try {
      final molds = await _moldRepository.getAllMolds();
      totalMolds = molds.length;
      activeMolds = molds.where((m) => m.status == MoldStatus.inUse).length;
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching molds', e);
    }
  }

  // جلب بيانات المواد الخام
  Future<void> _fetchRawMaterialsData() async {
    try {
      final materials = await _rawMaterialRepository.getAllRawMaterials();
      rawMaterialsCount = materials.length;

      // حساب إجمالي الوزن المتاح
      totalRawMaterialsWeight = materials.fold(
        0.0,
        (sum, material) => sum + material.availableQuantity,
      );
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching raw materials', e);
    }
  }

  // جلب بيانات الإنتاج
  Future<void> _fetchProductionData() async {
    try {
      // تحديد تاريخ اليوم
      final today = DateTime.now();
      final todayDate = DateTime(today.year, today.month, today.day);

      // جلب إنتاج اليوم
      final productionToday = await _productionRepository.getProductionByDate(
        todayDate,
      );

      // حساب إجمالي القطع المنتجة اليوم
      todayProduction = productionToday.fold(
        0,
        (sum, item) => sum + (item['partsProduced'] as int),
      );

      // حساب الإنتاج المتوقع (مثال: 20% أكثر من الإنتاج الحالي)
      expectedProduction = (todayProduction * 1.2).round();

      // تخزين آخر بيانات الإنتاج
      recentProduction = productionToday;
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching production', e);
    }
  }

  // جلب بيانات المخزون
  Future<void> _fetchInventoryData() async {
    try {
      // جلب عناصر المخزون
      final inventoryItems = await _inventoryRepository.getAllInventoryItems();
      totalInventory = inventoryItems.length;
      finishedProductsCount =
          inventoryItems.where((item) => item.itemType == 'finished').length;

      // جلب عناصر المخزون ذات المستوى المنخفض
      final items = await _inventoryRepository.getLowInventoryItems();
      lowInventoryItems = items;
      lowStockCount = items.length;
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching inventory', e);
    }
  }

  // جلب بيانات الطلبيات
  Future<void> _fetchOrdersData() async {
    try {
      // جلب جميع الطلبيات
      final orders = await _orderRepository.getAllOrders();

      // حساب عدد الطلبيات المعلقة (مثال: الطلبيات التي لم يتم استلامها بعد)
      pendingOrders = orders.length;
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching orders', e);
    }
  }

  // جلب بيانات المبيعات
  Future<void> _fetchSalesData() async {
    try {
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final endOfMonth = DateTime(now.year, now.month + 1, 0);

      final sales = await _salesRepository.getSalesByDateRange(
        startOfMonth,
        endOfMonth,
      );
      double totalSales = sales.fold(
        0.0,
        (sum, sale) => sum + (sale.totalAmount),
      );

      // تنسيق المبيعات بالجنيه المصري
      monthlySales = _formatCurrency(totalSales);
      update(); // تحديث الواجهة
    } catch (e) {
      AppLogger.e('Error fetching sales', e);
    }
  }

  // تنسيق العملة
  String _formatCurrency(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)} مليون';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(1)} ألف';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
