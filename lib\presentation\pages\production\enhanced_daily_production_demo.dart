import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'enhanced_daily_production_page.dart';

/// صفحة تجريبية للصفحة المحسنة
class EnhancedDailyProductionDemo extends StatelessWidget {
  const EnhancedDailyProductionDemo({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تجربة الصفحة المحسنة'),
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.engineering,
                size: 80,
                color: Colors.blue.shade600,
              ),
              const SizedBox(height: 20),
              const Text(
                'صفحة تسجيل الإنتاج اليومي المحسنة',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 10),
              Text(
                'تم تطوير صفحة محسنة بميزات متقدمة',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              
              // قائمة الميزات الجديدة
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'الميزات الجديدة:',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem('🎨 تصميم UI/UX محسن ومتجاوب'),
                    _buildFeatureItem('📊 إحصائيات سريعة تفاعلية'),
                    _buildFeatureItem('🔍 بحث وفلترة متقدمة'),
                    _buildFeatureItem('✏️ تعديل وحذف السجلات'),
                    _buildFeatureItem('📤 تصدير البيانات (PDF/Excel)'),
                    _buildFeatureItem('⚡ أداء محسن وتحميل سريع'),
                    _buildFeatureItem('🎯 validation محسن للبيانات'),
                    _buildFeatureItem('📱 responsive design للشاشات المختلفة'),
                  ],
                ),
              ),
              
              const SizedBox(height: 40),
              
              // أزرار التجربة
              Row(
                children: [
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Get.to(() => const EnhancedDailyProductionPage());
                      },
                      icon: const Icon(Icons.rocket_launch),
                      label: const Text('تجربة الصفحة الجديدة'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green.shade600,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: 12),
              
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        Get.back();
                      },
                      icon: const Icon(Icons.arrow_back),
                      label: const Text('العودة للصفحة الأصلية'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue.shade600,
                        padding: const EdgeInsets.symmetric(vertical: 15),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(
            Icons.check_circle,
            color: Colors.green.shade600,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
