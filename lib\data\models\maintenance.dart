/// نموذج بيانات الصيانة الوقائية
class Maintenance {
  final String? id;
  final String machineId;
  final MaintenanceType type; // نوع الصيانة
  final String title; // عنوان الصيانة
  final String description; // وصف الصيانة
  final MaintenanceFrequency frequency; // تكرار الصيانة
  final int intervalValue; // قيمة الفترة (ساعات أو دورات)
  final DateTime? lastPerformed; // آخر مرة تم تنفيذ الصيانة
  final DateTime? nextDue; // موعد الصيانة القادمة
  final MaintenanceStatus status; // حالة الصيانة
  final MaintenancePriority priority; // أولوية الصيانة
  final double? estimatedDuration; // المدة المتوقعة بالساعات
  final double? estimatedCost; // التكلفة المتوقعة
  final String? assignedTo; // المسؤول عن الصيانة
  final List<String>? requiredParts; // قطع الغيار المطلوبة
  final List<String>? requiredTools; // الأدوات المطلوبة
  final String? instructions; // تعليمات الصيانة
  final bool isActive; // هل الصيانة نشطة
  final DateTime createdAt;
  final DateTime? updatedAt;

  Maintenance({
    this.id,
    required this.machineId,
    required this.type,
    required this.title,
    required this.description,
    required this.frequency,
    required this.intervalValue,
    this.lastPerformed,
    this.nextDue,
    required this.status,
    required this.priority,
    this.estimatedDuration,
    this.estimatedCost,
    this.assignedTo,
    this.requiredParts,
    this.requiredTools,
    this.instructions,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  // هل الصيانة مستحقة؟
  bool get isDue {
    if (nextDue == null) return false;
    return DateTime.now().isAfter(nextDue!) ||
        DateTime.now().isAtSameMomentAs(nextDue!);
  }

  // هل الصيانة متأخرة؟
  bool get isOverdue {
    if (nextDue == null) return false;
    return DateTime.now().isAfter(nextDue!);
  }

  // عدد الأيام المتبقية للصيانة
  int get daysUntilDue {
    if (nextDue == null) return -1;
    return nextDue!.difference(DateTime.now()).inDays;
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'machineId': machineId,
      'type': type.index,
      'title': title,
      'description': description,
      'frequency': frequency.index,
      'intervalValue': intervalValue,
      'lastPerformed': lastPerformed?.toIso8601String(),
      'nextDue': nextDue?.toIso8601String(),
      'status': status.index,
      'priority': priority.index,
      'estimatedDuration': estimatedDuration,
      'estimatedCost': estimatedCost,
      'assignedTo': assignedTo,
      'requiredParts': requiredParts?.join(','),
      'requiredTools': requiredTools?.join(','),
      'instructions': instructions,
      'isActive': isActive ? 1 : 0,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory Maintenance.fromMap(Map<String, dynamic> map) {
    return Maintenance(
      id: map['id'],
      machineId: map['machineId'],
      type: MaintenanceType.values[map['type']],
      title: map['title'],
      description: map['description'],
      frequency: MaintenanceFrequency.values[map['frequency']],
      intervalValue: map['intervalValue'],
      lastPerformed:
          map['lastPerformed'] != null
              ? DateTime.parse(map['lastPerformed'])
              : null,
      nextDue: map['nextDue'] != null ? DateTime.parse(map['nextDue']) : null,
      status: MaintenanceStatus.values[map['status']],
      priority: MaintenancePriority.values[map['priority']],
      estimatedDuration: map['estimatedDuration'],
      estimatedCost: map['estimatedCost'],
      assignedTo: map['assignedTo'],
      requiredParts: map['requiredParts']?.split(','),
      requiredTools: map['requiredTools']?.split(','),
      instructions: map['instructions'],
      isActive: map['isActive'] == 1,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt:
          map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // إنشاء نسخة معدلة
  Maintenance copyWith({
    String? id,
    String? machineId,
    MaintenanceType? type,
    String? title,
    String? description,
    MaintenanceFrequency? frequency,
    int? intervalValue,
    DateTime? lastPerformed,
    DateTime? nextDue,
    MaintenanceStatus? status,
    MaintenancePriority? priority,
    double? estimatedDuration,
    double? estimatedCost,
    String? assignedTo,
    List<String>? requiredParts,
    List<String>? requiredTools,
    String? instructions,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Maintenance(
      id: id ?? this.id,
      machineId: machineId ?? this.machineId,
      type: type ?? this.type,
      title: title ?? this.title,
      description: description ?? this.description,
      frequency: frequency ?? this.frequency,
      intervalValue: intervalValue ?? this.intervalValue,
      lastPerformed: lastPerformed ?? this.lastPerformed,
      nextDue: nextDue ?? this.nextDue,
      status: status ?? this.status,
      priority: priority ?? this.priority,
      estimatedDuration: estimatedDuration ?? this.estimatedDuration,
      estimatedCost: estimatedCost ?? this.estimatedCost,
      assignedTo: assignedTo ?? this.assignedTo,
      requiredParts: requiredParts ?? this.requiredParts,
      requiredTools: requiredTools ?? this.requiredTools,
      instructions: instructions ?? this.instructions,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// أنواع الصيانة
enum MaintenanceType {
  preventive, // صيانة وقائية
  corrective, // صيانة تصحيحية
  predictive, // صيانة تنبؤية
  emergency, // صيانة طارئة
  routine, // صيانة روتينية
}

/// تكرار الصيانة
enum MaintenanceFrequency {
  hours, // بالساعات
  cycles, // بعدد الدورات
  days, // بالأيام
  weeks, // بالأسابيع
  months, // بالشهور
}

/// حالة الصيانة
enum MaintenanceStatus {
  scheduled, // مجدولة
  inProgress, // قيد التنفيذ
  completed, // مكتملة
  cancelled, // ملغية
  postponed, // مؤجلة
}

/// أولوية الصيانة
enum MaintenancePriority {
  low, // منخفضة
  medium, // متوسطة
  high, // عالية
  critical, // حرجة
}

/// إضافات مساعدة للتعامل مع الأنواع
extension MaintenanceTypeExtension on MaintenanceType {
  String get displayName {
    switch (this) {
      case MaintenanceType.preventive:
        return 'صيانة وقائية';
      case MaintenanceType.corrective:
        return 'صيانة تصحيحية';
      case MaintenanceType.predictive:
        return 'صيانة تنبؤية';
      case MaintenanceType.emergency:
        return 'صيانة طارئة';
      case MaintenanceType.routine:
        return 'صيانة روتينية';
    }
  }
}

extension MaintenanceFrequencyExtension on MaintenanceFrequency {
  String get displayName {
    switch (this) {
      case MaintenanceFrequency.hours:
        return 'ساعات';
      case MaintenanceFrequency.cycles:
        return 'دورات';
      case MaintenanceFrequency.days:
        return 'أيام';
      case MaintenanceFrequency.weeks:
        return 'أسابيع';
      case MaintenanceFrequency.months:
        return 'شهور';
    }
  }
}

extension MaintenanceStatusExtension on MaintenanceStatus {
  String get displayName {
    switch (this) {
      case MaintenanceStatus.scheduled:
        return 'مجدولة';
      case MaintenanceStatus.inProgress:
        return 'قيد التنفيذ';
      case MaintenanceStatus.completed:
        return 'مكتملة';
      case MaintenanceStatus.cancelled:
        return 'ملغية';
      case MaintenanceStatus.postponed:
        return 'مؤجلة';
    }
  }
}

extension MaintenancePriorityExtension on MaintenancePriority {
  String get displayName {
    switch (this) {
      case MaintenancePriority.low:
        return 'منخفضة';
      case MaintenancePriority.medium:
        return 'متوسطة';
      case MaintenancePriority.high:
        return 'عالية';
      case MaintenancePriority.critical:
        return 'حرجة';
    }
  }
}

/// نموذج سجل الصيانة المنجزة
class MaintenanceRecord {
  final String? id;
  final String maintenanceId; // معرف الصيانة الأصلية
  final String machineId;
  final DateTime startTime; // وقت بداية الصيانة
  final DateTime endTime; // وقت انتهاء الصيانة
  final String performedBy; // من قام بالصيانة
  final String workDescription; // وصف العمل المنجز
  final List<String>? partsUsed; // قطع الغيار المستخدمة
  final List<String>? toolsUsed; // الأدوات المستخدمة
  final double actualCost; // التكلفة الفعلية
  final MaintenanceResult result; // نتيجة الصيانة
  final String? notes; // ملاحظات إضافية
  final DateTime createdAt;

  MaintenanceRecord({
    this.id,
    required this.maintenanceId,
    required this.machineId,
    required this.startTime,
    required this.endTime,
    required this.performedBy,
    required this.workDescription,
    this.partsUsed,
    this.toolsUsed,
    required this.actualCost,
    required this.result,
    this.notes,
    required this.createdAt,
  });

  // مدة الصيانة بالساعات
  double get durationHours {
    return endTime.difference(startTime).inMinutes / 60.0;
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'maintenanceId': maintenanceId,
      'machineId': machineId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime.toIso8601String(),
      'performedBy': performedBy,
      'workDescription': workDescription,
      'partsUsed': partsUsed?.join(','),
      'toolsUsed': toolsUsed?.join(','),
      'actualCost': actualCost,
      'result': result.index,
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory MaintenanceRecord.fromMap(Map<String, dynamic> map) {
    return MaintenanceRecord(
      id: map['id'],
      maintenanceId: map['maintenanceId'],
      machineId: map['machineId'],
      startTime: DateTime.parse(map['startTime']),
      endTime: DateTime.parse(map['endTime']),
      performedBy: map['performedBy'],
      workDescription: map['workDescription'],
      partsUsed: map['partsUsed']?.split(','),
      toolsUsed: map['toolsUsed']?.split(','),
      actualCost: map['actualCost'],
      result: MaintenanceResult.values[map['result']],
      notes: map['notes'],
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  // إنشاء نسخة معدلة
  MaintenanceRecord copyWith({
    String? id,
    String? maintenanceId,
    String? machineId,
    DateTime? startTime,
    DateTime? endTime,
    String? performedBy,
    String? workDescription,
    List<String>? partsUsed,
    List<String>? toolsUsed,
    double? actualCost,
    MaintenanceResult? result,
    String? notes,
    DateTime? createdAt,
  }) {
    return MaintenanceRecord(
      id: id ?? this.id,
      maintenanceId: maintenanceId ?? this.maintenanceId,
      machineId: machineId ?? this.machineId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      performedBy: performedBy ?? this.performedBy,
      workDescription: workDescription ?? this.workDescription,
      partsUsed: partsUsed ?? this.partsUsed,
      toolsUsed: toolsUsed ?? this.toolsUsed,
      actualCost: actualCost ?? this.actualCost,
      result: result ?? this.result,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

/// نتيجة الصيانة
enum MaintenanceResult {
  successful, // نجحت
  partial, // جزئية
  failed, // فشلت
  postponed, // أُجلت
}

extension MaintenanceResultExtension on MaintenanceResult {
  String get displayName {
    switch (this) {
      case MaintenanceResult.successful:
        return 'نجحت';
      case MaintenanceResult.partial:
        return 'جزئية';
      case MaintenanceResult.failed:
        return 'فشلت';
      case MaintenanceResult.postponed:
        return 'أُجلت';
    }
  }
}
