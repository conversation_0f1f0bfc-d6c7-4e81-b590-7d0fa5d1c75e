import 'package:flutter/material.dart';

/// مساعد لتحديد حجم الشاشة وتطبيق التنسيقات المناسبة
class ScreenSize {
  static late MediaQueryData _mediaQueryData;
  static late double screenWidth;
  static late double screenHeight;
  static late double blockSizeHorizontal;
  static late double blockSizeVertical;
  static late double safeAreaHorizontal;
  static late double safeAreaVertical;
  static late double safeBlockHorizontal;
  static late double safeBlockVertical;
  static late bool isSmallScreen;
  static late bool isMediumScreen;
  static late bool isLargeScreen;
  static late Orientation orientation;

  /// تهيئة أحجام الشاشة
  static void init(BuildContext context) {
    _mediaQueryData = MediaQuery.of(context);
    screenWidth = _mediaQueryData.size.width;
    screenHeight = _mediaQueryData.size.height;
    orientation = _mediaQueryData.orientation;

    // تحديد نوع الشاشة بناءً على العرض
    isSmallScreen = screenWidth < 600;
    isMediumScreen = screenWidth >= 600 && screenWidth < 900;
    isLargeScreen = screenWidth >= 900;

    // حساب وحدات القياس النسبية
    blockSizeHorizontal = screenWidth / 100;
    blockSizeVertical = screenHeight / 100;

    // حساب المساحة الآمنة (بعد خصم الشريط العلوي والسفلي)
    safeAreaHorizontal =
        _mediaQueryData.padding.left + _mediaQueryData.padding.right;
    safeAreaVertical =
        _mediaQueryData.padding.top + _mediaQueryData.padding.bottom;
    safeBlockHorizontal = (screenWidth - safeAreaHorizontal) / 100;
    safeBlockVertical = (screenHeight - safeAreaVertical) / 100;
  }

  /// الحصول على عرض نسبي بناءً على نسبة مئوية من عرض الشاشة
  static double getWidthPercentage(double percentage) {
    return screenWidth * (percentage / 100);
  }

  /// الحصول على ارتفاع نسبي بناءً على نسبة مئوية من ارتفاع الشاشة
  static double getHeightPercentage(double percentage) {
    return screenHeight * (percentage / 100);
  }

  /// الحصول على حجم خط مناسب بناءً على حجم الشاشة
  static double getFontSize(double size) {
    if (isSmallScreen) {
      return size * 0.8;
    } else if (isMediumScreen) {
      return size * 0.9;
    } else {
      return size;
    }
  }

  /// الحصول على هامش مناسب بناءً على حجم الشاشة
  static double getMargin(double margin) {
    if (isSmallScreen) {
      return margin * 0.8;
    } else if (isMediumScreen) {
      return margin * 0.9;
    } else {
      return margin;
    }
  }

  /// الحصول على حشو مناسب بناءً على حجم الشاشة
  static double getPadding(double padding) {
    if (isSmallScreen) {
      return padding * 0.8;
    } else if (isMediumScreen) {
      return padding * 0.9;
    } else {
      return padding;
    }
  }
}
