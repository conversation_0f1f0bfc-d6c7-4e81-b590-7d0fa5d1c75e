import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/sale.dart';
import 'package:uuid/uuid.dart';

class SalesRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء جداول المبيعات (يتم استدعاؤها من DatabaseHelper عند تهيئة قاعدة البيانات)
  Future<void> createSalesTables(dynamic db) async {
    // جدول المبيعات
    await db.execute('''
      CREATE TABLE ${DatabaseHelper.tableSales} (
        id TEXT PRIMARY KEY,
        invoiceNumber TEXT NOT NULL,
        date TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT NOT NULL,
        shippingAddress TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        shippingStatus TEXT NOT NULL
      )
    ''');

    // جدول عناصر المبيعات
    await db.execute('''
      CREATE TABLE ${DatabaseHelper.tableSaleItems} (
        id TEXT PRIMARY KEY,
        saleId TEXT NOT NULL,
        productId TEXT NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        FOREIGN KEY (saleId) REFERENCES ${DatabaseHelper.tableSales} (id) ON DELETE CASCADE
      )
    ''');
  }

  // إنشاء عملية بيع جديدة
  Future<String> createSale(Sale sale) async {
    // إنشاء معرف جديد لعملية البيع إذا لم يكن موجوداً
    final saleId = sale.id ?? _uuid.v4();

    // إنشاء كائن البيع مع المعرف الجديد
    final saleWithId = Sale(
      id: saleId,
      invoiceNumber: sale.invoiceNumber,
      date: sale.date,
      customerName: sale.customerName,
      customerPhone: sale.customerPhone,
      shippingAddress: sale.shippingAddress,
      items: sale.items,
      totalAmount: sale.totalAmount,
      shippingStatus: sale.shippingStatus,
    );

    // حفظ البيع في قاعدة البيانات
    await _databaseHelper.insert(DatabaseHelper.tableSales, saleWithId.toMap());

    // حفظ عناصر البيع
    for (var item in saleWithId.items) {
      final itemWithIds = SaleItem(
        id: _uuid.v4(),
        saleId: saleId,
        productId: item.productId,
        productName: item.productName,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        totalPrice: item.totalPrice,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableSaleItems,
        itemWithIds.toMap(),
      );
    }

    return saleId;
  }

  // الحصول على جميع عمليات البيع
  Future<List<Sale>> getAllSales() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableSales,
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getSaleItems(map['id']);
        return Sale.fromMap(map, items);
      }).toList(),
    );
  }

  // الحصول على عمليات البيع بتاريخ معين
  Future<List<Sale>> getSalesByDate(DateTime date) async {
    final dateStr = date.toString().split(' ')[0];

    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableSales} WHERE date(date) = ?',
      [dateStr],
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getSaleItems(map['id']);
        return Sale.fromMap(map, items);
      }).toList(),
    );
  }

  // الحصول على عملية بيع بمعرف معين
  Future<Sale?> getSaleById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableSales,
      id,
    );

    if (map == null) {
      return null;
    }

    final items = await _getSaleItems(id);
    return Sale.fromMap(map, items);
  }

  // تحديث حالة عملية بيع
  Future<int> updateSaleStatus(String id, String status) async {
    return await _databaseHelper
        .rawQuery(
          'UPDATE ${DatabaseHelper.tableSales} SET shippingStatus = ? WHERE id = ?',
          [status, id],
        )
        .then((_) => 1);
  }

  // حذف عملية بيع
  Future<int> deleteSale(String id) async {
    // حذف عناصر البيع أولاً (سيتم حذفها تلقائياً بسبب ON DELETE CASCADE)
    return await _databaseHelper.delete(DatabaseHelper.tableSales, id);
  }

  // الحصول على عناصر عملية بيع معينة
  Future<List<SaleItem>> _getSaleItems(String saleId) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableSaleItems} WHERE saleId = ?',
      [saleId],
    );

    return maps.map((map) => SaleItem.fromMap(map)).toList();
  }

  // الحصول على إحصائيات المبيعات
  Future<Map<String, dynamic>> getSalesStatistics() async {
    // إجمالي عدد المبيعات
    final totalSalesResult = await _databaseHelper.rawQuery(
      'SELECT COUNT(id) as count FROM ${DatabaseHelper.tableSales}',
    );

    final int totalSales = totalSalesResult.first['count'] ?? 0;

    // إجمالي قيمة المبيعات
    final totalAmountResult = await _databaseHelper.rawQuery(
      'SELECT SUM(totalAmount) as total FROM ${DatabaseHelper.tableSales}',
    );

    final double totalAmount = totalAmountResult.first['total'] ?? 0.0;

    // عدد المبيعات حسب الحالة
    final statusCounts = <String, int>{};

    final statusesResult = await _databaseHelper.rawQuery(
      'SELECT shippingStatus, COUNT(id) as count FROM ${DatabaseHelper.tableSales} GROUP BY shippingStatus',
    );

    for (final row in statusesResult) {
      statusCounts[row['shippingStatus'] as String] = row['count'] as int;
    }

    return {
      'totalSales': totalSales,
      'totalAmount': totalAmount,
      'statusCounts': statusCounts,
    };
  }

  // الحصول على المبيعات في فترة زمنية محددة
  Future<List<Sale>> getSalesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final startDateStr = startDate.toString().split(' ')[0];
    final endDateStr = endDate.toString().split(' ')[0];

    // إضافة تخزين مؤقت للاستعلام
    final cacheKey =
        'sales_by_date_range_$startDateStr'
        '_$endDateStr';
    final cachedResult = await _getCachedSalesResult(cacheKey);

    if (cachedResult != null) {
      return cachedResult;
    }

    // تحسين الاستعلام باستخدام الفهرس المضاف على حقل date
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableSales} WHERE date(date) >= ? AND date(date) <= ? ORDER BY date DESC LIMIT 1000',
      [startDateStr, endDateStr],
    );

    // تحسين أداء استعلامات JOIN باستخدام استعلام واحد لجلب جميع عناصر المبيعات
    final List<String> saleIds =
        maps.map((map) => map['id'] as String).toList();

    if (saleIds.isEmpty) {
      return [];
    }

    // استعلام واحد لجلب جميع عناصر المبيعات للمبيعات المحددة
    final String placeholders = List.filled(saleIds.length, '?').join(',');
    final List<Map<String, dynamic>>
    allItemsMaps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableSaleItems} WHERE saleId IN ($placeholders)',
      saleIds,
    );

    // تنظيم عناصر المبيعات حسب معرف المبيعات
    final Map<String, List<SaleItem>> itemsBySaleId = {};
    for (final itemMap in allItemsMaps) {
      final saleId = itemMap['saleId'] as String;
      itemsBySaleId[saleId] = itemsBySaleId[saleId] ?? [];
      itemsBySaleId[saleId]!.add(SaleItem.fromMap(itemMap));
    }

    // إنشاء كائنات المبيعات مع عناصرها
    final List<Sale> sales =
        maps.map((map) {
          final saleId = map['id'] as String;
          return Sale.fromMap(map, itemsBySaleId[saleId] ?? []);
        }).toList();

    // تخزين النتيجة في التخزين المؤقت
    await _cacheSalesResult(cacheKey, sales);

    return sales;
  }

  // تخزين نتيجة استعلام المبيعات في التخزين المؤقت
  Future<void> _cacheSalesResult(String key, List<Sale> result) async {
    _salesCache[key] = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': result,
    };
  }

  // الحصول على نتيجة مخزنة مؤقتًا للمبيعات
  Future<List<Sale>?> _getCachedSalesResult(String key) async {
    final cachedData = _salesCache[key];
    if (cachedData != null) {
      final timestamp = cachedData['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      // التحقق من صلاحية التخزين المؤقت (5 دقائق)
      if (now - timestamp < 5 * 60 * 1000) {
        return cachedData['data'] as List<Sale>;
      }
    }
    return null;
  }

  // تخزين مؤقت للمبيعات
  final Map<String, Map<String, dynamic>> _salesCache = {};

  // تخزين مؤقت للاستعلامات
  final Map<String, Map<String, dynamic>> _queryCache = {};

  // الحصول على أكثر المنتجات مبيعاً
  Future<List<Map<String, dynamic>>> getTopSellingProducts(int limit) async {
    // إضافة تخزين مؤقت للاستعلام
    final cacheKey = 'top_selling_products_$limit';
    final cachedResult = await _getCachedResult(cacheKey);

    if (cachedResult != null) {
      return cachedResult;
    }

    // تحسين الاستعلام باستخدام الفهارس المضافة
    final List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(
      '''
      SELECT productId, productName,
             SUM(quantity) as totalQuantity,
             SUM(totalPrice) as totalRevenue,
             SUM(totalPrice) / SUM(quantity) as avgUnitPrice
      FROM ${DatabaseHelper.tableSaleItems}
      GROUP BY productId
      ORDER BY totalQuantity DESC
      LIMIT ?
    ''',
      [limit],
    );

    // تخزين النتيجة في التخزين المؤقت
    await _cacheResult(cacheKey, result);

    return result;
  }

  // تخزين نتيجة الاستعلام في التخزين المؤقت
  Future<void> _cacheResult(
    String key,
    List<Map<String, dynamic>> result,
  ) async {
    _queryCache[key] = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': result,
    };
  }

  // الحصول على نتيجة مخزنة مؤقتًا
  Future<List<Map<String, dynamic>>?> _getCachedResult(String key) async {
    final cachedData = _queryCache[key];
    if (cachedData != null) {
      final timestamp = cachedData['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      // التحقق من صلاحية التخزين المؤقت (10 دقائق)
      if (now - timestamp < 10 * 60 * 1000) {
        return cachedData['data'] as List<Map<String, dynamic>>;
      }
    }
    return null;
  }
}
