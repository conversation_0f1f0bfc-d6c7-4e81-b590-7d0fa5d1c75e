import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';

/// حاوية متجاوبة مع حجم الشاشة
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final double? width;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Alignment? alignment;
  final BoxConstraints? constraints;
  final Color? color;
  final Decoration? decoration;
  final BorderRadius? borderRadius;
  final double? widthFactor;
  final double? heightFactor;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.width,
    this.height,
    this.padding,
    this.margin,
    this.alignment,
    this.constraints,
    this.color,
    this.decoration,
    this.borderRadius,
    this.widthFactor,
    this.heightFactor,
  });

  @override
  Widget build(BuildContext context) {
    // تطبيق التنسيق المتجاوب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding =
        padding != null
            ? EdgeInsets.only(
              left: ScreenSize.getPadding((padding as EdgeInsets).left),
              top: ScreenSize.getPadding((padding as EdgeInsets).top),
              right: ScreenSize.getPadding((padding as EdgeInsets).right),
              bottom: ScreenSize.getPadding((padding as EdgeInsets).bottom),
            )
            : EdgeInsets.zero;

    final EdgeInsetsGeometry responsiveMargin =
        margin != null
            ? EdgeInsets.only(
              left: ScreenSize.getMargin((margin as EdgeInsets).left),
              top: ScreenSize.getMargin((margin as EdgeInsets).top),
              right: ScreenSize.getMargin((margin as EdgeInsets).right),
              bottom: ScreenSize.getMargin((margin as EdgeInsets).bottom),
            )
            : EdgeInsets.zero;

    return Container(
      width: width != null ? ScreenSize.getWidthPercentage(width!) : null,
      height: height != null ? ScreenSize.getHeightPercentage(height!) : null,
      padding: responsivePadding,
      margin: responsiveMargin,
      alignment: alignment,
      constraints: constraints,
      decoration:
          decoration ??
          (borderRadius != null || color != null
              ? BoxDecoration(color: color, borderRadius: borderRadius)
              : null),
      child: child,
    );
  }

  /// إنشاء حاوية متجاوبة بعرض كامل
  static Widget fullWidth({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Alignment? alignment,
    Color? color,
    Decoration? decoration,
    BorderRadius? borderRadius,
  }) {
    return ResponsiveContainer(
      width: 100, // 100% من عرض الشاشة
      padding: padding,
      margin: margin,
      alignment: alignment,
      color: color,
      decoration: decoration,
      borderRadius: borderRadius,
      child: child,
    );
  }

  /// إنشاء حاوية متجاوبة بعرض نصف الشاشة
  static Widget halfWidth({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Alignment? alignment,
    Color? color,
    Decoration? decoration,
    BorderRadius? borderRadius,
  }) {
    return ResponsiveContainer(
      width: 50, // 50% من عرض الشاشة
      padding: padding,
      margin: margin,
      alignment: alignment,
      color: color,
      decoration: decoration,
      borderRadius: borderRadius,
      child: child,
    );
  }
}

/// امتداد للتعامل مع الحاويات المتجاوبة
extension ResponsiveWidgetExtension on Widget {
  /// تغليف العنصر في حاوية متجاوبة
  Widget responsiveContainer({
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Alignment? alignment,
    Color? color,
    Decoration? decoration,
    BorderRadius? borderRadius,
  }) {
    return ResponsiveContainer(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      color: color,
      decoration: decoration,
      borderRadius: borderRadius,
      child: this,
    );
  }
}
