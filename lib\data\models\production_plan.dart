class ProductionPlan {
  final String? id;
  final DateTime startDate;
  final DateTime endDate;
  final List<ProductionPlanItem> items;
  final String status; // حالة الخطة (مخطط، قيد التنفيذ، مكتمل)

  ProductionPlan({
    this.id,
    required this.startDate,
    required this.endDate,
    required this.items,
    required this.status,
  });

  // تحويل ProductionPlan إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'status': status,
    };
  }

  // إنشاء ProductionPlan من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory ProductionPlan.fromMap(
    Map<String, dynamic> map,
    List<ProductionPlanItem> items,
  ) {
    return ProductionPlan(
      id: map['id'],
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      items: items,
      status: map['status'],
    );
  }
}

class ProductionPlanItem {
  final String? id;
  final String? planId;
  final String productId;
  final int quantity;
  final int priority; // أولوية الإنتاج (1 هي الأعلى)
  final DateTime? plannedStart;
  final DateTime? plannedEnd;
  final int completedQuantity; // الكمية المكتملة

  ProductionPlanItem({
    this.id,
    this.planId,
    required this.productId,
    required this.quantity,
    required this.priority,
    this.plannedStart,
    this.plannedEnd,
    required this.completedQuantity,
  });

  // تحويل ProductionPlanItem إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'planId': planId,
      'productId': productId,
      'quantity': quantity,
      'priority': priority,
      'plannedStart': plannedStart?.toIso8601String(),
      'plannedEnd': plannedEnd?.toIso8601String(),
      'completedQuantity': completedQuantity,
    };
  }

  // إنشاء ProductionPlanItem من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory ProductionPlanItem.fromMap(Map<String, dynamic> map) {
    return ProductionPlanItem(
      id: map['id'],
      planId: map['planId'],
      productId: map['productId'],
      quantity: map['quantity'],
      priority: map['priority'],
      plannedStart:
          map['plannedStart'] != null
              ? DateTime.parse(map['plannedStart'])
              : null,
      plannedEnd:
          map['plannedEnd'] != null ? DateTime.parse(map['plannedEnd']) : null,
      completedQuantity: map['completedQuantity'],
    );
  }
}

// حالات خطة الإنتاج
class ProductionPlanStatus {
  static const String planned = 'مخطط';
  static const String inProgress = 'قيد التنفيذ';
  static const String completed = 'مكتمل';
}
