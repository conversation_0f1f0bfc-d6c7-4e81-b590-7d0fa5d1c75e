import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';

class RawMaterialsController extends GetxController {
  final RawMaterialRepository _repository = Get.find<RawMaterialRepository>();

  final RxBool isLoading = false.obs;
  final RxBool isLoadingReceipts = false.obs;
  final RxList<RawMaterial> rawMaterials = <RawMaterial>[].obs;
  final RxList<RawMaterial> filteredMaterials = <RawMaterial>[].obs;
  final RxList<RawMaterialReceipt> materialReceipts =
      <RawMaterialReceipt>[].obs;

  // للبحث
  final TextEditingController searchController = TextEditingController();

  // لإضافة/تعديل المواد الخام
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();
  final TextEditingController nameController = TextEditingController();
  final TextEditingController codeController = TextEditingController();
  final TextEditingController colorController = TextEditingController();
  final TextEditingController quantityController = TextEditingController();
  final TextEditingController costPerKgController = TextEditingController();

  // لإضافة استلام مواد خام
  final GlobalKey<FormState> receiptFormKey = GlobalKey<FormState>();
  final TextEditingController newQuantityController = TextEditingController();
  final TextEditingController newPriceController = TextEditingController();

  @override
  void onInit() {
    super.onInit();
    fetchRawMaterials();

    // استماع لتغييرات البحث
    searchController.addListener(() {
      onSearchTextChanged(searchController.text);
    });
  }

  @override
  void onClose() {
    searchController.dispose();
    nameController.dispose();
    codeController.dispose();
    colorController.dispose();
    quantityController.dispose();
    costPerKgController.dispose();
    newQuantityController.dispose();
    newPriceController.dispose();
    super.onClose();
  }

  // جلب قائمة المواد الخام
  Future<void> fetchRawMaterials() async {
    isLoading.value = true;
    try {
      final materials = await _repository.getAllRawMaterials();
      rawMaterials.assignAll(materials);
      filteredMaterials.assignAll(materials);
    } catch (e) {
      _showError('حدث خطأ أثناء جلب المواد الخام: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // البحث في المواد الخام
  void onSearchTextChanged(String text) {
    if (text.trim().isEmpty) {
      filteredMaterials.assignAll(rawMaterials);
      return;
    }

    final lowerCaseQuery = text.toLowerCase();

    final filtered =
        rawMaterials.where((material) {
          return material.name.toLowerCase().contains(lowerCaseQuery) ||
              material.code.toLowerCase().contains(lowerCaseQuery) ||
              material.color.toLowerCase().contains(lowerCaseQuery);
        }).toList();

    filteredMaterials.assignAll(filtered);
  }

  // إعادة تعيين نموذج إضافة مادة خام
  void resetMaterialForm() {
    nameController.clear();
    codeController.clear();
    colorController.clear();
    quantityController.clear();
    costPerKgController.clear();
  }

  // تهيئة نموذج تعديل مادة خام
  void setupEditForm(RawMaterial material) {
    nameController.text = material.name;
    codeController.text = material.code;
    colorController.text = material.color;
    quantityController.text = material.availableQuantity.toString();
    costPerKgController.text = material.costPerKg.toString();
  }

  // إضافة مادة خام جديدة
  Future<void> addRawMaterial() async {
    isLoading.value = true;
    try {
      final newMaterial = RawMaterial(
        name: nameController.text,
        code: codeController.text,
        color: colorController.text,
        availableQuantity: double.parse(quantityController.text),
        costPerKg: double.parse(costPerKgController.text),
      );

      await _repository.createRawMaterial(newMaterial);
      await fetchRawMaterials();
      _showSuccess('تم إضافة المادة الخام بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة المادة الخام: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحديث مادة خام
  Future<void> updateRawMaterial(String id) async {
    isLoading.value = true;
    try {
      final material = rawMaterials.firstWhere((m) => m.id == id);

      final updatedMaterial = RawMaterial(
        id: id,
        name: nameController.text,
        code: codeController.text,
        color: colorController.text,
        availableQuantity: material.availableQuantity,
        costPerKg: double.parse(costPerKgController.text),
      );

      await _repository.updateRawMaterial(updatedMaterial);
      await fetchRawMaterials();
      _showSuccess('تم تحديث المادة الخام بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث المادة الخام: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // إعادة تعيين نموذج إضافة استلام
  void resetReceiptForm() {
    newQuantityController.clear();
    newPriceController.clear();
  }

  // إضافة استلام مادة خام
  Future<void> addRawMaterialReceipt(String materialId) async {
    isLoading.value = true;
    try {
      final receipt = RawMaterialReceipt(
        rawMaterialId: materialId,
        date: DateTime.now(),
        quantity: double.parse(newQuantityController.text),
        pricePerKg: double.parse(newPriceController.text),
      );

      await _repository.addRawMaterialReceipt(receipt);
      await fetchRawMaterials();
      _showSuccess('تم إضافة كمية جديدة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة كمية جديدة: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل سجل استلام المواد الخام
  Future<void> loadMaterialReceipts(String materialId) async {
    isLoadingReceipts.value = true;
    try {
      final receipts = await _repository.getRawMaterialReceipts(materialId);
      materialReceipts.assignAll(receipts);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل سجل الاستلام: $e');
    } finally {
      isLoadingReceipts.value = false;
    }
  }

  // تنسيق التاريخ
  String formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  // عرض رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
