import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// نموذج متجاوب مع حجم الشاشة
class ResponsiveForm extends StatelessWidget {
  final GlobalKey<FormState> formKey;
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final double? spacing;
  final bool autovalidateMode;
  final VoidCallback? onSubmit;
  final String? submitButtonText;
  final IconData? submitButtonIcon;
  final bool isLoading;
  final Widget? loadingWidget;

  const ResponsiveForm({
    super.key,
    required this.formKey,
    required this.children,
    this.padding,
    this.spacing,
    this.autovalidateMode = false,
    this.onSubmit,
    this.submitButtonText,
    this.submitButtonIcon,
    this.isLoading = false,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    // تحديد المسافة بين الحقول بناءً على حجم الشاشة
    final double fieldSpacing = spacing ?? ScreenSize.getPadding(16);

    // إضافة مسافات بين الحقول
    final List<Widget> formChildren = [];
    for (int i = 0; i < children.length; i++) {
      formChildren.add(children[i]);
      if (i < children.length - 1) {
        formChildren.add(SizedBox(height: fieldSpacing));
      }
    }

    // إضافة زر الإرسال إذا تم تحديده
    if (onSubmit != null && submitButtonText != null) {
      formChildren.add(SizedBox(height: fieldSpacing * 1.5));
      formChildren.add(
        SizedBox(
          width: double.infinity,
          height: ScreenSize.isSmallScreen ? 48 : 56,
          child: ElevatedButton(
            onPressed: isLoading ? null : onSubmit,
            child:
                isLoading
                    ? (loadingWidget ??
                        const SizedBox(
                          width: 24,
                          height: 24,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            strokeWidth: 2,
                          ),
                        ))
                    : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (submitButtonIcon != null) ...[
                          Icon(submitButtonIcon),
                          SizedBox(width: ScreenSize.getPadding(8)),
                        ],
                        TextUtils.responsiveText(
                          submitButtonText!,
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
          ),
        ),
      );
    }

    return Form(
      key: formKey,
      autovalidateMode:
          autovalidateMode
              ? AutovalidateMode.onUserInteraction
              : AutovalidateMode.disabled,
      child: Padding(
        padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: formChildren,
        ),
      ),
    );
  }

  /// إنشاء حقل نص متجاوب
  static Widget textField({
    required String label,
    required TextEditingController controller,
    String? hint,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconPressed,
    bool obscureText = false,
    TextInputType keyboardType = TextInputType.text,
    String? Function(String?)? validator,
    int? maxLines = 1,
    int? minLines,
    bool enabled = true,
    bool readOnly = false,
    VoidCallback? onTap,
    Function(String)? onChanged,
    Function(String)? onSubmitted,
    TextAlign textAlign = TextAlign.start,
    String? suffixText,
    String? prefixText,
    Widget? suffix,
    Widget? prefix,
    bool autofocus = false,
    FocusNode? focusNode,
    TextInputAction? textInputAction,
    List<TextInputFormatter>? inputFormatters,
  }) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(Get.context!);

    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefixIcon != null ? Icon(prefixIcon) : null,
        suffixIcon:
            suffixIcon != null
                ? IconButton(
                  icon: Icon(suffixIcon),
                  onPressed: onSuffixIconPressed,
                )
                : null,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: EdgeInsets.symmetric(
          horizontal: ScreenSize.getPadding(16),
          vertical: ScreenSize.getPadding(12),
        ),
        suffixText: suffixText,
        prefixText: prefixText,
        suffix: suffix,
        prefix: prefix,
      ),
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      maxLines: maxLines,
      minLines: minLines,
      enabled: enabled,
      readOnly: readOnly,
      onTap: onTap,
      onChanged: onChanged,
      onFieldSubmitted: onSubmitted,
      textAlign: textAlign,
      autofocus: autofocus,
      focusNode: focusNode,
      textInputAction: textInputAction,
      inputFormatters: inputFormatters,
      style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 14 : 16),
    );
  }
}
