import 'package:flutter/foundation.dart';
import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/breakdown.dart';
import 'package:mostafa_final/data/models/maintenance.dart';
import 'package:uuid/uuid.dart';

class MaintenanceRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  static const String tableBreakdowns = 'breakdowns';
  static const String tableMaintenance = 'maintenance';
  static const String tableMaintenanceRecords = 'maintenance_records';

  // إنشاء المستودع وضمان وجود الجداول
  MaintenanceRepository() {
    _initializeTables();
  }

  // تهيئة الجداول
  Future<void> _initializeTables() async {
    try {
      await createTables();
    } catch (e) {
      debugPrint('خطأ في تهيئة جداول الصيانة: $e');
    }
  }

  // إنشاء الجداول المطلوبة
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    // جدول الأعطال
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableBreakdowns (
        id TEXT PRIMARY KEY,
        machineId TEXT NOT NULL,
        startTime TEXT NOT NULL,
        endTime TEXT,
        type INTEGER NOT NULL,
        cause INTEGER NOT NULL,
        description TEXT NOT NULL,
        severity INTEGER NOT NULL,
        reportedBy TEXT,
        fixedBy TEXT,
        fixDescription TEXT,
        repairCost REAL,
        downtimeHours REAL,
        spareParts TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (machineId) REFERENCES machines (id)
      )
    ''');

    // جدول الصيانة الوقائية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableMaintenance (
        id TEXT PRIMARY KEY,
        machineId TEXT NOT NULL,
        type INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        frequency INTEGER NOT NULL,
        intervalValue INTEGER NOT NULL,
        lastPerformed TEXT,
        nextDue TEXT,
        status INTEGER NOT NULL,
        priority INTEGER NOT NULL,
        estimatedDuration REAL,
        estimatedCost REAL,
        assignedTo TEXT,
        requiredParts TEXT,
        requiredTools TEXT,
        instructions TEXT,
        isActive INTEGER NOT NULL DEFAULT 1,
        createdAt TEXT NOT NULL,
        updatedAt TEXT,
        FOREIGN KEY (machineId) REFERENCES machines (id)
      )
    ''');

    // جدول سجلات الصيانة المنجزة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableMaintenanceRecords (
        id TEXT PRIMARY KEY,
        maintenanceId TEXT NOT NULL,
        machineId TEXT NOT NULL,
        startTime TEXT NOT NULL,
        endTime TEXT NOT NULL,
        performedBy TEXT NOT NULL,
        workDescription TEXT NOT NULL,
        partsUsed TEXT,
        toolsUsed TEXT,
        actualCost REAL NOT NULL,
        result INTEGER NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        FOREIGN KEY (maintenanceId) REFERENCES $tableMaintenance (id),
        FOREIGN KEY (machineId) REFERENCES machines (id)
      )
    ''');

    // إنشاء الفهارس
    await _createIndexes(db);
  }

  // إنشاء الفهارس لتحسين الأداء
  Future<void> _createIndexes(db) async {
    // فهارس جدول الأعطال
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_breakdowns_machine ON $tableBreakdowns (machineId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_breakdowns_start_time ON $tableBreakdowns (startTime)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_breakdowns_type ON $tableBreakdowns (type)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_breakdowns_severity ON $tableBreakdowns (severity)',
    );

    // فهارس جدول الصيانة
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_machine ON $tableMaintenance (machineId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_next_due ON $tableMaintenance (nextDue)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_status ON $tableMaintenance (status)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_priority ON $tableMaintenance (priority)',
    );

    // فهارس جدول سجلات الصيانة
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_records_maintenance ON $tableMaintenanceRecords (maintenanceId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_records_machine ON $tableMaintenanceRecords (machineId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_maintenance_records_start_time ON $tableMaintenanceRecords (startTime)',
    );
  }

  // === طرق إدارة الأعطال ===

  // إضافة عطل جديد
  Future<String> addBreakdown(Breakdown breakdown) async {
    final breakdownWithId = breakdown.copyWith(
      id: _uuid.v4(),
      createdAt: DateTime.now(),
    );

    await _databaseHelper.insert(tableBreakdowns, breakdownWithId.toMap());
    return breakdownWithId.id!;
  }

  // تحديث عطل
  Future<int> updateBreakdown(Breakdown breakdown) async {
    final updatedBreakdown = breakdown.copyWith(updatedAt: DateTime.now());
    return await _databaseHelper.update(
      tableBreakdowns,
      updatedBreakdown.toMap(),
    );
  }

  // إنهاء عطل
  Future<int> finishBreakdown(
    String breakdownId, {
    String? fixedBy,
    String? fixDescription,
    double? repairCost,
    List<String>? spareParts,
  }) async {
    final endTime = DateTime.now();
    final db = await _databaseHelper.database;

    // الحصول على العطل الحالي لحساب ساعات التوقف
    final breakdown = await getBreakdownById(breakdownId);
    if (breakdown == null) return 0;

    final downtimeHours =
        endTime.difference(breakdown.startTime).inMinutes / 60.0;

    return await db.update(
      tableBreakdowns,
      {
        'endTime': endTime.toIso8601String(),
        'fixedBy': fixedBy,
        'fixDescription': fixDescription,
        'repairCost': repairCost,
        'downtimeHours': downtimeHours,
        'spareParts': spareParts?.join(','),
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [breakdownId],
    );
  }

  // الحصول على عطل بالمعرف
  Future<Breakdown?> getBreakdownById(String id) async {
    final map = await _databaseHelper.queryRow(tableBreakdowns, id);
    if (map == null) return null;
    return Breakdown.fromMap(map);
  }

  // الحصول على جميع الأعطال
  Future<List<Breakdown>> getAllBreakdowns() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableBreakdowns ORDER BY startTime DESC',
    );
    return maps.map((map) => Breakdown.fromMap(map)).toList();
  }

  // الحصول على الأعطال النشطة (غير المنتهية)
  Future<List<Breakdown>> getActiveBreakdowns() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableBreakdowns WHERE endTime IS NULL ORDER BY startTime DESC',
    );
    return maps.map((map) => Breakdown.fromMap(map)).toList();
  }

  // الحصول على أعطال ماكينة معينة
  Future<List<Breakdown>> getBreakdownsByMachine(String machineId) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableBreakdowns WHERE machineId = ? ORDER BY startTime DESC',
      [machineId],
    );
    return maps.map((map) => Breakdown.fromMap(map)).toList();
  }

  // الحصول على أعطال فترة زمنية معينة
  Future<List<Breakdown>> getBreakdownsByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableBreakdowns WHERE startTime >= ? AND startTime <= ? ORDER BY startTime DESC',
      [startDate.toIso8601String(), endDate.toIso8601String()],
    );
    return maps.map((map) => Breakdown.fromMap(map)).toList();
  }

  // === طرق إدارة الصيانة الوقائية ===

  // إضافة صيانة وقائية جديدة
  Future<String> addMaintenance(Maintenance maintenance) async {
    final maintenanceWithId = maintenance.copyWith(
      id: _uuid.v4(),
      createdAt: DateTime.now(),
    );

    await _databaseHelper.insert(tableMaintenance, maintenanceWithId.toMap());
    return maintenanceWithId.id!;
  }

  // تحديث صيانة
  Future<int> updateMaintenance(Maintenance maintenance) async {
    final updatedMaintenance = maintenance.copyWith(updatedAt: DateTime.now());
    return await _databaseHelper.update(
      tableMaintenance,
      updatedMaintenance.toMap(),
    );
  }

  // حذف صيانة
  Future<int> deleteMaintenance(String id) async {
    return await _databaseHelper.delete(tableMaintenance, id);
  }

  // الحصول على صيانة بالمعرف
  Future<Maintenance?> getMaintenanceById(String id) async {
    final map = await _databaseHelper.queryRow(tableMaintenance, id);
    if (map == null) return null;
    return Maintenance.fromMap(map);
  }

  // الحصول على جميع الصيانات
  Future<List<Maintenance>> getAllMaintenance() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableMaintenance WHERE isActive = 1 ORDER BY nextDue ASC',
    );
    return maps.map((map) => Maintenance.fromMap(map)).toList();
  }

  // الحصول على الصيانات المستحقة
  Future<List<Maintenance>> getDueMaintenance() async {
    final now = DateTime.now().toIso8601String();
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableMaintenance WHERE isActive = 1 AND nextDue <= ? AND status != ? ORDER BY nextDue ASC',
      [now, MaintenanceStatus.completed.index],
    );
    return maps.map((map) => Maintenance.fromMap(map)).toList();
  }

  // الحصول على صيانات ماكينة معينة
  Future<List<Maintenance>> getMaintenanceByMachine(String machineId) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableMaintenance WHERE machineId = ? AND isActive = 1 ORDER BY nextDue ASC',
      [machineId],
    );
    return maps.map((map) => Maintenance.fromMap(map)).toList();
  }

  // === طرق إدارة سجلات الصيانة ===

  // إضافة سجل صيانة منجزة
  Future<String> addMaintenanceRecord(MaintenanceRecord record) async {
    final recordWithId = record.copyWith(
      id: _uuid.v4(),
      createdAt: DateTime.now(),
    );

    await _databaseHelper.insert(tableMaintenanceRecords, recordWithId.toMap());
    return recordWithId.id!;
  }

  // الحصول على سجلات الصيانة
  Future<List<MaintenanceRecord>> getMaintenanceRecords() async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableMaintenanceRecords ORDER BY startTime DESC',
    );
    return maps.map((map) => MaintenanceRecord.fromMap(map)).toList();
  }

  // الحصول على سجلات صيانة ماكينة معينة
  Future<List<MaintenanceRecord>> getMaintenanceRecordsByMachine(
    String machineId,
  ) async {
    final maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableMaintenanceRecords WHERE machineId = ? ORDER BY startTime DESC',
      [machineId],
    );
    return maps.map((map) => MaintenanceRecord.fromMap(map)).toList();
  }

  // === طرق التحليل والإحصائيات ===

  // تحليل الأعطال المتكررة
  Future<Map<String, dynamic>> getBreakdownAnalysis() async {
    final db = await _databaseHelper.database;

    // الأعطال حسب النوع
    final typeAnalysis = await db.rawQuery('''
      SELECT type, COUNT(*) as count, AVG(downtimeHours) as avgDowntime
      FROM $tableBreakdowns
      WHERE endTime IS NOT NULL
      GROUP BY type
      ORDER BY count DESC
    ''');

    // الأعطال حسب السبب
    final causeAnalysis = await db.rawQuery('''
      SELECT cause, COUNT(*) as count, AVG(downtimeHours) as avgDowntime
      FROM $tableBreakdowns
      WHERE endTime IS NOT NULL
      GROUP BY cause
      ORDER BY count DESC
    ''');

    // الأعطال حسب الماكينة
    final machineAnalysis = await db.rawQuery('''
      SELECT machineId, COUNT(*) as count,
             AVG(downtimeHours) as avgDowntime,
             SUM(downtimeHours) as totalDowntime
      FROM $tableBreakdowns
      WHERE endTime IS NOT NULL
      GROUP BY machineId
      ORDER BY count DESC
    ''');

    // إحصائيات عامة
    final generalStats = await db.rawQuery('''
      SELECT
        COUNT(*) as totalBreakdowns,
        AVG(downtimeHours) as avgDowntime,
        SUM(downtimeHours) as totalDowntime,
        AVG(repairCost) as avgRepairCost,
        SUM(repairCost) as totalRepairCost
      FROM $tableBreakdowns
      WHERE endTime IS NOT NULL
    ''');

    return {
      'typeAnalysis': typeAnalysis,
      'causeAnalysis': causeAnalysis,
      'machineAnalysis': machineAnalysis,
      'generalStats': generalStats.isNotEmpty ? generalStats.first : {},
    };
  }

  // إحصائيات الصيانة
  Future<Map<String, dynamic>> getMaintenanceStats() async {
    final db = await _databaseHelper.database;

    // عدد الصيانات المستحقة
    final dueMaintenance = await getDueMaintenance();

    // الصيانات حسب الحالة
    final statusStats = await db.rawQuery('''
      SELECT status, COUNT(*) as count
      FROM $tableMaintenance
      WHERE isActive = 1
      GROUP BY status
    ''');

    // الصيانات حسب الأولوية
    final priorityStats = await db.rawQuery('''
      SELECT priority, COUNT(*) as count
      FROM $tableMaintenance
      WHERE isActive = 1
      GROUP BY priority
    ''');

    // إحصائيات سجلات الصيانة
    final recordStats = await db.rawQuery('''
      SELECT
        COUNT(*) as totalRecords,
        AVG(actualCost) as avgCost,
        SUM(actualCost) as totalCost,
        AVG((julianday(endTime) - julianday(startTime)) * 24) as avgDuration
      FROM $tableMaintenanceRecords
    ''');

    return {
      'dueCount': dueMaintenance.length,
      'statusStats': statusStats,
      'priorityStats': priorityStats,
      'recordStats': recordStats.isNotEmpty ? recordStats.first : {},
    };
  }

  // حساب ساعات التشغيل للماكينة (من بيانات الإنتاج)
  Future<double> getMachineOperatingHours(
    String machineId, {
    int days = 30,
  }) async {
    final db = await _databaseHelper.database;
    final startDate = DateTime.now().subtract(Duration(days: days));

    final result = await db.rawQuery(
      '''
      SELECT SUM(cycleTime * partsProduced / 3600) as totalHours
      FROM production
      WHERE machineId = ? AND date >= ?
    ''',
      [machineId, startDate.toIso8601String()],
    );

    if (result.isNotEmpty && result.first['totalHours'] != null) {
      return (result.first['totalHours'] as num).toDouble();
    }
    return 0.0;
  }

  // حساب عدد الدورات للماكينة
  Future<int> getMachineCycles(String machineId, {int days = 30}) async {
    final db = await _databaseHelper.database;
    final startDate = DateTime.now().subtract(Duration(days: days));

    final result = await db.rawQuery(
      '''
      SELECT SUM(partsProduced) as totalParts
      FROM production
      WHERE machineId = ? AND date >= ?
    ''',
      [machineId, startDate.toIso8601String()],
    );

    if (result.isNotEmpty && result.first['totalParts'] != null) {
      return (result.first['totalParts'] as num).toInt();
    }
    return 0;
  }

  // تحديث موعد الصيانة القادمة
  Future<int> updateNextMaintenanceDue(String maintenanceId) async {
    final maintenance = await getMaintenanceById(maintenanceId);
    if (maintenance == null) return 0;

    DateTime? nextDue;
    final now = DateTime.now();

    switch (maintenance.frequency) {
      case MaintenanceFrequency.hours:
        // بناءً على ساعات التشغيل - تقدير بسيط: إضافة الفترة بالأيام
        nextDue = now.add(
          Duration(days: (maintenance.intervalValue / 8).round()),
        );
        break;
      case MaintenanceFrequency.cycles:
        // بناءً على عدد الدورات - تقدير مشابه
        nextDue = now.add(
          Duration(days: (maintenance.intervalValue / 1000).round()),
        );
        break;
      case MaintenanceFrequency.days:
        nextDue = now.add(Duration(days: maintenance.intervalValue));
        break;
      case MaintenanceFrequency.weeks:
        nextDue = now.add(Duration(days: maintenance.intervalValue * 7));
        break;
      case MaintenanceFrequency.months:
        nextDue = DateTime(
          now.year,
          now.month + maintenance.intervalValue,
          now.day,
        );
        break;
    }

    final db = await _databaseHelper.database;
    return await db.update(
      tableMaintenance,
      {
        'lastPerformed': now.toIso8601String(),
        'nextDue': nextDue.toIso8601String(),
        'updatedAt': now.toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [maintenanceId],
    );
  }
}
