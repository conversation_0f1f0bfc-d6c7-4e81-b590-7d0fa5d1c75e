# دليل حل مشكلة بناء APK لنظام إدارة المصنع

## المشكلة

عند محاولة بناء ملف APK للتطبيق، تظهر المشكلة التالية:

```
FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':printing:verifyReleaseResources'.
> A failure occurred while executing com.android.build.gradle.tasks.VerifyLibraryResourcesTask$Action
   > Android resource linking failed
     ERROR: D:\flutter apps\mostafa_final\New folder\build\printing\intermediates\merged_res\release\mergeReleaseResources\values\values.xml:194: AAPT: error: resource android:attr/lStar not found.
```

هذه المشكلة ناتجة عن عدم توافق بين مكتبة `printing` وإصدار Android SDK المستخدم.

## الحلول المقترحة

### الحل 1: إنشاء مشروع جديد بدون مكتبة printing

هذا هو الحل الأكثر فعالية وموثوقية:

1. قم بإنشاء مشروع Flutter جديد:

```bash
flutter create factory_management_system
```

2. قم بنسخ الملفات التالية من المشروع القديم إلى المشروع الجديد:
   - جميع الملفات في مجلد `lib` (باستثناء الكود المرتبط بمكتبة printing)
   - ملف `pubspec.yaml` (مع تعديل اسم المشروع وإزالة مكتبة printing)
   - مجلد `assets` إذا كان موجودًا

3. قم بتعديل الكود لاستخدام مكتبات بديلة لإنشاء وعرض ملفات PDF:
   - استخدم مكتبة `pdf` فقط لإنشاء ملفات PDF
   - استخدم مكتبة `path_provider` لحفظ ملفات PDF
   - استخدم مكتبة `share_plus` لمشاركة ملفات PDF
   - استخدم مكتبة `open_file` لفتح ملفات PDF

4. قم بتنفيذ:

```bash
flutter pub get
flutter build apk
```

### الحل 2: استخدام إصدار أقدم من Flutter

1. قم بتثبيت إصدار أقدم من Flutter:

```bash
flutter version 3.16.9
```

2. قم بتنفيذ:

```bash
flutter clean
flutter pub get
flutter build apk
```

### الحل 3: تعديل ملف build.gradle لتجاهل مكتبة printing في وضع الإنتاج

1. قم بإنشاء ملف `android/app/src/main/java/io/flutter/app/FlutterMultiDexApplication.java`:

```java
package io.flutter.app;

import androidx.multidex.MultiDexApplication;

public class FlutterMultiDexApplication extends MultiDexApplication {
}
```

2. قم بتعديل ملف `android/app/src/main/AndroidManifest.xml` لاستخدام `FlutterMultiDexApplication`:

```xml
<application
    android:name=".FlutterMultiDexApplication"
    ...
>
```

3. قم بتعديل ملف `android/app/build.gradle` لإضافة دعم MultiDex:

```gradle
dependencies {
    implementation "androidx.multidex:multidex:2.0.1"
}

android {
    defaultConfig {
        multiDexEnabled true
    }
}
```

4. قم بتنفيذ:

```bash
flutter clean
flutter pub get
flutter build apk --no-shrink
```

## الخلاصة

المشكلة الرئيسية هي عدم توافق بين مكتبة `printing` وإصدار Android SDK. الحل الأفضل هو إنشاء مشروع جديد بدون مكتبة printing واستخدام مكتبات بديلة لإنشاء وعرض ملفات PDF.

إذا كنت بحاجة إلى استخدام مكتبة printing، فيمكنك تجربة استخدام إصدار أقدم من Flutter أو تعديل ملف build.gradle لتجاهل مكتبة printing في وضع الإنتاج.
