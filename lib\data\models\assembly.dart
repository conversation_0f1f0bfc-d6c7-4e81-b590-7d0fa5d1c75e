import 'package:intl/intl.dart';

class Assembly {
  final String? id;
  final String productId; // معرف المنتج المجمع
  final DateTime date; // تاريخ التجميع
  final String workerId; // معرف العامل المسؤول عن التجميع
  final int quantity; // عدد الوحدات المجمعة
  final double laborCostPerMinute; // تكلفة العمالة للدقيقة الواحدة
  final double totalAssemblyCost; // إجمالي تكلفة التجميع
  final double totalPackagingCost; // إجمالي تكلفة التغليف
  final String status; // حالة التجميع (مكتمل، معيب، إلخ)
  final List<AssemblyComponentUsage> componentsUsed; // المكونات المستخدمة

  Assembly({
    this.id,
    required this.productId,
    required this.date,
    required this.workerId,
    required this.quantity,
    required this.laborCostPerMinute,
    required this.totalAssemblyCost,
    required this.totalPackagingCost,
    required this.status,
    required this.componentsUsed,
  });

  // تحويل Assembly إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'productId': productId,
      'date': DateFormat('yyyy-MM-dd').format(date),
      'workerId': workerId,
      'quantity': quantity,
      'laborCostPerMinute': laborCostPerMinute,
      'totalAssemblyCost': totalAssemblyCost,
      'totalPackagingCost': totalPackagingCost,
      'status': status,
    };
  }

  // إنشاء Assembly من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Assembly.fromMap(
    Map<String, dynamic> map,
    List<AssemblyComponentUsage> componentsUsed,
  ) {
    return Assembly(
      id: map['id'],
      productId: map['productId'],
      date: DateFormat('yyyy-MM-dd').parse(map['date']),
      workerId: map['workerId'],
      quantity: map['quantity'],
      laborCostPerMinute: map['laborCostPerMinute'],
      totalAssemblyCost: map['totalAssemblyCost'],
      totalPackagingCost: map['totalPackagingCost'],
      status: map['status'],
      componentsUsed: componentsUsed,
    );
  }
}

class AssemblyComponentUsage {
  final String? id;
  final String? assemblyId; // معرف عملية التجميع
  final String componentId; // معرف المكون المستخدم
  final String name; // اسم المكون
  final String partId; // معرف الجزء (من المخزون)
  final int componentType; // نوع المكون (0: جزء بلاستيكي، 1: إكسسوار)
  final int quantity; // الكمية المستخدمة
  final double cost; // تكلفة المكون الإجمالية

  AssemblyComponentUsage({
    this.id,
    this.assemblyId,
    required this.componentId,
    required this.name,
    required this.partId,
    required this.componentType,
    required this.quantity,
    required this.cost,
  });

  // تحويل AssemblyComponentUsage إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'assemblyId': assemblyId,
      'componentId': componentId,
      'name': name,
      'partId': partId,
      'componentType': componentType,
      'quantity': quantity,
      'cost': cost,
    };
  }

  // إنشاء AssemblyComponentUsage من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory AssemblyComponentUsage.fromMap(Map<String, dynamic> map) {
    return AssemblyComponentUsage(
      id: map['id'],
      assemblyId: map['assemblyId'],
      componentId: map['componentId'],
      name: map['name'],
      partId: map['partId'],
      componentType: map['componentType'],
      quantity: map['quantity'],
      cost: map['cost'],
    );
  }
}

// حالات عملية التجميع
class AssemblyStatus {
  static const String completed = 'مكتمل';
  static const String defective = 'معيب';
  static const String inProgress = 'قيد التنفيذ';
  static const String canceled = 'ملغي';
}
