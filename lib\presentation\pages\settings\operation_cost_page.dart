import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/operation_cost.dart';
import 'package:mostafa_final/presentation/controllers/operation_cost_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class OperationCostPage extends StatelessWidget {
  const OperationCostPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(OperationCostController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة تكاليف التشغيل'),
        centerTitle: true,
      ),
      drawer: const CustomDrawer(),
      floatingActionButton: FloatingActionButton(
        onPressed: controller.showAddOperationCostDialog,
        child: const Icon(Icons.add),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: 'جاري تحميل البيانات...');
        }

        return Column(
          children: [
            _buildCategoryTabs(controller),
            Expanded(
              child: _buildCostsList(controller),
            ),
          ],
        );
      }),
    );
  }

  Widget _buildCategoryTabs(OperationCostController controller) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Obx(
        () => SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            children: [
              _buildCategoryTab(
                controller,
                CostCategory.electricity,
                Icons.electric_bolt,
                Colors.blue,
              ),
              _buildCategoryTab(
                controller,
                CostCategory.labor,
                Icons.people,
                Colors.green,
              ),
              _buildCategoryTab(
                controller,
                CostCategory.maintenance,
                Icons.build,
                Colors.orange,
              ),
              _buildCategoryTab(
                controller,
                CostCategory.overhead,
                Icons.account_balance,
                Colors.purple,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCategoryTab(
    OperationCostController controller,
    String category,
    IconData icon,
    Color color,
  ) {
    final isSelected = controller.selectedCategory.value == category;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: ElevatedButton.icon(
        onPressed: () => controller.changeCategory(category),
        icon: Icon(
          icon,
          color: isSelected ? Colors.white : color,
        ),
        label: Text(category),
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? color : Colors.white,
          foregroundColor: isSelected ? Colors.white : color,
          side: BorderSide(color: color),
        ),
      ),
    );
  }

  Widget _buildCostsList(OperationCostController controller) {
    return Obx(() {
      final List<OperationCost> costs;

      switch (controller.selectedCategory.value) {
        case CostCategory.electricity:
          costs = controller.electricityCosts;
          break;
        case CostCategory.labor:
          costs = controller.laborCosts;
          break;
        case CostCategory.maintenance:
          costs = controller.maintenanceCosts;
          break;
        case CostCategory.overhead:
          costs = controller.overheadCosts;
          break;
        default:
          costs = [];
      }

      if (costs.isEmpty) {
        return Center(
          child: Text(
            'لا توجد تكاليف في فئة ${controller.selectedCategory.value}',
            style: const TextStyle(fontSize: 16),
          ),
        );
      }

      return ListView.builder(
        itemCount: costs.length,
        itemBuilder: (context, index) {
          final cost = costs[index];
          return _buildCostItem(controller, cost);
        },
      );
    });
  }

  Widget _buildCostItem(OperationCostController controller, OperationCost cost) {
    final dateFormat = DateFormat('yyyy-MM-dd');
    final formattedDate = dateFormat.format(cost.lastUpdate);

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        title: Text(cost.name),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('القيمة: ${cost.value} ${cost.unit}'),
            Text('آخر تحديث: $formattedDate'),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (cost.isDefault)
              const Padding(
                padding: EdgeInsets.only(left: 8.0),
                child: Chip(
                  label: Text('افتراضي'),
                  backgroundColor: Colors.green,
                  labelStyle: TextStyle(color: Colors.white),
                ),
              ),
            IconButton(
              icon: const Icon(Icons.edit, color: Colors.blue),
              onPressed: () => controller.showUpdateOperationCostDialog(cost),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: Colors.red),
              onPressed: () => _showDeleteConfirmation(controller, cost),
            ),
          ],
        ),
        onTap: () => controller.showUpdateOperationCostDialog(cost),
      ),
    );
  }

  void _showDeleteConfirmation(
    OperationCostController controller,
    OperationCost cost,
  ) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف ${cost.name}؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              Get.back();
              controller.deleteOperationCost(cost.id!);
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
