import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// شريط بحث متجاوب مع حجم الشاشة
class ResponsiveSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onChanged;
  final VoidCallback? onClear;
  final Widget? leading;
  final List<Widget>? actions;
  final bool autoFocus;
  final bool showBorder;

  const ResponsiveSearchBar({
    super.key,
    required this.controller,
    required this.hintText,
    required this.onChanged,
    this.onClear,
    this.leading,
    this.actions,
    this.autoFocus = false,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);
    
    return Container(
      height: ScreenSize.isSmallScreen ? 40 : 50,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        boxShadow: showBorder ? null : EnhancedAppTheme.cardShadow,
        border: showBorder
            ? Border.all(color: EnhancedAppTheme.textLight.withAlpha(128))
            : null,
      ),
      child: Row(
        children: [
          if (leading != null)
            Padding(
              padding: EdgeInsets.only(right: ScreenSize.getPadding(8)),
              child: leading,
            )
          else
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: ScreenSize.getPadding(12),
              ),
              child: Icon(
                Icons.search,
                color: EnhancedAppTheme.textSecondary,
                size: ScreenSize.isSmallScreen ? 18 : 24,
              ),
            ),
          Expanded(
            child: TextField(
              controller: controller,
              autofocus: autoFocus,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: TextStyle(
                  color: EnhancedAppTheme.textLight,
                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                ),
                border: InputBorder.none,
                contentPadding: EdgeInsets.symmetric(
                  vertical: ScreenSize.isSmallScreen ? 10 : 15,
                ),
              ),
              style: TextStyle(
                color: EnhancedAppTheme.textPrimary,
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
              ),
              onChanged: onChanged,
            ),
          ),
          if (controller.text.isNotEmpty)
            IconButton(
              icon: Icon(
                Icons.close,
                color: EnhancedAppTheme.textSecondary,
                size: ScreenSize.isSmallScreen ? 16 : 20,
              ),
              onPressed: () {
                controller.clear();
                onChanged('');
                if (onClear != null) {
                  onClear!();
                }
              },
              padding: EdgeInsets.all(ScreenSize.getPadding(4)),
              constraints: BoxConstraints(
                minWidth: ScreenSize.isSmallScreen ? 32 : 40,
                minHeight: ScreenSize.isSmallScreen ? 32 : 40,
              ),
            ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
}

/// رقاقة تصفية متجاوبة مع حجم الشاشة
class ResponsiveFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;

  const ResponsiveFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);
    
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: ScreenSize.getPadding(12),
          vertical: ScreenSize.getPadding(6),
        ),
        margin: EdgeInsets.only(left: ScreenSize.getPadding(8)),
        decoration: BoxDecoration(
          color: isSelected
              ? EnhancedAppTheme.primaryColor
              : EnhancedAppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected
                ? EnhancedAppTheme.primaryColor
                : EnhancedAppTheme.textLight,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null)
              Icon(
                icon,
                size: ScreenSize.isSmallScreen ? 12 : 16,
                color: isSelected
                    ? EnhancedAppTheme.textOnPrimary
                    : EnhancedAppTheme.textSecondary,
              ),
            if (icon != null) SizedBox(width: ScreenSize.getPadding(4)),
            TextUtils.responsiveText(
              label,
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                color: isSelected
                    ? EnhancedAppTheme.textOnPrimary
                    : EnhancedAppTheme.textSecondary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }
}
