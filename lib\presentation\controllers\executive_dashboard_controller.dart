import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:mostafa_final/data/repositories/executive_dashboard_repository.dart';
import 'package:mostafa_final/services/executive_analytics_service.dart';
import 'package:mostafa_final/services/export_service.dart';
import 'package:mostafa_final/services/notification_service.dart';

class ExecutiveDashboardController extends GetxController {
  final ExecutiveDashboardRepository _dashboardRepository =
      ExecutiveDashboardRepository();
  final ExecutiveAnalyticsService _analyticsService =
      ExecutiveAnalyticsService();
  final ExportService _exportService = ExportService();
  late final NotificationService _notificationService;

  // حالة التحميل
  final isLoading = false.obs;
  final isRefreshing = false.obs;
  final isGeneratingReport = false.obs;

  // البيانات الأساسية
  final kpis = <KPI>[].obs;
  final reports = <ExecutiveReport>[].obs;
  final performanceSummary = Rxn<PerformanceSummary>();
  final chartData = <String, List<ChartData>>{}.obs;

  // الفلاتر والإعدادات
  final selectedPeriod = ReportPeriod.monthly.obs;
  final selectedReportType = ReportType.comprehensive.obs;
  final selectedDateRange =
      DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 30)),
        end: DateTime.now(),
      ).obs;

  // إعدادات لوحة التحكم
  final dashboardSettings = <String, String>{}.obs;
  final selectedKPITypes = <KPIType>[].obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    loadInitialData();
  }

  /// تهيئة الخدمات
  void _initializeServices() {
    // تهيئة خدمة التنبيهات
    if (!Get.isRegistered<NotificationService>()) {
      Get.put(NotificationService());
    }
    _notificationService = NotificationService.instance;
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadKPIs(),
        loadReports(),
        loadPerformanceSummary(),
        loadChartData(),
        loadDashboardSettings(),
      ]);
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحديث البيانات
  Future<void> refreshData() async {
    isRefreshing.value = true;
    try {
      // إعادة حساب المؤشرات
      await calculateAndSaveKPIs();

      // تحديث ملخص الأداء
      await loadPerformanceSummary();

      // تحديث بيانات الرسوم البيانية
      await loadChartData();

      // فحص التنبيهات
      await _notificationService.checkKPIsForNotifications(kpis);

      Get.snackbar('نجح', 'تم تحديث البيانات بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث البيانات: $e');
    } finally {
      isRefreshing.value = false;
    }
  }

  /// تحميل المؤشرات الرئيسية
  Future<void> loadKPIs() async {
    try {
      final allKPIs = await _dashboardRepository.getAllKPIs();
      kpis.value = allKPIs;
    } catch (e) {
      debugPrint('خطأ في تحميل المؤشرات: $e');
    }
  }

  /// حساب وحفظ المؤشرات الجديدة
  Future<void> calculateAndSaveKPIs() async {
    try {
      final calculatedKPIs = await _analyticsService.calculateKPIs(
        days: selectedDateRange.value.duration.inDays,
      );

      // حفظ المؤشرات في قاعدة البيانات
      for (var kpi in calculatedKPIs) {
        await _dashboardRepository.saveKPI(kpi);
      }

      // تحديث القائمة المحلية
      kpis.value = calculatedKPIs;
    } catch (e) {
      debugPrint('خطأ في حساب المؤشرات: $e');
    }
  }

  /// تحميل التقارير
  Future<void> loadReports() async {
    try {
      final allReports = await _dashboardRepository.getAllReports();
      reports.value = allReports;
    } catch (e) {
      debugPrint('خطأ في تحميل التقارير: $e');
    }
  }

  /// تحميل ملخص الأداء
  Future<void> loadPerformanceSummary() async {
    try {
      final summary = await _analyticsService.generatePerformanceSummary(
        days: selectedDateRange.value.duration.inDays,
      );
      performanceSummary.value = summary;
    } catch (e) {
      debugPrint('خطأ في تحميل ملخص الأداء: $e');
    }
  }

  /// تحميل بيانات الرسوم البيانية
  Future<void> loadChartData() async {
    try {
      final days = selectedDateRange.value.duration.inDays;

      final productionTrend = await _analyticsService.generateChartData(
        'production_trend',
        days: days,
      );
      final machineEfficiency = await _analyticsService.generateChartData(
        'machine_efficiency',
        days: days,
      );
      final maintenanceCosts = await _analyticsService.generateChartData(
        'maintenance_costs',
        days: days,
      );
      final salesRevenue = await _analyticsService.generateChartData(
        'sales_revenue',
        days: days,
      );

      chartData.value = {
        'production_trend': productionTrend,
        'machine_efficiency': machineEfficiency,
        'maintenance_costs': maintenanceCosts,
        'sales_revenue': salesRevenue,
      };
    } catch (e) {
      debugPrint('خطأ في تحميل بيانات الرسوم البيانية: $e');
    }
  }

  /// تحميل إعدادات لوحة التحكم
  Future<void> loadDashboardSettings() async {
    try {
      final settings = await _dashboardRepository.getAllUserSettings(
        'default_user',
      );
      dashboardSettings.value = settings;

      // تطبيق الإعدادات المحفوظة
      _applySettings();
    } catch (e) {
      debugPrint('خطأ في تحميل إعدادات لوحة التحكم: $e');
    }
  }

  /// تطبيق الإعدادات المحفوظة
  void _applySettings() {
    // تطبيق فترة التقرير المحفوظة
    final savedPeriod = dashboardSettings['report_period'];
    if (savedPeriod != null) {
      final periodIndex = int.tryParse(savedPeriod);
      if (periodIndex != null && periodIndex < ReportPeriod.values.length) {
        selectedPeriod.value = ReportPeriod.values[periodIndex];
      }
    }

    // تطبيق نوع التقرير المحفوظ
    final savedReportType = dashboardSettings['report_type'];
    if (savedReportType != null) {
      final typeIndex = int.tryParse(savedReportType);
      if (typeIndex != null && typeIndex < ReportType.values.length) {
        selectedReportType.value = ReportType.values[typeIndex];
      }
    }
  }

  /// إنشاء تقرير تنفيذي جديد
  Future<void> generateExecutiveReport() async {
    isGeneratingReport.value = true;
    try {
      final startDate = selectedDateRange.value.start;
      final endDate = selectedDateRange.value.end;

      // جمع البيانات للتقرير
      final reportData = await _gatherReportData(startDate, endDate);

      // إنشاء التقرير
      final report = ExecutiveReport(
        id: '',
        title: 'تقرير تنفيذي ${selectedReportType.value.displayName}',
        description:
            'تقرير شامل عن أداء المصنع للفترة من ${_formatDate(startDate)} إلى ${_formatDate(endDate)}',
        type: selectedReportType.value,
        period: selectedPeriod.value,
        startDate: startDate,
        endDate: endDate,
        data: reportData,
        insights: _generateInsights(reportData),
        recommendations: _generateRecommendations(reportData),
        generatedAt: DateTime.now(),
        generatedBy: 'النظام التلقائي',
      );

      // حفظ التقرير
      await _dashboardRepository.saveExecutiveReport(report);

      // تحديث قائمة التقارير
      await loadReports();

      Get.snackbar('نجح', 'تم إنشاء التقرير التنفيذي بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إنشاء التقرير: $e');
    } finally {
      isGeneratingReport.value = false;
    }
  }

  /// جمع بيانات التقرير
  Future<Map<String, dynamic>> _gatherReportData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final data = <String, dynamic>{};

    try {
      // بيانات الأداء العام
      final summary = await _analyticsService.generatePerformanceSummary(
        days: endDate.difference(startDate).inDays,
      );
      data['performance_summary'] = summary.toMap();

      // المؤشرات الرئيسية
      final currentKPIs = await _analyticsService.calculateKPIs(
        days: endDate.difference(startDate).inDays,
      );
      data['kpis'] = currentKPIs.map((kpi) => kpi.toMap()).toList();

      // بيانات الرسوم البيانية
      data['charts'] = Map<String, dynamic>.from(chartData);

      // إحصائيات إضافية
      data['period_days'] = endDate.difference(startDate).inDays;
      data['generated_at'] = DateTime.now().toIso8601String();
    } catch (e) {
      debugPrint('خطأ في جمع بيانات التقرير: $e');
    }

    return data;
  }

  /// إنشاء الرؤى والتحليلات
  List<String> _generateInsights(Map<String, dynamic> data) {
    final insights = <String>[];

    try {
      final summary = data['performance_summary'] as Map<String, dynamic>?;
      if (summary != null) {
        final overallScore = summary['overallScore'] as double;

        if (overallScore >= 80) {
          insights.add(
            'الأداء العام للمصنع ممتاز بنسبة ${overallScore.toStringAsFixed(1)}%',
          );
        } else if (overallScore >= 60) {
          insights.add(
            'الأداء العام للمصنع جيد بنسبة ${overallScore.toStringAsFixed(1)}% مع إمكانية للتحسين',
          );
        } else {
          insights.add(
            'الأداء العام للمصنع يحتاج تحسين كبير - النسبة الحالية ${overallScore.toStringAsFixed(1)}%',
          );
        }

        // تحليل الإنتاج
        final production = summary['production'] as Map<String, dynamic>?;
        if (production != null) {
          final efficiency = production['efficiency'] as double;
          if (efficiency >= 85) {
            insights.add(
              'كفاءة الإنتاج عالية جداً (${efficiency.toStringAsFixed(1)}%)',
            );
          } else if (efficiency < 60) {
            insights.add(
              'كفاءة الإنتاج منخفضة وتحتاج تدخل فوري (${efficiency.toStringAsFixed(1)}%)',
            );
          }
        }

        // تحليل الصيانة
        final maintenance = summary['maintenance'] as Map<String, dynamic>?;
        if (maintenance != null) {
          final availability = maintenance['availability'] as double;
          if (availability >= 95) {
            insights.add(
              'معدل توفر الماكينات ممتاز (${availability.toStringAsFixed(1)}%)',
            );
          } else if (availability < 85) {
            insights.add(
              'معدل توفر الماكينات منخفض ويؤثر على الإنتاج (${availability.toStringAsFixed(1)}%)',
            );
          }
        }
      }
    } catch (e) {
      insights.add('حدث خطأ في تحليل البيانات');
    }

    return insights;
  }

  /// إنشاء التوصيات
  List<String> _generateRecommendations(Map<String, dynamic> data) {
    final recommendations = <String>[];

    try {
      final summary = data['performance_summary'] as Map<String, dynamic>?;
      if (summary != null) {
        final production = summary['production'] as Map<String, dynamic>?;
        final maintenance = summary['maintenance'] as Map<String, dynamic>?;
        final financial = summary['financial'] as Map<String, dynamic>?;

        // توصيات الإنتاج
        if (production != null) {
          final efficiency = production['efficiency'] as double;
          if (efficiency < 70) {
            recommendations.add(
              'تحسين كفاءة الإنتاج من خلال تدريب المشغلين وصيانة الماكينات',
            );
          }

          final capacityUtilization =
              production['capacityUtilization'] as double;
          if (capacityUtilization < 75) {
            recommendations.add(
              'زيادة استغلال الطاقة الإنتاجية من خلال تحسين الجدولة',
            );
          }
        }

        // توصيات الصيانة
        if (maintenance != null) {
          final mtbf = maintenance['mtbf'] as double;
          if (mtbf < 100) {
            recommendations.add(
              'تطبيق برنامج صيانة وقائية أكثر فعالية لتقليل الأعطال',
            );
          }

          final mttr = maintenance['mttr'] as double;
          if (mttr > 6) {
            recommendations.add(
              'تحسين سرعة الاستجابة للأعطال وتوفير قطع الغيار',
            );
          }
        }

        // توصيات مالية
        if (financial != null) {
          final profitMargin = financial['profitMargin'] as double;
          if (profitMargin < 20) {
            recommendations.add('مراجعة هيكل التكاليف وتحسين هامش الربح');
          }
        }
      }
    } catch (e) {
      recommendations.add('مراجعة شاملة لجميع العمليات لتحسين الأداء');
    }

    return recommendations;
  }

  /// تغيير فترة التقرير
  void changePeriod(ReportPeriod period) {
    selectedPeriod.value = period;
    _updateDateRangeForPeriod(period);
    _saveSetting('report_period', period.index.toString());
    refreshData();
  }

  /// تحديث نطاق التاريخ بناءً على الفترة
  void _updateDateRangeForPeriod(ReportPeriod period) {
    final now = DateTime.now();
    DateTime startDate;

    switch (period) {
      case ReportPeriod.daily:
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case ReportPeriod.weekly:
        startDate = now.subtract(const Duration(days: 7));
        break;
      case ReportPeriod.monthly:
        startDate = DateTime(now.year, now.month - 1, now.day);
        break;
      case ReportPeriod.quarterly:
        startDate = DateTime(now.year, now.month - 3, now.day);
        break;
      case ReportPeriod.yearly:
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      case ReportPeriod.custom:
        return; // لا تغيير للفترة المخصصة
    }

    selectedDateRange.value = DateTimeRange(start: startDate, end: now);
  }

  /// تغيير نوع التقرير
  void changeReportType(ReportType type) {
    selectedReportType.value = type;
    _saveSetting('report_type', type.index.toString());
  }

  /// تغيير نطاق التاريخ
  void changeDateRange(DateTimeRange dateRange) {
    selectedDateRange.value = dateRange;
    selectedPeriod.value = ReportPeriod.custom;
    refreshData();
  }

  /// حفظ إعداد
  Future<void> _saveSetting(String key, String value) async {
    try {
      await _dashboardRepository.saveDashboardSetting(
        'default_user',
        key,
        value,
      );
      dashboardSettings[key] = value;
    } catch (e) {
      debugPrint('خطأ في حفظ الإعداد: $e');
    }
  }

  /// تنسيق التاريخ
  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  /// الحصول على المؤشرات حسب النوع
  List<KPI> getKPIsByType(KPIType type) {
    return kpis.where((kpi) => kpi.type == type).toList();
  }

  /// الحصول على التقارير حسب النوع
  List<ExecutiveReport> getReportsByType(ReportType type) {
    return reports.where((report) => report.type == type).toList();
  }

  /// حذف تقرير
  Future<void> deleteReport(String reportId) async {
    try {
      await _dashboardRepository.deleteReport(reportId);
      await loadReports();
      Get.snackbar('نجح', 'تم حذف التقرير بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في حذف التقرير: $e');
    }
  }

  /// تصدير تقرير إلى PDF
  Future<void> exportReport(ExecutiveReport report) async {
    try {
      await _exportService.exportReportToPDF(
        report,
        kpis: kpis,
        summary: performanceSummary.value,
      );
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير التقرير: $e');
    }
  }

  /// تصدير البيانات إلى Excel
  Future<void> exportDataToExcel() async {
    try {
      await _exportService.exportDataToExcel(
        kpis: kpis,
        summary: performanceSummary.value,
        chartData: chartData,
      );
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير البيانات: $e');
    }
  }

  /// تصدير رسم بياني كصورة
  Future<void> exportChartAsImage(GlobalKey chartKey, String chartName) async {
    try {
      await _exportService.exportChartAsImage(chartKey, chartName);
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير الرسم البياني: $e');
    }
  }

  /// إحصائيات سريعة
  Map<String, dynamic> get quickStats {
    final summary = performanceSummary.value;
    if (summary == null) {
      return {
        'overallScore': 0.0,
        'totalKPIs': kpis.length,
        'totalReports': reports.length,
        'lastUpdate': 'غير متاح',
      };
    }

    return {
      'overallScore': summary.overallScore,
      'totalKPIs': kpis.length,
      'totalReports': reports.length,
      'lastUpdate': _formatDate(summary.calculatedAt),
      'productionEfficiency': summary.production.efficiency,
      'availability': summary.maintenance.availability,
      'profitMargin': summary.financial.profitMargin,
    };
  }
}
