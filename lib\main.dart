import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/di/dependency_injection.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/pages/splash/splash_page.dart';
import 'package:mostafa_final/presentation/pages/dashboard/dashboard_page.dart';
import 'package:mostafa_final/presentation/pages/raw_materials/raw_materials_page.dart';
import 'package:mostafa_final/presentation/pages/raw_materials/raw_material_report_page.dart';
import 'package:mostafa_final/presentation/pages/machines/machines_page.dart';
import 'package:mostafa_final/presentation/pages/molds/molds_page.dart';
import 'package:mostafa_final/presentation/pages/production/daily_production_page.dart';
import 'package:mostafa_final/presentation/pages/production/injection_cost_page.dart';
import 'package:mostafa_final/presentation/pages/production/production_priority_page.dart';
import 'package:mostafa_final/presentation/pages/production/assembly_page.dart';
import 'package:mostafa_final/presentation/pages/production/assembly_report_page.dart';
import 'package:mostafa_final/presentation/pages/production/cost_report_page.dart';
import 'package:mostafa_final/presentation/pages/inventory/inventory_page.dart';
import 'package:mostafa_final/presentation/pages/sales/sales_page.dart';
import 'package:mostafa_final/presentation/pages/sales/sales_report_page.dart';
import 'package:mostafa_final/presentation/pages/orders/orders_page.dart';
import 'package:mostafa_final/presentation/pages/backup_page.dart';
import 'package:mostafa_final/presentation/pages/performance_test_page.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

// استيراد Platform فقط عندما لا نكون في بيئة الويب
import 'dart:io' if (dart.library.js) 'mock_platform.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize FFI for sqflite on Windows
  if (!kIsWeb) {
    try {
      // التحقق من نظام التشغيل فقط إذا لم نكن في بيئة الويب
      final isDesktop = Platform.isWindows || Platform.isLinux;
      if (isDesktop) {
        // Initialize FFI
        sqfliteFfiInit();
        // Change the default factory
        databaseFactory = databaseFactoryFfi;
      }
    } catch (e) {
      debugPrint('Platform detection error: $e');
    }
  }

  // تهيئة حقن التبعيات
  DependencyInjection.init();

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        // تهيئة أحجام الشاشة
        ScreenSize.init(context);
        return GetMaterialApp(
          title: 'نظام إدارة المصنع',
          debugShowCheckedModeBanner: false,
          locale: const Locale('ar', 'EG'),
          theme: EnhancedAppTheme.lightTheme,
          localizationsDelegates: const [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: const [Locale('ar', 'EG')],
          initialRoute: '/splash',
          getPages: [
            GetPage(name: '/splash', page: () => const SplashPage()),
            GetPage(name: '/dashboard', page: () => const DashboardPage()),
            GetPage(
              name: '/raw-materials',
              page: () => const RawMaterialsPage(),
            ),
            GetPage(name: '/machines', page: () => const MachinesPage()),
            GetPage(name: '/molds', page: () => const MoldsPage()),
            GetPage(
              name: '/daily-production',
              page: () => const DailyProductionPage(),
            ),
            GetPage(
              name: '/injection-cost',
              page: () => const InjectionCostPage(),
            ),
            GetPage(
              name: '/production-priority',
              page: () => const ProductionPriorityPage(),
            ),
            GetPage(name: '/assembly', page: () => const AssemblyPage()),
            GetPage(
              name: '/assembly-report',
              page: () => const AssemblyReportPage(),
            ),
            GetPage(name: '/inventory', page: () => const InventoryPage()),
            GetPage(
              name: '/raw-material-report',
              page: () => const RawMaterialReportPage(),
            ),
            GetPage(name: '/cost-report', page: () => const CostReportPage()),
            GetPage(name: '/sales', page: () => const SalesPage()),
            GetPage(name: '/orders', page: () => const OrdersPage()),
            GetPage(name: '/sales-report', page: () => const SalesReportPage()),
            GetPage(name: '/backup', page: () => BackupPage()),
            GetPage(
              name: '/performance-test',
              page: () => PerformanceTestPage(),
            ),
            // المزيد من الصفحات سيتم إضافتها لاحقاً
          ],
          fallbackLocale: const Locale('ar', 'EG'),
          textDirection: TextDirection.rtl,
        );
      },
    );
  }
}
