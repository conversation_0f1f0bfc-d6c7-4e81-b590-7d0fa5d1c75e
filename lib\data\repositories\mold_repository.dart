import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:uuid/uuid.dart';

class MoldRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء إسطمبة جديدة
  Future<String> createMold(Mold mold) async {
    final moldWithId = Mold(
      id: _uuid.v4(),
      name: mold.name,
      productCode: mold.productCode,
      status: mold.status,
      currentMachineId: mold.currentMachineId,
      cavityCount: mold.cavityCount,
      singlePartWeight: mold.singlePartWeight,
      createdAt: mold.createdAt,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableMolds,
      moldWithId.toMap(),
    );
  }

  // الحصول على جميع الإسطمبات
  Future<List<Mold>> getAllMolds() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableMolds,
    );

    return maps.map((map) => Mold.fromMap(map)).toList();
  }

  // الحصول على إسطمبة بمعرف معين
  Future<Mold?> getMoldById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableMolds,
      id,
    );

    if (map == null) {
      return null;
    }

    return Mold.fromMap(map);
  }

  // الحصول على الإسطمبات بكود منتج معين
  Future<List<Mold>> getMoldsByProductCode(String productCode) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableMolds} WHERE productCode = ?',
      [productCode],
    );

    return maps.map((map) => Mold.fromMap(map)).toList();
  }

  // تحديث إسطمبة
  Future<int> updateMold(Mold mold) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableMolds,
      mold.toMap(),
    );
  }

  // حذف إسطمبة
  Future<int> deleteMold(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableMolds, id);
  }

  // الحصول على الإسطمبات المتاحة
  Future<List<Mold>> getAvailableMolds() async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableMolds}
      WHERE status = ?
    ''';

    List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(query, [
      MoldStatus.available,
    ]);

    return maps.map((map) => Mold.fromMap(map)).toList();
  }

  // الحصول على الإسطمبات مع الماكينات المرتبطة بها
  Future<List<Map<String, dynamic>>> getMoldsWithMachines() async {
    String query = '''
      SELECT mold.*, machine.name as machineName, machine.model as machineModel
      FROM ${DatabaseHelper.tableMolds} mold
      LEFT JOIN ${DatabaseHelper.tableMachines} machine ON mold.currentMachineId = machine.id
    ''';

    return await _databaseHelper.rawQuery(query);
  }

  // تغيير حالة الإسطمبة
  Future<int> changeMoldStatus(String id, String status) async {
    Mold? mold = await getMoldById(id);

    if (mold == null) {
      throw Exception("الإسطمبة غير موجودة");
    }

    // إذا كانت الإسطمبة مرتبطة بماكينة ونريد وضعها في حالة الصيانة
    // يجب إزالتها من الماكينة أولاً
    if (status == MoldStatus.maintenance && mold.currentMachineId != null) {
      String updateMachineQuery = '''
        UPDATE ${DatabaseHelper.tableMachines}
        SET currentMoldId = NULL
        WHERE id = ?
      ''';

      await _databaseHelper.rawQuery(updateMachineQuery, [
        mold.currentMachineId,
      ]);

      Mold updatedMold = mold.copyWith(status: status, currentMachineId: null);

      return await updateMold(updatedMold);
    }

    // لحالات أخرى، نحدث فقط حالة الإسطمبة
    Mold updatedMold = mold.copyWith(status: status);

    return await updateMold(updatedMold);
  }
}
