import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/controllers/cost_calculation_controller.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/utils/format_utils.dart';

class CostReportPage extends StatelessWidget {
  const CostReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(CostCalculationController());

    return Scaffold(
      appBar: AppBar(title: const Text('تقرير التكاليف')),
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CustomLoadingIndicator());
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildMachineSelector(controller),
              const SizedBox(height: 16),
              _buildMoldSelector(controller),
              const SizedBox(height: 24),
              if (controller.selectedMachine.value != null &&
                  controller.selectedMold.value != null) ...[
                _buildProductionSettings(controller),
                const SizedBox(height: 24),
                _buildCostBreakdown(controller),
              ],
            ],
          ),
        );
      }),
    );
  }

  Widget _buildMachineSelector(CostCalculationController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر الماكينة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (controller.machines.isEmpty)
              const Center(child: Text('لا توجد ماكينات متاحة'))
            else
              DropdownButtonFormField<dynamic>(
                decoration: const InputDecoration(
                  labelText: 'الماكينة',
                  border: OutlineInputBorder(),
                ),
                value: controller.selectedMachine.value,
                items:
                    controller.machines.map((machine) {
                      return DropdownMenuItem<dynamic>(
                        value: machine,
                        child: Text('${machine.name} - ${machine.model}'),
                      );
                    }).toList(),
                onChanged: (machine) {
                  if (machine != null) {
                    controller.selectMachine(machine);
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoldSelector(CostCalculationController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختر الإسطمبة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            if (controller.molds.isEmpty)
              const Center(child: Text('لا توجد إسطمبات متاحة'))
            else
              DropdownButtonFormField<dynamic>(
                decoration: const InputDecoration(
                  labelText: 'الإسطمبة',
                  border: OutlineInputBorder(),
                ),
                value: controller.selectedMold.value,
                items:
                    controller.molds.map((mold) {
                      return DropdownMenuItem<dynamic>(
                        value: mold,
                        child: Text('${mold.name} - ${mold.productCode}'),
                      );
                    }).toList(),
                onChanged: (mold) {
                  if (mold != null) {
                    controller.selectMold(mold);
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionSettings(CostCalculationController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إعدادات الإنتاج',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: controller.partsProduced.value.toString(),
                    keyboardType: TextInputType.number,
                    decoration: const InputDecoration(
                      labelText: 'عدد القطع المنتجة',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final count = int.tryParse(value);
                      if (count != null) {
                        controller.setPartsProduced(count);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    initialValue: controller.cycleTime.value.toString(),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'زمن الدورة (ثانية)',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final time = double.tryParse(value);
                      if (time != null) {
                        controller.setCycleTime(time);
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    initialValue: controller.electricityRate.value.toString(),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'سعر الكيلووات/ساعة',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final rate = double.tryParse(value);
                      if (rate != null) {
                        controller.setElectricityRate(rate);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    initialValue:
                        controller.materialPricePerKg.value.toString(),
                    keyboardType: const TextInputType.numberWithOptions(
                      decimal: true,
                    ),
                    decoration: const InputDecoration(
                      labelText: 'سعر الكيلوجرام',
                      border: OutlineInputBorder(),
                    ),
                    onChanged: (value) {
                      final price = double.tryParse(value);
                      if (price != null) {
                        controller.setMaterialPrice(price);
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => controller.calculateInjectionCost(),
              style: ElevatedButton.styleFrom(
                backgroundColor: EnhancedAppTheme.primaryColor,
                foregroundColor: Colors.white,
              ),
              child: const Text('حساب التكلفة'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostBreakdown(CostCalculationController controller) {
    return Obx(() {
      if (controller.isCalculating.value) {
        return const Center(child: CustomLoadingIndicator());
      }

      return Card(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'تفاصيل التكلفة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              _buildCostItem(
                'تكلفة الكهرباء',
                controller.electricityCost.value,
              ),
              _buildCostItem('تكلفة المشغل', controller.operatorCost.value),
              _buildCostItem(
                'تكلفة المادة الخام',
                controller.materialCost.value,
              ),
              const Divider(),
              _buildCostItem(
                'إجمالي التكلفة',
                controller.totalCost.value,
                isBold: true,
              ),
              _buildCostItem(
                'تكلفة القطعة الواحدة',
                controller.singlePartCost.value,
                isBold: true,
                color: EnhancedAppTheme.primaryColor,
              ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildCostItem(
    String label,
    double value, {
    bool isBold = false,
    Color? color,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
          Text(
            '${FormatUtils.formatCurrency(value)} ج.م',
            style: TextStyle(
              fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
