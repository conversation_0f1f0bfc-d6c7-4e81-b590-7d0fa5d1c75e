import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mostafa_final/data/models/predictive_analytics.dart';
import 'package:mostafa_final/services/predictive_analytics_service.dart';
import 'package:mostafa_final/services/advanced_notification_service.dart';
import 'package:mostafa_final/services/notification_service.dart';

/// وحدة التحكم للتحليل التنبؤي والذكاء الاصطناعي
class PredictiveAnalyticsController extends GetxController {
  final PredictiveAnalyticsService _analyticsService =
      PredictiveAnalyticsService();
  late final AdvancedNotificationService _notificationService;

  // حالة التحميل
  final isLoading = false.obs;
  final isAnalysisRunning = false.obs;

  // التنبؤات بالأعطال
  final failurePredictions = <FailurePrediction>[].obs;
  final selectedFailurePrediction = Rxn<FailurePrediction>();

  // تحليل الاتجاهات
  final trendAnalyses = <TrendAnalysis>[].obs;
  final selectedTrendAnalysis = Rxn<TrendAnalysis>();

  // التوقعات المستقبلية
  final futureForecasts = <FutureForecast>[].obs;
  final selectedForecast = Rxn<FutureForecast>();

  // تحليل الارتباطات
  final correlationAnalyses = <CorrelationAnalysis>[].obs;
  final selectedCorrelation = Rxn<CorrelationAnalysis>();

  // التنبيهات التنبؤية
  final predictiveNotifications = <PredictiveNotification>[].obs;

  // إعدادات التحليل
  final analysisSettings = <String, dynamic>{}.obs;
  final forecastDays = 30.obs;
  final analysisWindow = 30.obs;

  // فلاتر وتصنيف
  final selectedFailureTypes = <FailureType>[].obs;
  final selectedSeverityLevels = <NotificationSeverity>[].obs;
  final showOnlyHighRisk = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeServices();
    loadInitialData();
  }

  /// تهيئة الخدمات
  void _initializeServices() {
    try {
      // تهيئة خدمة التنبيهات العادية أولاً
      if (!Get.isRegistered<NotificationService>()) {
        Get.put(NotificationService());
      }

      // تهيئة خدمة التنبيهات المتقدمة
      if (!Get.isRegistered<AdvancedNotificationService>()) {
        Get.put(AdvancedNotificationService());
      }
      _notificationService = AdvancedNotificationService.instance;

      // ربط التنبيهات التنبؤية
      ever(_notificationService.predictiveNotifications, (notifications) {
        predictiveNotifications.value = notifications;
      });
    } catch (e) {
      debugPrint('خطأ في تهيئة الخدمات: $e');
      // إنشاء خدمة مؤقتة في حالة الفشل
      Get.put(NotificationService());
      Get.put(AdvancedNotificationService());
      _notificationService = AdvancedNotificationService.instance;
    }
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadFailurePredictions(),
        loadTrendAnalyses(),
        loadFutureForecasts(),
        loadCorrelationAnalyses(),
      ]);
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل التنبؤات بالأعطال
  Future<void> loadFailurePredictions() async {
    try {
      final predictions = await _analyticsService.predictMachineFailures(
        daysAhead: forecastDays.value,
      );
      failurePredictions.value = predictions;
    } catch (e) {
      debugPrint('خطأ في تحميل التنبؤات بالأعطال: $e');
    }
  }

  /// تحميل تحليل الاتجاهات
  Future<void> loadTrendAnalyses() async {
    try {
      final trends = await _analyticsService.analyzeTrends(
        analysisWindow: analysisWindow.value,
      );
      trendAnalyses.value = trends;
    } catch (e) {
      debugPrint('خطأ في تحميل تحليل الاتجاهات: $e');
    }
  }

  /// تحميل التوقعات المستقبلية
  Future<void> loadFutureForecasts() async {
    try {
      final forecasts = await _analyticsService.generateFutureForecasts(
        forecastDays: forecastDays.value,
      );
      futureForecasts.value = forecasts;
    } catch (e) {
      debugPrint('خطأ في تحميل التوقعات المستقبلية: $e');
    }
  }

  /// تحميل تحليل الارتباطات
  Future<void> loadCorrelationAnalyses() async {
    try {
      final correlations = await _analyticsService.analyzeCorrelations();
      correlationAnalyses.value = correlations;
    } catch (e) {
      debugPrint('خطأ في تحميل تحليل الارتباطات: $e');
    }
  }

  /// تشغيل التحليل الشامل
  Future<void> runComprehensiveAnalysis() async {
    if (isAnalysisRunning.value) return;

    isAnalysisRunning.value = true;
    try {
      // تشغيل التحليل التنبؤي المتقدم
      await _notificationService.runPredictiveAnalysis();

      // تحديث جميع البيانات
      await loadInitialData();

      Get.snackbar('نجح', 'تم إكمال التحليل الشامل بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في التحليل الشامل: $e');
    } finally {
      isAnalysisRunning.value = false;
    }
  }

  /// تحديث إعدادات التحليل
  void updateAnalysisSettings({int? newForecastDays, int? newAnalysisWindow}) {
    if (newForecastDays != null) {
      forecastDays.value = newForecastDays;
    }
    if (newAnalysisWindow != null) {
      analysisWindow.value = newAnalysisWindow;
    }

    // إعادة تحميل البيانات مع الإعدادات الجديدة
    loadInitialData();
  }

  /// فلترة التنبؤات بالأعطال
  List<FailurePrediction> get filteredFailurePredictions {
    var filtered = failurePredictions.toList();

    // فلترة حسب نوع العطل
    if (selectedFailureTypes.isNotEmpty) {
      filtered =
          filtered
              .where(
                (p) => selectedFailureTypes.contains(p.predictedFailureType),
              )
              .toList();
    }

    // فلترة المخاطر العالية فقط
    if (showOnlyHighRisk.value) {
      filtered = filtered.where((p) => p.failureProbability > 70).toList();
    }

    return filtered;
  }

  /// فلترة التنبيهات التنبؤية
  List<PredictiveNotification> get filteredPredictiveNotifications {
    var filtered = predictiveNotifications.toList();

    // فلترة حسب مستوى الشدة
    if (selectedSeverityLevels.isNotEmpty) {
      filtered =
          filtered
              .where((n) => selectedSeverityLevels.contains(n.severity))
              .toList();
    }

    return filtered;
  }

  /// تحديد تنبؤ عطل
  void selectFailurePrediction(FailurePrediction prediction) {
    selectedFailurePrediction.value = prediction;
  }

  /// تحديد تحليل اتجاه
  void selectTrendAnalysis(TrendAnalysis trend) {
    selectedTrendAnalysis.value = trend;
  }

  /// تحديد توقع مستقبلي
  void selectForecast(FutureForecast forecast) {
    selectedForecast.value = forecast;
  }

  /// تحديد تحليل ارتباط
  void selectCorrelation(CorrelationAnalysis correlation) {
    selectedCorrelation.value = correlation;
  }

  /// تبديل فلتر المخاطر العالية
  void toggleHighRiskFilter() {
    showOnlyHighRisk.value = !showOnlyHighRisk.value;
  }

  /// إضافة/إزالة نوع عطل من الفلتر
  void toggleFailureTypeFilter(FailureType type) {
    if (selectedFailureTypes.contains(type)) {
      selectedFailureTypes.remove(type);
    } else {
      selectedFailureTypes.add(type);
    }
  }

  /// إضافة/إزالة مستوى شدة من الفلتر
  void toggleSeverityFilter(NotificationSeverity severity) {
    if (selectedSeverityLevels.contains(severity)) {
      selectedSeverityLevels.remove(severity);
    } else {
      selectedSeverityLevels.add(severity);
    }
  }

  /// مسح جميع الفلاتر
  void clearAllFilters() {
    selectedFailureTypes.clear();
    selectedSeverityLevels.clear();
    showOnlyHighRisk.value = false;
  }

  /// وضع علامة مقروء على التنبيه التنبؤي
  void markNotificationAsRead(String notificationId) {
    _notificationService.markPredictiveNotificationAsRead(notificationId);
  }

  /// وضع علامة إجراء مُتخذ
  void markActionTaken(String notificationId) {
    _notificationService.markActionTaken(notificationId);
  }

  /// الحصول على إحصائيات سريعة
  Map<String, dynamic> get quickStats {
    final highRiskPredictions =
        failurePredictions.where((p) => p.failureProbability > 70).length;
    final mediumRiskPredictions =
        failurePredictions
            .where(
              (p) => p.failureProbability > 50 && p.failureProbability <= 70,
            )
            .length;
    final criticalNotifications =
        predictiveNotifications
            .where((n) => n.severity == NotificationSeverity.critical)
            .length;
    final unreadNotifications =
        predictiveNotifications.where((n) => !n.isRead).length;

    final avgFailureProbability =
        failurePredictions.isNotEmpty
            ? failurePredictions
                    .map((p) => p.failureProbability)
                    .reduce((a, b) => a + b) /
                failurePredictions.length
            : 0.0;

    return {
      'totalPredictions': failurePredictions.length,
      'highRiskPredictions': highRiskPredictions,
      'mediumRiskPredictions': mediumRiskPredictions,
      'criticalNotifications': criticalNotifications,
      'unreadNotifications': unreadNotifications,
      'avgFailureProbability': avgFailureProbability,
      'totalTrends': trendAnalyses.length,
      'totalForecasts': futureForecasts.length,
      'totalCorrelations': correlationAnalyses.length,
    };
  }

  /// الحصول على التوصيات الذكية
  List<String> get smartRecommendations {
    final recommendations = <String>[];
    final stats = quickStats;

    // توصيات بناءً على التنبؤات
    if (stats['highRiskPredictions'] > 0) {
      recommendations.add(
        'يوجد ${stats['highRiskPredictions']} ماكينة معرضة لخطر عطل عالي - اتخذ إجراءات فورية',
      );
    }

    if (stats['avgFailureProbability'] > 60) {
      recommendations.add(
        'متوسط احتمالية الأعطال مرتفع (${stats['avgFailureProbability'].toStringAsFixed(1)}%) - راجع برنامج الصيانة الوقائية',
      );
    }

    // توصيات بناءً على الاتجاهات
    final negativeStrongs =
        trendAnalyses
            .where(
              (t) =>
                  t.direction == TrendDirection.decreasing &&
                  t.trendStrength > 70,
            )
            .length;

    if (negativeStrongs > 0) {
      recommendations.add(
        'يوجد $negativeStrongs مؤشر يظهر اتجاهاً سلبياً قوياً - تحليل الأسباب مطلوب',
      );
    }

    // توصيات بناءً على التنبيهات
    if (stats['criticalNotifications'] > 0) {
      recommendations.add(
        'يوجد ${stats['criticalNotifications']} تنبيه حرج يحتاج اهتمام فوري',
      );
    }

    // توصيات عامة
    if (recommendations.isEmpty) {
      recommendations.add('الوضع مستقر - استمر في المراقبة الدورية');
    }

    return recommendations;
  }

  /// تصدير تقرير التحليل التنبؤي
  Future<void> exportPredictiveAnalysisReport() async {
    try {
      // هنا يمكن إضافة منطق التصدير
      Get.snackbar('معلومات', 'ميزة تصدير التقرير قيد التطوير');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير التقرير: $e');
    }
  }

  /// جدولة تحليل دوري
  void schedulePeriodicAnalysis({
    required NotificationSchedule schedule,
    required String name,
  }) {
    final scheduledNotification = ScheduledNotification(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      type: ScheduledNotificationType.performance_review,
      description: 'تحليل تنبؤي دوري مجدول',
      schedule: schedule,
      startDate: DateTime.now(),
      nextExecutionTime: _calculateNextExecution(schedule),
    );

    _notificationService.addScheduledNotification(scheduledNotification);
    Get.snackbar('نجح', 'تم جدولة التحليل الدوري بنجاح');
  }

  /// حساب وقت التنفيذ التالي
  DateTime _calculateNextExecution(NotificationSchedule schedule) {
    final now = DateTime.now();
    switch (schedule) {
      case NotificationSchedule.daily:
        return now.add(const Duration(days: 1));
      case NotificationSchedule.weekly:
        return now.add(const Duration(days: 7));
      case NotificationSchedule.monthly:
        return DateTime(now.year, now.month + 1, now.day);
      default:
        return now.add(const Duration(hours: 1));
    }
  }
}
