import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// قائمة متجاوبة مع حجم الشاشة
class ResponsiveList extends StatelessWidget {
  final List<Widget> children;
  final EdgeInsetsGeometry? padding;
  final ScrollPhysics? physics;
  final bool shrinkWrap;
  final ScrollController? controller;
  final bool? primary;
  final Widget? separator;
  final String? emptyMessage;
  final IconData? emptyIcon;
  final Widget? emptyWidget;
  final bool isLoading;
  final Widget? loadingWidget;

  const ResponsiveList({
    super.key,
    required this.children,
    this.padding,
    this.physics,
    this.shrinkWrap = false,
    this.controller,
    this.primary,
    this.separator,
    this.emptyMessage,
    this.emptyIcon,
    this.emptyWidget,
    this.isLoading = false,
    this.loadingWidget,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
    if (isLoading) {
      return loadingWidget ?? const Center(child: CircularProgressIndicator());
    }

    // عرض رسالة فارغة إذا كانت القائمة فارغة
    if (children.isEmpty) {
      if (emptyWidget != null) {
        return emptyWidget!;
      }

      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (emptyIcon != null)
              Icon(
                emptyIcon,
                size: ScreenSize.isSmallScreen ? 48 : 64,
                color: Colors.grey.shade400,
              ),
            if (emptyIcon != null) SizedBox(height: ScreenSize.getPadding(16)),
            if (emptyMessage != null)
              TextUtils.responsiveText(
                emptyMessage!,
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                  color: Colors.grey.shade600,
                ),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      );
    }

    // عرض القائمة مع أو بدون فاصل
    if (separator != null) {
      return ListView.separated(
        padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
        physics: physics,
        shrinkWrap: shrinkWrap,
        controller: controller,
        primary: primary,
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
        separatorBuilder: (context, index) => separator!,
      );
    }

    return ListView(
      padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      primary: primary,
      children: children,
    );
  }

  /// إنشاء قائمة متجاوبة من العناصر
  static ResponsiveList fromItems<T>({
    required List<T> items,
    required Widget Function(T item) itemBuilder,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool shrinkWrap = false,
    ScrollController? controller,
    bool? primary,
    Widget? separator,
    String? emptyMessage,
    IconData? emptyIcon,
    Widget? emptyWidget,
    bool isLoading = false,
    Widget? loadingWidget,
  }) {
    return ResponsiveList(
      padding: padding,
      physics: physics,
      shrinkWrap: shrinkWrap,
      controller: controller,
      primary: primary,
      separator: separator,
      emptyMessage: emptyMessage,
      emptyIcon: emptyIcon,
      emptyWidget: emptyWidget,
      isLoading: isLoading,
      loadingWidget: loadingWidget,
      children: items.map((item) => itemBuilder(item)).toList(),
    );
  }
}
