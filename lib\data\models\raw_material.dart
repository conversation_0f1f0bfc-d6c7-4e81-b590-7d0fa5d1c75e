class RawMaterial {
  final String? id;
  final String name;
  final String code;
  final double availableQuantity; // الكمية المتاحة بالكيلوجرام
  final double costPerKg; // التكلفة لكل كيلوجرام
  final String color; // اللون

  RawMaterial({
    this.id,
    required this.name,
    required this.code,
    required this.availableQuantity,
    required this.costPerKg,
    required this.color,
  });

  // تحويل RawMaterial إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'code': code,
      'availableQuantity': availableQuantity,
      'costPerKg': costPerKg,
      'color': color,
    };
  }

  // إنشاء RawMaterial من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory RawMaterial.fromMap(Map<String, dynamic> map) {
    return RawMaterial(
      id: map['id'],
      name: map['name'],
      code: map['code'],
      availableQuantity: map['availableQuantity'],
      costPerKg: map['costPerKg'],
      color: map['color'],
    );
  }

  // إنشاء نسخة معدلة من RawMaterial
  RawMaterial copyWith({
    String? id,
    String? name,
    String? code,
    double? availableQuantity,
    double? costPerKg,
    String? color,
  }) {
    return RawMaterial(
      id: id ?? this.id,
      name: name ?? this.name,
      code: code ?? this.code,
      availableQuantity: availableQuantity ?? this.availableQuantity,
      costPerKg: costPerKg ?? this.costPerKg,
      color: color ?? this.color,
    );
  }
}

// سجل استلام الخامات
class RawMaterialReceipt {
  final String? id;
  final String rawMaterialId;
  final DateTime date;
  final double quantity;
  final double pricePerKg;

  RawMaterialReceipt({
    this.id,
    required this.rawMaterialId,
    required this.date,
    required this.quantity,
    required this.pricePerKg,
  });

  // تحويل RawMaterialReceipt إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'rawMaterialId': rawMaterialId,
      'date': date.toIso8601String(),
      'quantity': quantity,
      'pricePerKg': pricePerKg,
    };
  }

  // إنشاء RawMaterialReceipt من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory RawMaterialReceipt.fromMap(Map<String, dynamic> map) {
    return RawMaterialReceipt(
      id: map['id'],
      rawMaterialId: map['rawMaterialId'],
      date: DateTime.parse(map['date']),
      quantity: map['quantity'],
      pricePerKg: map['pricePerKg'],
    );
  }
}
