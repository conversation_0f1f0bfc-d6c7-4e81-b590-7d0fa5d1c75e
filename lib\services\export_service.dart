import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'package:excel/excel.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';

class ExportService {
  static final ExportService _instance = ExportService._internal();
  factory ExportService() => _instance;
  ExportService._internal();

  /// تصدير تقرير تنفيذي إلى PDF
  Future<void> exportReportToPDF(ExecutiveReport report, {
    List<KPI>? kpis,
    PerformanceSummary? summary,
  }) async {
    try {
      // إنشاء مستند PDF
      final pdf = pw.Document();
      
      // تحميل الخط العربي
      final arabicFont = await _loadArabicFont();
      
      // إضافة صفحة التقرير
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          textDirection: pw.TextDirection.rtl,
          theme: pw.ThemeData.withFont(
            base: arabicFont,
            bold: arabicFont,
          ),
          build: (pw.Context context) {
            return [
              _buildPDFHeader(report),
              pw.SizedBox(height: 20),
              _buildPDFSummary(summary),
              pw.SizedBox(height: 20),
              _buildPDFKPIs(kpis),
              pw.SizedBox(height: 20),
              _buildPDFInsights(report),
              pw.SizedBox(height: 20),
              _buildPDFRecommendations(report),
            ];
          },
        ),
      );

      // حفظ ومشاركة الملف
      await _savePDFAndShare(pdf, 'تقرير_تنفيذي_${_formatDateForFile(report.generatedAt)}');
      
      Get.snackbar('نجح', 'تم تصدير التقرير بصيغة PDF بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير التقرير: $e');
    }
  }

  /// تصدير البيانات إلى Excel
  Future<void> exportDataToExcel({
    required List<KPI> kpis,
    PerformanceSummary? summary,
    Map<String, List<ChartData>>? chartData,
  }) async {
    try {
      // إنشاء ملف Excel
      final excel = Excel.createExcel();
      
      // حذف الورقة الافتراضية
      excel.delete('Sheet1');
      
      // إضافة ورقة المؤشرات
      _addKPIsSheet(excel, kpis);
      
      // إضافة ورقة ملخص الأداء
      if (summary != null) {
        _addPerformanceSummarySheet(excel, summary);
      }
      
      // إضافة ورقة بيانات الرسوم البيانية
      if (chartData != null) {
        _addChartDataSheet(excel, chartData);
      }
      
      // حفظ ومشاركة الملف
      await _saveExcelAndShare(excel, 'بيانات_المصنع_${_formatDateForFile(DateTime.now())}');
      
      Get.snackbar('نجح', 'تم تصدير البيانات بصيغة Excel بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير البيانات: $e');
    }
  }

  /// تصدير رسم بياني كصورة
  Future<void> exportChartAsImage(GlobalKey chartKey, String chartName) async {
    try {
      // التقاط الرسم البياني كصورة
      final RenderRepaintBoundary boundary = 
          chartKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      
      final ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      final ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final Uint8List pngBytes = byteData!.buffer.asUint8List();
      
      // حفظ ومشاركة الصورة
      await _saveImageAndShare(pngBytes, '${chartName}_${_formatDateForFile(DateTime.now())}');
      
      Get.snackbar('نجح', 'تم تصدير الرسم البياني كصورة بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تصدير الرسم البياني: $e');
    }
  }

  /// تحميل الخط العربي للـ PDF
  Future<pw.Font> _loadArabicFont() async {
    try {
      final fontData = await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
      return pw.Font.ttf(fontData);
    } catch (e) {
      // في حالة عدم وجود الخط العربي، استخدم الخط الافتراضي
      return await PdfGoogleFonts.notoSansArabicRegular();
    }
  }

  /// بناء رأس التقرير في PDF
  pw.Widget _buildPDFHeader(ExecutiveReport report) {
    return pw.Container(
      width: double.infinity,
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        color: PdfColors.blue50,
        border: pw.Border.all(color: PdfColors.blue),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            report.title,
            style: pw.TextStyle(fontSize: 24, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Text(
            report.description,
            style: const pw.TextStyle(fontSize: 14),
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              pw.Text('تاريخ الإنشاء: ${DateFormat('yyyy-MM-dd HH:mm').format(report.generatedAt)}'),
              pw.Text('الفترة: ${DateFormat('yyyy-MM-dd').format(report.startDate)} - ${DateFormat('yyyy-MM-dd').format(report.endDate)}'),
            ],
          ),
        ],
      ),
    );
  }

  /// بناء ملخص الأداء في PDF
  pw.Widget _buildPDFSummary(PerformanceSummary? summary) {
    if (summary == null) return pw.SizedBox();
    
    return pw.Container(
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'ملخص الأداء العام',
            style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
          ),
          pw.SizedBox(height: 10),
          pw.Container(
            padding: const pw.EdgeInsets.all(15),
            decoration: pw.BoxDecoration(
              color: PdfColors.green50,
              border: pw.Border.all(color: PdfColors.green),
            ),
            child: pw.Column(
              children: [
                pw.Text(
                  'النتيجة الإجمالية: ${summary.overallScore.toStringAsFixed(1)}%',
                  style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold),
                ),
                pw.SizedBox(height: 10),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceAround,
                  children: [
                    _buildPDFMetric('كفاءة الإنتاج', '${summary.production.efficiency.toStringAsFixed(1)}%'),
                    _buildPDFMetric('معدل التوفر', '${summary.maintenance.availability.toStringAsFixed(1)}%'),
                    _buildPDFMetric('هامش الربح', '${summary.financial.profitMargin.toStringAsFixed(1)}%'),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// بناء مؤشر في PDF
  pw.Widget _buildPDFMetric(String title, String value) {
    return pw.Column(
      children: [
        pw.Text(value, style: pw.TextStyle(fontSize: 16, fontWeight: pw.FontWeight.bold)),
        pw.Text(title, style: const pw.TextStyle(fontSize: 12)),
      ],
    );
  }

  /// بناء المؤشرات في PDF
  pw.Widget _buildPDFKPIs(List<KPI>? kpis) {
    if (kpis == null || kpis.isEmpty) return pw.SizedBox();
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'المؤشرات الرئيسية للأداء',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        pw.Table(
          border: pw.TableBorder.all(),
          children: [
            // رأس الجدول
            pw.TableRow(
              decoration: const pw.BoxDecoration(color: PdfColors.grey200),
              children: [
                _buildPDFTableCell('المؤشر', isHeader: true),
                _buildPDFTableCell('القيمة الحالية', isHeader: true),
                _buildPDFTableCell('الهدف', isHeader: true),
                _buildPDFTableCell('التحقق', isHeader: true),
              ],
            ),
            // بيانات المؤشرات
            ...kpis.take(10).map((kpi) => pw.TableRow(
              children: [
                _buildPDFTableCell(kpi.name),
                _buildPDFTableCell('${kpi.currentValue.toStringAsFixed(1)}${kpi.unit.symbol}'),
                _buildPDFTableCell('${kpi.targetValue.toStringAsFixed(1)}${kpi.unit.symbol}'),
                _buildPDFTableCell('${kpi.achievementPercentage.toStringAsFixed(1)}%'),
              ],
            )),
          ],
        ),
      ],
    );
  }

  /// بناء خلية جدول في PDF
  pw.Widget _buildPDFTableCell(String text, {bool isHeader = false}) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          fontSize: isHeader ? 12 : 10,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  /// بناء الرؤى في PDF
  pw.Widget _buildPDFInsights(ExecutiveReport report) {
    if (report.insights.isEmpty) return pw.SizedBox();
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'الرؤى والتحليلات',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.insights.map((insight) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('• ', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              pw.Expanded(child: pw.Text(insight)),
            ],
          ),
        )),
      ],
    );
  }

  /// بناء التوصيات في PDF
  pw.Widget _buildPDFRecommendations(ExecutiveReport report) {
    if (report.recommendations.isEmpty) return pw.SizedBox();
    
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'التوصيات',
          style: pw.TextStyle(fontSize: 18, fontWeight: pw.FontWeight.bold),
        ),
        pw.SizedBox(height: 10),
        ...report.recommendations.map((recommendation) => pw.Container(
          margin: const pw.EdgeInsets.only(bottom: 5),
          child: pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text('• ', style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
              pw.Expanded(child: pw.Text(recommendation)),
            ],
          ),
        )),
      ],
    );
  }

  /// إضافة ورقة المؤشرات إلى Excel
  void _addKPIsSheet(Excel excel, List<KPI> kpis) {
    final sheet = excel['المؤشرات الرئيسية'];
    
    // رأس الجدول
    final headers = ['المؤشر', 'القيمة الحالية', 'الهدف', 'القيمة السابقة', 'نسبة التحقق', 'التغيير %', 'الوحدة'];
    for (int i = 0; i < headers.length; i++) {
      final cell = sheet.cell(CellIndex.indexByColumnRow(columnIndex: i, rowIndex: 0));
      cell.value = TextCellValue(headers[i]);
      cell.cellStyle = CellStyle(
        bold: true,
        backgroundColorHex: ExcelColor.blue,
        fontColorHex: ExcelColor.white,
      );
    }
    
    // بيانات المؤشرات
    for (int i = 0; i < kpis.length; i++) {
      final kpi = kpis[i];
      final row = i + 1;
      
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue(kpi.name);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(kpi.currentValue);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: row)).value = DoubleCellValue(kpi.targetValue);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 3, rowIndex: row)).value = DoubleCellValue(kpi.previousValue);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 4, rowIndex: row)).value = DoubleCellValue(kpi.achievementPercentage);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 5, rowIndex: row)).value = DoubleCellValue(kpi.changePercentage);
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 6, rowIndex: row)).value = TextCellValue(kpi.unit.symbol);
    }
  }

  /// إضافة ورقة ملخص الأداء إلى Excel
  void _addPerformanceSummarySheet(Excel excel, PerformanceSummary summary) {
    final sheet = excel['ملخص الأداء'];
    
    // النتيجة الإجمالية
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: 0)).value = TextCellValue('النتيجة الإجمالية');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: 0)).value = DoubleCellValue(summary.overallScore);
    
    // مؤشرات الإنتاج
    int row = 2;
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('مؤشرات الإنتاج');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('كفاءة الإنتاج');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.production.efficiency);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('إجمالي الإنتاج');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = IntCellValue(summary.production.totalProduction);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('استغلال الطاقة');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.production.capacityUtilization);
    
    // مؤشرات الصيانة
    row += 2;
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('مؤشرات الصيانة');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('MTBF');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.maintenance.mtbf);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('MTTR');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.maintenance.mttr);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('معدل التوفر');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.maintenance.availability);
    
    // المؤشرات المالية
    row += 2;
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: row)).value = TextCellValue('المؤشرات المالية');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('إجمالي الإيرادات');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.financial.totalRevenue);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('إجمالي التكاليف');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.financial.totalCosts);
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: ++row)).value = TextCellValue('هامش الربح');
    sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: row)).value = DoubleCellValue(summary.financial.profitMargin);
  }

  /// إضافة ورقة بيانات الرسوم البيانية إلى Excel
  void _addChartDataSheet(Excel excel, Map<String, List<ChartData>> chartData) {
    final sheet = excel['بيانات الرسوم البيانية'];
    
    int currentRow = 0;
    
    chartData.forEach((chartName, data) {
      // عنوان الرسم البياني
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue(chartName);
      currentRow++;
      
      // رأس الجدول
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue('التسمية');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow)).value = TextCellValue('القيمة');
      sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow)).value = TextCellValue('التاريخ');
      currentRow++;
      
      // البيانات
      for (var item in data) {
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 0, rowIndex: currentRow)).value = TextCellValue(item.label);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 1, rowIndex: currentRow)).value = DoubleCellValue(item.value);
        sheet.cell(CellIndex.indexByColumnRow(columnIndex: 2, rowIndex: currentRow)).value = 
            TextCellValue(item.date?.toString() ?? '');
        currentRow++;
      }
      
      currentRow += 2; // فراغ بين الرسوم البيانية
    });
  }

  /// حفظ ومشاركة ملف PDF
  Future<void> _savePDFAndShare(pw.Document pdf, String fileName) async {
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/$fileName.pdf');
    await file.writeAsBytes(await pdf.save());
    
    await Share.shareXFiles([XFile(file.path)], text: 'تقرير تنفيذي من نظام إدارة المصنع');
  }

  /// حفظ ومشاركة ملف Excel
  Future<void> _saveExcelAndShare(Excel excel, String fileName) async {
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/$fileName.xlsx');
    await file.writeAsBytes(excel.save()!);
    
    await Share.shareXFiles([XFile(file.path)], text: 'بيانات المصنع من نظام إدارة المصنع');
  }

  /// حفظ ومشاركة صورة
  Future<void> _saveImageAndShare(Uint8List imageBytes, String fileName) async {
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/$fileName.png');
    await file.writeAsBytes(imageBytes);
    
    await Share.shareXFiles([XFile(file.path)], text: 'رسم بياني من نظام إدارة المصنع');
  }

  /// تنسيق التاريخ للملف
  String _formatDateForFile(DateTime date) {
    return DateFormat('yyyy_MM_dd_HH_mm').format(date);
  }
}
