class Production {
  final String? id;
  final DateTime date;
  final String machineId;
  final String moldId;
  final String rawMaterialId;
  final double rawMaterialUsed; // الخامات المستخدمة بالكيلوجرام
  final int partsProduced; // عدد القطع المنتجة
  final double cycleTime; // وقت الدورة بالثواني
  final double electricityCost; // تكلفة الكهرباء للإنتاج
  final double operatorCost; // تكلفة المشغلين
  final double shiftsCount; // عدد الورديات (1، 1.5، 2، 3)

  Production({
    this.id,
    required this.date,
    required this.machineId,
    required this.moldId,
    required this.rawMaterialId,
    required this.rawMaterialUsed,
    required this.partsProduced,
    required this.cycleTime,
    required this.electricityCost,
    required this.operatorCost,
    this.shiftsCount = 1.0, // القيمة الافتراضية هي وردية واحدة
  });

  // حساب تكلفة حقن القطعة الواحدة
  double get injectionCostPerPart =>
      (electricityCost + operatorCost) / partsProduced;

  // تحويل Production إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'date': date.toIso8601String(),
      'machineId': machineId,
      'moldId': moldId,
      'rawMaterialId': rawMaterialId,
      'rawMaterialUsed': rawMaterialUsed,
      'partsProduced': partsProduced,
      'cycleTime': cycleTime,
      'electricityCost': electricityCost,
      'operatorCost': operatorCost,
      'shiftsCount': shiftsCount,
    };
  }

  // إنشاء Production من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Production.fromMap(Map<String, dynamic> map) {
    return Production(
      id: map['id'],
      date: DateTime.parse(map['date']),
      machineId: map['machineId'],
      moldId: map['moldId'],
      rawMaterialId: map['rawMaterialId'],
      rawMaterialUsed: map['rawMaterialUsed'],
      partsProduced: map['partsProduced'],
      cycleTime: map['cycleTime'],
      electricityCost: map['electricityCost'],
      operatorCost: map['operatorCost'],
      shiftsCount: map['shiftsCount'] ?? 1.0,
    );
  }
}
