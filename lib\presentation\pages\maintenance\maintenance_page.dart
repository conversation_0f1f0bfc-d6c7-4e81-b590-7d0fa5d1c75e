import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/breakdown.dart';
import 'package:mostafa_final/data/models/maintenance.dart';
import 'package:mostafa_final/presentation/controllers/maintenance_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class MaintenancePage extends StatelessWidget {
  const MaintenancePage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(MaintenanceController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة الصيانة والأعطال'),
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: controller.loadInitialData,
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showAnalysisDialog(controller),
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: 'add_breakdown',
            onPressed: controller.showAddBreakdownDialog,
            backgroundColor: Colors.red,
            child: const Icon(Icons.warning),
            tooltip: 'إضافة عطل',
          ),
          const SizedBox(height: 10),
          FloatingActionButton(
            heroTag: 'add_maintenance',
            onPressed: controller.showAddMaintenanceDialog,
            backgroundColor: Colors.green,
            child: const Icon(Icons.build),
            tooltip: 'إضافة صيانة',
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(
            message: 'جاري تحميل بيانات الصيانة...',
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildControlPanel(controller),
              const SizedBox(height: 20),
              _buildQuickStats(controller),
              const SizedBox(height: 20),
              _buildMaintenanceAlerts(controller),
              const SizedBox(height: 20),
              _buildTabSection(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildControlPanel(MaintenanceController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لوحة التحكم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<String>(
                      decoration: const InputDecoration(
                        labelText: 'اختر الماكينة',
                        border: OutlineInputBorder(),
                      ),
                      value:
                          controller.selectedMachineId.value.isEmpty
                              ? null
                              : controller.selectedMachineId.value,
                      items: [
                        const DropdownMenuItem(
                          value: '',
                          child: Text('جميع الماكينات'),
                        ),
                        ...controller.machines.map((machine) {
                          return DropdownMenuItem(
                            value: machine.id,
                            child: Text(machine.name),
                          );
                        }),
                      ],
                      onChanged: (value) {
                        controller.changeMachine(value ?? '');
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => _showDateRangePicker(controller),
                  icon: const Icon(Icons.date_range),
                  label: const Text('تغيير الفترة'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: controller.analyzeBreakdowns,
                    icon: const Icon(Icons.analytics),
                    label: const Text('تحليل الأعطال'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: controller.analyzeMaintenanceEffectiveness,
                    icon: const Icon(Icons.assessment),
                    label: const Text('تحليل الكفاءة'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStats(MaintenanceController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'إحصائيات سريعة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final stats = controller.quickStats;
              return Wrap(
                spacing: 16,
                runSpacing: 16,
                children: [
                  _buildStatCard(
                    'إجمالي الأعطال',
                    '${stats['totalBreakdowns']}',
                    Icons.warning,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'الأعطال النشطة',
                    '${stats['activeBreakdowns']}',
                    Icons.error,
                    Colors.red,
                  ),
                  _buildStatCard(
                    'الصيانات المجدولة',
                    '${stats['totalMaintenance']}',
                    Icons.build,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'الصيانات المستحقة',
                    '${stats['dueMaintenance']}',
                    Icons.schedule,
                    Colors.green,
                  ),
                  _buildStatCard(
                    'الصيانات المتأخرة',
                    '${stats['overdueMaintenance']}',
                    Icons.alarm,
                    Colors.purple,
                  ),
                  _buildStatCard(
                    'التنبيهات',
                    '${stats['maintenanceAlerts']}',
                    Icons.notifications,
                    Colors.amber,
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      width: 150,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMaintenanceAlerts(MaintenanceController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'تنبيهات الصيانة',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Obx(
                  () => Chip(
                    label: Text('${controller.maintenanceAlerts.length}'),
                    backgroundColor:
                        controller.maintenanceAlerts.isEmpty
                            ? Colors.green.shade100
                            : Colors.red.shade100,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.maintenanceAlerts.isEmpty) {
                return const ListTile(
                  leading: Icon(Icons.check_circle, color: Colors.green),
                  title: Text('لا توجد تنبيهات'),
                  subtitle: Text('جميع الصيانات محدثة'),
                );
              }

              return Column(
                children:
                    controller.maintenanceAlerts.take(5).map((alert) {
                      final isOverdue = alert['isOverdue'] as bool;
                      final priority = alert['priority'] as MaintenancePriority;

                      return ListTile(
                        leading: Icon(
                          isOverdue ? Icons.alarm : Icons.schedule,
                          color:
                              isOverdue
                                  ? Colors.red
                                  : _getPriorityColor(priority),
                        ),
                        title: Text(alert['maintenanceTitle'] as String),
                        subtitle: Text(
                          '${alert['machineName']} - ${isOverdue ? 'متأخرة' : 'مستحقة'} ${alert['daysUntilDue']} أيام',
                        ),
                        trailing: Chip(
                          label: Text(priority.displayName),
                          backgroundColor: _getPriorityColor(
                            priority,
                          ).withValues(alpha: 0.2),
                        ),
                      );
                    }).toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildTabSection(MaintenanceController controller) {
    return DefaultTabController(
      length: 3,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'الأعطال', icon: Icon(Icons.warning)),
              Tab(text: 'الصيانة الوقائية', icon: Icon(Icons.build)),
              Tab(text: 'سجلات الصيانة', icon: Icon(Icons.history)),
            ],
          ),
          SizedBox(
            height: 400,
            child: TabBarView(
              children: [
                _buildBreakdownsTab(controller),
                _buildMaintenanceTab(controller),
                _buildMaintenanceRecordsTab(controller),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBreakdownsTab(MaintenanceController controller) {
    return Obx(() {
      if (controller.breakdowns.isEmpty) {
        return const Center(child: Text('لا توجد أعطال مسجلة'));
      }

      return ListView.builder(
        itemCount: controller.breakdowns.length,
        itemBuilder: (context, index) {
          final breakdown = controller.breakdowns[index];
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 4),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor:
                    breakdown.isOngoing ? Colors.red : Colors.green,
                child: Icon(
                  breakdown.isOngoing ? Icons.warning : Icons.check,
                  color: Colors.white,
                ),
              ),
              title: Text(breakdown.type.displayName),
              subtitle: Text(
                '${breakdown.description}\n'
                'المدة: ${breakdown.durationHours.toStringAsFixed(1)} ساعة',
              ),
              trailing:
                  breakdown.isOngoing
                      ? ElevatedButton(
                        onPressed:
                            () => _showFinishBreakdownDialog(
                              controller,
                              breakdown,
                            ),
                        child: const Text('إنهاء'),
                      )
                      : Text(
                        'مكتمل',
                        style: TextStyle(color: Colors.green.shade600),
                      ),
            ),
          );
        },
      );
    });
  }

  Widget _buildMaintenanceTab(MaintenanceController controller) {
    return Obx(() {
      if (controller.maintenanceList.isEmpty) {
        return const Center(child: Text('لا توجد صيانات مجدولة'));
      }

      return ListView.builder(
        itemCount: controller.maintenanceList.length,
        itemBuilder: (context, index) {
          final maintenance = controller.maintenanceList[index];
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 4),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getMaintenanceStatusColor(maintenance.status),
                child: Icon(
                  _getMaintenanceStatusIcon(maintenance.status),
                  color: Colors.white,
                ),
              ),
              title: Text(maintenance.title),
              subtitle: Text(
                '${maintenance.description}\n'
                'الموعد: ${maintenance.nextDue != null ? DateFormat('yyyy-MM-dd').format(maintenance.nextDue!) : 'غير محدد'}',
              ),
              trailing:
                  maintenance.isDue
                      ? ElevatedButton(
                        onPressed:
                            () => _showCompleteMaintenanceDialog(
                              controller,
                              maintenance,
                            ),
                        child: const Text('تنفيذ'),
                      )
                      : Chip(
                        label: Text(maintenance.status.displayName),
                        backgroundColor: _getMaintenanceStatusColor(
                          maintenance.status,
                        ).withValues(alpha: 0.2),
                      ),
            ),
          );
        },
      );
    });
  }

  Widget _buildMaintenanceRecordsTab(MaintenanceController controller) {
    return Obx(() {
      if (controller.maintenanceRecords.isEmpty) {
        return const Center(child: Text('لا توجد سجلات صيانة'));
      }

      return ListView.builder(
        itemCount: controller.maintenanceRecords.length,
        itemBuilder: (context, index) {
          final record = controller.maintenanceRecords[index];
          return Card(
            margin: const EdgeInsets.symmetric(vertical: 4),
            child: ListTile(
              leading: CircleAvatar(
                backgroundColor: _getMaintenanceResultColor(record.result),
                child: Icon(
                  _getMaintenanceResultIcon(record.result),
                  color: Colors.white,
                ),
              ),
              title: Text(record.workDescription),
              subtitle: Text(
                'المدة: ${record.durationHours.toStringAsFixed(1)} ساعة\n'
                'التكلفة: ${record.actualCost.toStringAsFixed(2)} ريال',
              ),
              trailing: Text(
                DateFormat('yyyy-MM-dd').format(record.startTime),
                style: const TextStyle(fontSize: 12),
              ),
            ),
          );
        },
      );
    });
  }

  // طرق مساعدة للألوان والأيقونات
  Color _getPriorityColor(MaintenancePriority priority) {
    switch (priority) {
      case MaintenancePriority.low:
        return Colors.green;
      case MaintenancePriority.medium:
        return Colors.orange;
      case MaintenancePriority.high:
        return Colors.red;
      case MaintenancePriority.critical:
        return Colors.purple;
    }
  }

  Color _getMaintenanceStatusColor(MaintenanceStatus status) {
    switch (status) {
      case MaintenanceStatus.scheduled:
        return Colors.blue;
      case MaintenanceStatus.inProgress:
        return Colors.orange;
      case MaintenanceStatus.completed:
        return Colors.green;
      case MaintenanceStatus.cancelled:
        return Colors.grey;
      case MaintenanceStatus.postponed:
        return Colors.amber;
    }
  }

  IconData _getMaintenanceStatusIcon(MaintenanceStatus status) {
    switch (status) {
      case MaintenanceStatus.scheduled:
        return Icons.schedule;
      case MaintenanceStatus.inProgress:
        return Icons.build;
      case MaintenanceStatus.completed:
        return Icons.check;
      case MaintenanceStatus.cancelled:
        return Icons.cancel;
      case MaintenanceStatus.postponed:
        return Icons.pause;
    }
  }

  Color _getMaintenanceResultColor(MaintenanceResult result) {
    switch (result) {
      case MaintenanceResult.successful:
        return Colors.green;
      case MaintenanceResult.partial:
        return Colors.orange;
      case MaintenanceResult.failed:
        return Colors.red;
      case MaintenanceResult.postponed:
        return Colors.amber;
    }
  }

  IconData _getMaintenanceResultIcon(MaintenanceResult result) {
    switch (result) {
      case MaintenanceResult.successful:
        return Icons.check_circle;
      case MaintenanceResult.partial:
        return Icons.warning;
      case MaintenanceResult.failed:
        return Icons.error;
      case MaintenanceResult.postponed:
        return Icons.schedule;
    }
  }

  // نوافذ الحوار
  void _showDateRangePicker(MaintenanceController controller) async {
    final dateRange = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: controller.selectedDateRange.value,
    );

    if (dateRange != null) {
      controller.changeDateRange(dateRange);
    }
  }

  void _showAnalysisDialog(MaintenanceController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('تحليل الصيانة'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: Center(child: Text('سيتم تطوير نافذة التحليل')),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
        ],
      ),
    );
  }

  void _showFinishBreakdownDialog(
    MaintenanceController controller,
    Breakdown breakdown,
  ) {
    Get.dialog(
      AlertDialog(
        title: const Text('إنهاء العطل'),
        content: const SizedBox(
          width: 400,
          height: 200,
          child: Center(child: Text('نموذج إنهاء العطل')),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              controller.finishBreakdown(breakdown.id!);
              Get.back();
            },
            child: const Text('إنهاء'),
          ),
        ],
      ),
    );
  }

  void _showCompleteMaintenanceDialog(
    MaintenanceController controller,
    Maintenance maintenance,
  ) {
    Get.dialog(
      AlertDialog(
        title: const Text('تنفيذ الصيانة'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: Center(child: Text('نموذج تنفيذ الصيانة')),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              // سيتم تطوير منطق تنفيذ الصيانة
              Get.back();
            },
            child: const Text('تنفيذ'),
          ),
        ],
      ),
    );
  }
}
