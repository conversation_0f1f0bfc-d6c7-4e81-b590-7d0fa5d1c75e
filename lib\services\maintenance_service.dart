import 'package:mostafa_final/data/models/maintenance.dart';
import 'package:mostafa_final/data/repositories/maintenance_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';

class MaintenanceService {
  final MaintenanceRepository _maintenanceRepository = MaintenanceRepository();
  final MachineRepository _machineRepository = MachineRepository();

  /// تحليل الأعطال وإنشاء توصيات للصيانة الوقائية
  Future<Map<String, dynamic>>
  analyzeBreakdownsAndRecommendMaintenance() async {
    // الحصول على تحليل الأعطال
    final breakdownAnalysis =
        await _maintenanceRepository.getBreakdownAnalysis();

    // تحليل الأعطال المتكررة
    final recommendations = <Map<String, dynamic>>[];

    // تحليل الأعطال حسب الماكينة
    final machineAnalysis =
        breakdownAnalysis['machineAnalysis'] as List<Map<String, dynamic>>;

    for (var analysis in machineAnalysis) {
      final machineId = analysis['machineId'] as String;
      final breakdownCount = analysis['count'] as int;
      final avgDowntime = (analysis['avgDowntime'] as num?)?.toDouble() ?? 0.0;
      final totalDowntime =
          (analysis['totalDowntime'] as num?)?.toDouble() ?? 0.0;

      // إذا كانت الماكينة تعاني من أعطال متكررة
      if (breakdownCount >= 3 || totalDowntime >= 24) {
        final machine = await _machineRepository.getMachineById(machineId);

        recommendations.add({
          'machineId': machineId,
          'machineName': machine?.name ?? 'غير معروف',
          'breakdownCount': breakdownCount,
          'avgDowntime': avgDowntime,
          'totalDowntime': totalDowntime,
          'recommendationType': _getRecommendationType(
            breakdownCount,
            totalDowntime,
          ),
          'priority': _calculatePriority(breakdownCount, totalDowntime),
          'suggestedActions': _getSuggestedActions(breakdownCount, avgDowntime),
        });
      }
    }

    // ترتيب التوصيات حسب الأولوية
    recommendations.sort(
      (a, b) => (b['priority'] as int).compareTo(a['priority'] as int),
    );

    return {
      'breakdownAnalysis': breakdownAnalysis,
      'recommendations': recommendations,
      'summary': {
        'totalMachinesAnalyzed': machineAnalysis.length,
        'machinesNeedingAttention': recommendations.length,
        'highPriorityMachines':
            recommendations.where((r) => r['priority'] >= 8).length,
      },
    };
  }

  /// تحديد نوع التوصية بناءً على تحليل الأعطال
  String _getRecommendationType(int breakdownCount, double totalDowntime) {
    if (breakdownCount >= 5 || totalDowntime >= 48) {
      return 'صيانة شاملة عاجلة';
    } else if (breakdownCount >= 3 || totalDowntime >= 24) {
      return 'صيانة وقائية مكثفة';
    } else {
      return 'صيانة وقائية منتظمة';
    }
  }

  /// حساب أولوية الصيانة (1-10)
  int _calculatePriority(int breakdownCount, double totalDowntime) {
    int priority = 1;

    // زيادة الأولوية بناءً على عدد الأعطال
    priority += (breakdownCount * 1.5).round();

    // زيادة الأولوية بناءً على ساعات التوقف
    priority += (totalDowntime / 12).round();

    // التأكد من أن الأولوية لا تتجاوز 10
    return priority > 10 ? 10 : priority;
  }

  /// الحصول على الإجراءات المقترحة
  List<String> _getSuggestedActions(int breakdownCount, double avgDowntime) {
    final actions = <String>[];

    if (breakdownCount >= 5) {
      actions.add('فحص شامل للماكينة');
      actions.add('استبدال القطع المتآكلة');
    }

    if (avgDowntime >= 4) {
      actions.add('تحسين إجراءات الصيانة');
      actions.add('تدريب المشغلين');
    }

    if (breakdownCount >= 3) {
      actions.add('زيادة تكرار الصيانة الوقائية');
      actions.add('مراقبة مؤشرات الأداء');
    }

    if (actions.isEmpty) {
      actions.add('صيانة وقائية منتظمة');
    }

    return actions;
  }

  /// إنشاء جدولة صيانة وقائية تلقائية
  Future<List<Maintenance>> generatePreventiveMaintenanceSchedule(
    String machineId,
  ) async {
    final machine = await _machineRepository.getMachineById(machineId);
    if (machine == null) return [];

    final maintenanceSchedule = <Maintenance>[];
    final now = DateTime.now();

    // صيانة يومية
    maintenanceSchedule.add(
      Maintenance(
        machineId: machineId,
        type: MaintenanceType.routine,
        title: 'فحص يومي - ${machine.name}',
        description: 'فحص عام للماكينة والتأكد من سلامة التشغيل',
        frequency: MaintenanceFrequency.days,
        intervalValue: 1,
        nextDue: now.add(const Duration(days: 1)),
        status: MaintenanceStatus.scheduled,
        priority: MaintenancePriority.low,
        estimatedDuration: 0.5,
        estimatedCost: 50.0,
        instructions:
            'فحص مستوى الزيت، تنظيف الماكينة، فحص الأصوات غير الطبيعية',
        isActive: true,
        createdAt: now,
      ),
    );

    // صيانة أسبوعية
    maintenanceSchedule.add(
      Maintenance(
        machineId: machineId,
        type: MaintenanceType.preventive,
        title: 'صيانة أسبوعية - ${machine.name}',
        description: 'صيانة وقائية أسبوعية شاملة',
        frequency: MaintenanceFrequency.weeks,
        intervalValue: 1,
        nextDue: now.add(const Duration(days: 7)),
        status: MaintenanceStatus.scheduled,
        priority: MaintenancePriority.medium,
        estimatedDuration: 2.0,
        estimatedCost: 200.0,
        requiredParts: ['فلاتر', 'زيوت'],
        instructions: 'تغيير الفلاتر، فحص الأحزمة، تشحيم المحامل',
        isActive: true,
        createdAt: now,
      ),
    );

    // صيانة شهرية
    maintenanceSchedule.add(
      Maintenance(
        machineId: machineId,
        type: MaintenanceType.preventive,
        title: 'صيانة شهرية - ${machine.name}',
        description: 'صيانة وقائية شهرية متقدمة',
        frequency: MaintenanceFrequency.months,
        intervalValue: 1,
        nextDue: now.add(const Duration(days: 30)),
        status: MaintenanceStatus.scheduled,
        priority: MaintenancePriority.high,
        estimatedDuration: 4.0,
        estimatedCost: 500.0,
        requiredParts: ['قطع غيار متنوعة', 'زيوت خاصة'],
        requiredTools: ['مفاتيح خاصة', 'أجهزة قياس'],
        instructions: 'فحص شامل للمكونات، معايرة الأجهزة، اختبار الأداء',
        isActive: true,
        createdAt: now,
      ),
    );

    // صيانة بناءً على ساعات التشغيل (كل 100 ساعة)
    maintenanceSchedule.add(
      Maintenance(
        machineId: machineId,
        type: MaintenanceType.preventive,
        title: 'صيانة بناءً على ساعات التشغيل - ${machine.name}',
        description: 'صيانة وقائية بناءً على ساعات التشغيل الفعلية',
        frequency: MaintenanceFrequency.hours,
        intervalValue: 100,
        nextDue: now.add(const Duration(days: 14)), // تقدير: 100 ساعة = 14 يوم
        status: MaintenanceStatus.scheduled,
        priority: MaintenancePriority.high,
        estimatedDuration: 3.0,
        estimatedCost: 400.0,
        requiredParts: ['فلاتر متقدمة', 'زيوت هيدروليكية'],
        instructions:
            'فحص النظام الهيدروليكي، تنظيف المبردات، فحص الدوائر الكهربائية',
        isActive: true,
        createdAt: now,
      ),
    );

    return maintenanceSchedule;
  }

  /// تحليل كفاءة الصيانة
  Future<Map<String, dynamic>> analyzeMaintenanceEffectiveness() async {
    final stats = await _maintenanceRepository.getMaintenanceStats();
    final breakdownAnalysis =
        await _maintenanceRepository.getBreakdownAnalysis();

    final generalStats =
        breakdownAnalysis['generalStats'] as Map<String, dynamic>;
    final recordStats = stats['recordStats'] as Map<String, dynamic>;

    // حساب مؤشرات الكفاءة
    final totalBreakdowns =
        (generalStats['totalBreakdowns'] as num?)?.toInt() ?? 0;
    final totalDowntime =
        (generalStats['totalDowntime'] as num?)?.toDouble() ?? 0.0;
    // final totalMaintenanceRecords =
    //     (recordStats['totalRecords'] as num?)?.toInt() ?? 0;
    final totalMaintenanceCost =
        (recordStats['totalCost'] as num?)?.toDouble() ?? 0.0;

    // حساب متوسط الوقت بين الأعطال (MTBF)
    final mtbf =
        totalBreakdowns > 0
            ? (30 * 24) / totalBreakdowns
            : 0.0; // افتراض 30 يوم

    // حساب متوسط وقت الإصلاح (MTTR)
    final mttr = totalBreakdowns > 0 ? totalDowntime / totalBreakdowns : 0.0;

    // حساب معدل توفر الماكينات
    final availability =
        totalDowntime > 0
            ? ((30 * 24 - totalDowntime) / (30 * 24)) * 100
            : 100.0;

    return {
      'mtbf': mtbf, // Mean Time Between Failures
      'mttr': mttr, // Mean Time To Repair
      'availability': availability, // نسبة التوفر
      'totalMaintenanceCost': totalMaintenanceCost,
      'costPerBreakdown':
          totalBreakdowns > 0 ? totalMaintenanceCost / totalBreakdowns : 0.0,
      'maintenanceEfficiency': _calculateMaintenanceEfficiency(
        mtbf,
        mttr,
        availability,
      ),
      'recommendations': _getEfficiencyRecommendations(
        mtbf,
        mttr,
        availability,
      ),
    };
  }

  /// حساب كفاءة الصيانة الإجمالية
  double _calculateMaintenanceEfficiency(
    double mtbf,
    double mttr,
    double availability,
  ) {
    // معادلة مبسطة لحساب كفاءة الصيانة
    double efficiency = 0.0;

    // وزن MTBF (40%)
    efficiency += (mtbf / 168) * 40; // 168 ساعة = أسبوع

    // وزن MTTR (30%) - كلما قل كان أفضل
    efficiency += (1 - (mttr / 24)) * 30; // 24 ساعة كحد أقصى

    // وزن التوفر (30%)
    efficiency += (availability / 100) * 30;

    return efficiency > 100 ? 100 : (efficiency < 0 ? 0 : efficiency);
  }

  /// الحصول على توصيات تحسين الكفاءة
  List<String> _getEfficiencyRecommendations(
    double mtbf,
    double mttr,
    double availability,
  ) {
    final recommendations = <String>[];

    if (mtbf < 72) {
      // أقل من 3 أيام
      recommendations.add('زيادة تكرار الصيانة الوقائية');
      recommendations.add('تحسين جودة قطع الغيار');
    }

    if (mttr > 4) {
      // أكثر من 4 ساعات
      recommendations.add('تدريب فريق الصيانة');
      recommendations.add('تحسين توفر قطع الغيار');
      recommendations.add('تطوير إجراءات الإصلاح');
    }

    if (availability < 90) {
      recommendations.add('تحليل أسباب التوقف الرئيسية');
      recommendations.add('تطبيق الصيانة التنبؤية');
    }

    if (recommendations.isEmpty) {
      recommendations.add('الحفاظ على الأداء الحالي الممتاز');
    }

    return recommendations;
  }

  /// إنشاء تنبيهات الصيانة المستحقة
  Future<List<Map<String, dynamic>>> generateMaintenanceAlerts() async {
    final dueMaintenance = await _maintenanceRepository.getDueMaintenance();
    final alerts = <Map<String, dynamic>>[];

    for (var maintenance in dueMaintenance) {
      final machine = await _machineRepository.getMachineById(
        maintenance.machineId,
      );

      alerts.add({
        'id': maintenance.id,
        'machineId': maintenance.machineId,
        'machineName': machine?.name ?? 'غير معروف',
        'maintenanceTitle': maintenance.title,
        'dueDate': maintenance.nextDue,
        'priority': maintenance.priority,
        'isOverdue': maintenance.isOverdue,
        'daysUntilDue': maintenance.daysUntilDue,
        'estimatedDuration': maintenance.estimatedDuration,
        'estimatedCost': maintenance.estimatedCost,
      });
    }

    // ترتيب التنبيهات: المتأخرة أولاً، ثم حسب الأولوية
    alerts.sort((a, b) {
      final aOverdue = a['isOverdue'] as bool;
      final bOverdue = b['isOverdue'] as bool;

      if (aOverdue && !bOverdue) return -1;
      if (!aOverdue && bOverdue) return 1;

      final aPriority = (a['priority'] as MaintenancePriority).index;
      final bPriority = (b['priority'] as MaintenancePriority).index;

      return bPriority.compareTo(aPriority);
    });

    return alerts;
  }
}
