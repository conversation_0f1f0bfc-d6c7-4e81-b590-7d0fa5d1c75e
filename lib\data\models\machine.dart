class Machine {
  final String? id;
  final String name;
  final String model;
  final double powerConsumption; // استهلاك الطاقة بالكيلوواط
  final String status; // حالة الماكينة (تعمل، متوقفة، صيانة)
  final String? currentMoldId; // الإسطمبة المركبة حالياً
  final DateTime? lastMoldChange; // آخر تغيير للإسطمبة

  Machine({
    this.id,
    required this.name,
    required this.model,
    required this.powerConsumption,
    required this.status,
    this.currentMoldId,
    this.lastMoldChange,
  });

  // تحويل Machine إلى Map<String, dynamic> لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'model': model,
      'powerConsumption': powerConsumption,
      'status': status,
      'currentMoldId': currentMoldId,
      'lastMoldChange': lastMoldChange?.toIso8601String(),
    };
  }

  // إنشاء Machine من Map<String, dynamic> المستخرج من قاعدة البيانات
  factory Machine.fromMap(Map<String, dynamic> map) {
    return Machine(
      id: map['id'],
      name: map['name'],
      model: map['model'],
      powerConsumption: map['powerConsumption'],
      status: map['status'],
      currentMoldId: map['currentMoldId'],
      lastMoldChange:
          map['lastMoldChange'] != null
              ? DateTime.parse(map['lastMoldChange'])
              : null,
    );
  }

  // إنشاء نسخة معدلة من Machine
  Machine copyWith({
    String? id,
    String? name,
    String? model,
    double? powerConsumption,
    String? status,
    String? currentMoldId,
    DateTime? lastMoldChange,
  }) {
    return Machine(
      id: id ?? this.id,
      name: name ?? this.name,
      model: model ?? this.model,
      powerConsumption: powerConsumption ?? this.powerConsumption,
      status: status ?? this.status,
      currentMoldId: currentMoldId ?? this.currentMoldId,
      lastMoldChange: lastMoldChange ?? this.lastMoldChange,
    );
  }
}

// حالات الماكينة
class MachineStatus {
  static const String operating = 'تعمل';
  static const String stopped = 'متوقفة';
  static const String maintenance = 'صيانة';
}
