import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// بطاقة متجاوبة مع حجم الشاشة
class ResponsiveCard extends StatelessWidget {
  final Widget? child;
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final Color? color;
  final double? elevation;
  final BorderRadius? borderRadius;
  final bool hasShadow;

  const ResponsiveCard({
    super.key,
    this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
    this.color,
    this.elevation,
    this.borderRadius,
    this.hasShadow = true,
  });

  @override
  Widget build(BuildContext context) {
    // تطبيق التنسيق المتجاوب بناءً على حجم الشاشة
    final EdgeInsetsGeometry responsivePadding =
        padding != null
            ? EdgeInsets.only(
              left: ScreenSize.getPadding((padding as EdgeInsets).left),
              top: ScreenSize.getPadding((padding as EdgeInsets).top),
              right: ScreenSize.getPadding((padding as EdgeInsets).right),
              bottom: ScreenSize.getPadding((padding as EdgeInsets).bottom),
            )
            : EdgeInsets.all(ScreenSize.getPadding(16));

    final EdgeInsetsGeometry responsiveMargin =
        margin != null
            ? EdgeInsets.only(
              left: ScreenSize.getMargin((margin as EdgeInsets).left),
              top: ScreenSize.getMargin((margin as EdgeInsets).top),
              right: ScreenSize.getMargin((margin as EdgeInsets).right),
              bottom: ScreenSize.getMargin((margin as EdgeInsets).bottom),
            )
            : EdgeInsets.all(ScreenSize.getMargin(8));

    return Card(
      elevation: hasShadow ? (elevation ?? 2) : 0,
      margin: responsiveMargin,
      color: color,
      shape: RoundedRectangleBorder(
        borderRadius: borderRadius ?? BorderRadius.circular(8),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(8),
        child: Padding(padding: responsivePadding, child: _buildCardContent()),
      ),
    );
  }

  Widget _buildCardContent() {
    if (title != null || subtitle != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || leading != null || trailing != null)
            Row(
              children: [
                if (leading != null) ...[
                  leading!,
                  SizedBox(width: ScreenSize.getPadding(8)),
                ],
                if (title != null)
                  Expanded(
                    child: TextUtils.responsiveTitle(title!, maxLines: 1),
                  ),
                if (trailing != null) trailing!,
              ],
            ),
          if (title != null && subtitle != null)
            SizedBox(height: ScreenSize.getPadding(8)),
          if (subtitle != null)
            TextUtils.responsiveSubtitle(subtitle!, maxLines: 2),
          if (child != null) ...[
            SizedBox(height: ScreenSize.getPadding(12)),
            child!,
          ],
        ],
      );
    } else {
      return child ?? const SizedBox.shrink();
    }
  }
}

/// امتداد للتعامل مع البطاقات المتجاوبة
extension ResponsiveCardExtension on Widget {
  /// تغليف العنصر في بطاقة متجاوبة
  Widget responsiveCard({
    String? title,
    String? subtitle,
    Widget? leading,
    Widget? trailing,
    VoidCallback? onTap,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Color? color,
    double? elevation,
    BorderRadius? borderRadius,
    bool hasShadow = true,
  }) {
    return ResponsiveCard(
      title: title,
      subtitle: subtitle,
      leading: leading,
      trailing: trailing,
      onTap: onTap,
      padding: padding,
      margin: margin,
      color: color,
      elevation: elevation,
      borderRadius: borderRadius,
      hasShadow: hasShadow,
      child: this,
    );
  }
}
