import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:uuid/uuid.dart';

class RawMaterialRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء مادة خام جديدة
  Future<String> createRawMaterial(RawMaterial rawMaterial) async {
    final rawMaterialWithId = RawMaterial(
      id: _uuid.v4(),
      name: rawMaterial.name,
      code: rawMaterial.code,
      availableQuantity: rawMaterial.availableQuantity,
      costPerKg: rawMaterial.costPerKg,
      color: rawMaterial.color,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableRawMaterials,
      rawMaterialWithId.toMap(),
    );
  }

  // الحصول على جميع المواد الخام
  Future<List<RawMaterial>> getAllRawMaterials() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableRawMaterials,
    );

    return maps.map((map) => RawMaterial.fromMap(map)).toList();
  }

  // الحصول على مادة خام بمعرف معين
  Future<RawMaterial?> getRawMaterialById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableRawMaterials,
      id,
    );

    if (map == null) {
      return null;
    }

    return RawMaterial.fromMap(map);
  }

  // تحديث مادة خام
  Future<int> updateRawMaterial(RawMaterial rawMaterial) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableRawMaterials,
      rawMaterial.toMap(),
    );
  }

  // حذف مادة خام
  Future<int> deleteRawMaterial(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableRawMaterials, id);
  }

  // تعديل كمية المادة الخام (خصم أو إضافة)
  Future<int> updateRawMaterialQuantity(
    String id,
    double quantityChange,
  ) async {
    RawMaterial? rawMaterial = await getRawMaterialById(id);

    if (rawMaterial == null) {
      throw Exception("المادة الخام غير موجودة");
    }

    double newQuantity = rawMaterial.availableQuantity + quantityChange;

    if (newQuantity < 0) {
      throw Exception("لا يمكن أن تكون الكمية المتاحة سالبة");
    }

    RawMaterial updatedRawMaterial = rawMaterial.copyWith(
      availableQuantity: newQuantity,
    );

    return await updateRawMaterial(updatedRawMaterial);
  }

  // إضافة استلام مادة خام
  Future<String> addRawMaterialReceipt(RawMaterialReceipt receipt) async {
    final receiptWithId = RawMaterialReceipt(
      id: _uuid.v4(),
      rawMaterialId: receipt.rawMaterialId,
      date: receipt.date,
      quantity: receipt.quantity,
      pricePerKg: receipt.pricePerKg,
    );

    // إضافة الاستلام إلى قاعدة البيانات
    String receiptId = await _databaseHelper.insert(
      DatabaseHelper.tableRawMaterialReceipts,
      receiptWithId.toMap(),
    );

    // تحديث كمية المادة الخام
    await updateRawMaterialQuantity(receipt.rawMaterialId, receipt.quantity);

    // تحديث متوسط سعر التكلفة
    await _updateAverageCost(receipt.rawMaterialId);

    return receiptId;
  }

  // تحديث متوسط تكلفة المادة الخام بناءً على عمليات الاستلام
  Future<void> _updateAverageCost(String rawMaterialId) async {
    String query = '''
      SELECT SUM(quantity) as totalQuantity, SUM(quantity * pricePerKg) as totalCost
      FROM ${DatabaseHelper.tableRawMaterialReceipts}
      WHERE rawMaterialId = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      rawMaterialId,
    ]);

    if (result.isEmpty ||
        result[0]['totalQuantity'] == null ||
        result[0]['totalQuantity'] == 0) {
      return;
    }

    double totalQuantity = result[0]['totalQuantity'];
    double totalCost = result[0]['totalCost'];

    double averageCost = totalCost / totalQuantity;

    RawMaterial? rawMaterial = await getRawMaterialById(rawMaterialId);

    if (rawMaterial != null) {
      RawMaterial updatedRawMaterial = rawMaterial.copyWith(
        costPerKg: averageCost,
      );

      await updateRawMaterial(updatedRawMaterial);
    }
  }

  // الحصول على سجل استلام المواد الخام لمادة معينة
  Future<List<RawMaterialReceipt>> getRawMaterialReceipts(
    String rawMaterialId,
  ) async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableRawMaterialReceipts}
      WHERE rawMaterialId = ?
      ORDER BY date DESC
    ''';

    List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(query, [
      rawMaterialId,
    ]);

    return maps.map((map) => RawMaterialReceipt.fromMap(map)).toList();
  }

  // الحصول على إحصائيات المواد الخام
  Future<Map<String, dynamic>> getRawMaterialsStatistics() async {
    // الحصول على إجمالي تكلفة المواد الخام
    String costQuery = '''
      SELECT SUM(availableQuantity * costPerKg) as totalCost
      FROM ${DatabaseHelper.tableRawMaterials}
    ''';

    List<Map<String, dynamic>> costResult = await _databaseHelper.rawQuery(
      costQuery,
    );
    double totalCost =
        costResult.isNotEmpty && costResult[0]['totalCost'] != null
            ? costResult[0]['totalCost']
            : 0.0;

    // الحصول على إجمالي الكمية المتاحة
    String quantityQuery = '''
      SELECT SUM(availableQuantity) as totalQuantity
      FROM ${DatabaseHelper.tableRawMaterials}
    ''';

    List<Map<String, dynamic>> quantityResult = await _databaseHelper.rawQuery(
      quantityQuery,
    );
    double totalQuantity =
        quantityResult.isNotEmpty && quantityResult[0]['totalQuantity'] != null
            ? quantityResult[0]['totalQuantity']
            : 0.0;

    // الحصول على عدد أصناف المواد الخام
    String countQuery = '''
      SELECT COUNT(id) as materialCount
      FROM ${DatabaseHelper.tableRawMaterials}
    ''';

    List<Map<String, dynamic>> countResult = await _databaseHelper.rawQuery(
      countQuery,
    );
    int materialCount =
        countResult.isNotEmpty && countResult[0]['materialCount'] != null
            ? countResult[0]['materialCount']
            : 0;

    return {
      'totalCost': totalCost,
      'totalQuantity': totalQuantity,
      'materialCount': materialCount,
    };
  }
}
