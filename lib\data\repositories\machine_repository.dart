import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/mold_change_history.dart';
import 'package:mostafa_final/data/repositories/mold_change_history_repository.dart';
import 'package:uuid/uuid.dart';

class MachineRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final MoldChangeHistoryRepository _historyRepository =
      MoldChangeHistoryRepository();
  final _uuid = const Uuid();

  // إنشاء ماكينة جديدة
  Future<String> createMachine(Machine machine) async {
    final machineWithId = Machine(
      id: _uuid.v4(),
      name: machine.name,
      model: machine.model,
      powerConsumption: machine.powerConsumption,
      status: machine.status,
      currentMoldId: machine.currentMoldId,
      lastMoldChange: machine.lastMoldChange,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableMachines,
      machineWithId.toMap(),
    );
  }

  // الحصول على جميع الماكينات
  Future<List<Machine>> getAllMachines() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableMachines,
    );

    return maps.map((map) => Machine.fromMap(map)).toList();
  }

  // الحصول على ماكينة بمعرف معين
  Future<Machine?> getMachineById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableMachines,
      id,
    );

    if (map == null) {
      return null;
    }

    return Machine.fromMap(map);
  }

  // تحديث ماكينة
  Future<int> updateMachine(Machine machine) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableMachines,
      machine.toMap(),
    );
  }

  // حذف ماكينة
  Future<int> deleteMachine(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableMachines, id);
  }

  // تغيير الإسطمبة المركبة على الماكينة
  Future<void> changeMold(
    String machineId,
    String moldId, {
    String? notes,
  }) async {
    // 1. الحصول على الماكينة والإسطمبة
    final Machine? machine = await getMachineById(machineId);

    if (machine == null) {
      throw Exception("الماكينة غير موجودة");
    }

    // 2. التحقق مما إذا كانت الإسطمبة مستخدمة في ماكينة أخرى
    String checkMoldQuery = '''
      SELECT * FROM ${DatabaseHelper.tableMolds}
      WHERE id = ? AND (currentMachineId IS NULL OR currentMachineId = ?)
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(
      checkMoldQuery,
      [moldId, machineId],
    );

    if (result.isEmpty) {
      throw Exception("الإسطمبة غير متاحة أو مستخدمة في ماكينة أخرى");
    }

    // حفظ معرف الإسطمبة السابقة للتاريخ
    final String? previousMoldId = machine.currentMoldId;

    // 3. تحرير الإسطمبة الحالية إذا وجدت
    if (previousMoldId != null) {
      String releaseMoldQuery = '''
        UPDATE ${DatabaseHelper.tableMolds}
        SET status = ?, currentMachineId = NULL
        WHERE id = ?
      ''';

      await _databaseHelper.rawQuery(releaseMoldQuery, [
        MoldStatus.available,
        previousMoldId,
      ]);
    }

    // تاريخ التغيير
    final DateTime changeDate = DateTime.now();

    // 4. تحديث الماكينة بالإسطمبة الجديدة
    final updatedMachine = machine.copyWith(
      currentMoldId: moldId,
      lastMoldChange: changeDate,
    );

    await updateMachine(updatedMachine);

    // 5. تحديث حالة الإسطمبة الجديدة
    String updateMoldQuery = '''
      UPDATE ${DatabaseHelper.tableMolds}
      SET status = ?, currentMachineId = ?
      WHERE id = ?
    ''';

    await _databaseHelper.rawQuery(updateMoldQuery, [
      MoldStatus.inUse,
      machineId,
      moldId,
    ]);

    // 6. تسجيل عملية التغيير في تاريخ التغييرات
    final MoldChangeHistory history = MoldChangeHistory(
      machineId: machineId,
      moldId: moldId,
      changeDate: changeDate,
      previousMoldId: previousMoldId,
      notes: notes,
    );

    await _historyRepository.addMoldChangeRecord(history);
  }

  // الحصول على الماكينات مع الإسطمبات المركبة عليها
  Future<List<Map<String, dynamic>>> getMachinesWithMolds() async {
    String query = '''
      SELECT m.*, mold.name as moldName, mold.productCode
      FROM ${DatabaseHelper.tableMachines} m
      LEFT JOIN ${DatabaseHelper.tableMolds} mold ON m.currentMoldId = mold.id
    ''';

    return await _databaseHelper.rawQuery(query);
  }
}
