import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/presentation/controllers/raw_material_report_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class RawMaterialReportPage extends StatelessWidget {
  const RawMaterialReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(RawMaterialReportController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير المواد الخام'),
        actions: [
          IconButton(
            icon: const Icon(Icons.print),
            tooltip: 'طباعة التقرير',
            onPressed: controller.generateReport,
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const LoadingIndicator()
                : Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildReportTypeSelector(controller),
                      const SizedBox(height: 16),
                      _buildDateRangeSelector(context, controller),
                      const SizedBox(height: 16),
                      const Divider(),
                      const SizedBox(height: 16),
                      Obx(
                        () =>
                            controller.isLoadingData.value
                                ? const Center(
                                  child: CircularProgressIndicator(),
                                )
                                : _buildReportContent(controller),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  Widget _buildReportTypeSelector(RawMaterialReportController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نوع التقرير',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Obx(
          () => Wrap(
            spacing: 8,
            children: [
              _buildReportTypeChip(
                controller,
                ReportType.consumption,
                'الاستهلاك',
                Icons.trending_down,
              ),
              _buildReportTypeChip(
                controller,
                ReportType.receipt,
                'الاستلام',
                Icons.trending_up,
              ),
              _buildReportTypeChip(
                controller,
                ReportType.inventory,
                'المخزون',
                Icons.inventory,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReportTypeChip(
    RawMaterialReportController controller,
    ReportType type,
    String label,
    IconData icon,
  ) {
    return FilterChip(
      label: Text(label),
      avatar: Icon(icon, size: 18),
      selected: controller.selectedReportType.value == type,
      onSelected: (bool selected) {
        if (selected) {
          controller.selectedReportType.value = type;
          controller.loadReportData();
        }
      },
    );
  }

  Widget _buildDateRangeSelector(
    BuildContext context,
    RawMaterialReportController controller,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'نطاق التقرير',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: _buildDateSelector(
                context,
                controller,
                'من: ${DateFormat('yyyy/MM/dd').format(controller.startDate.value)}',
                () => controller.selectStartDate(context),
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildDateSelector(
                context,
                controller,
                'إلى: ${DateFormat('yyyy/MM/dd').format(controller.endDate.value)}',
                () => controller.selectEndDate(context),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            _buildDateRangeChip(
              'الشهر الحالي',
              Icons.date_range,
              controller.setDateRangeThisMonth,
            ),
            _buildDateRangeChip(
              'الشهر السابق',
              Icons.history,
              controller.setDateRangeLastMonth,
            ),
            _buildDateRangeChip(
              'السنة الحالية',
              Icons.calendar_today,
              controller.setDateRangeThisYear,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateSelector(
    BuildContext context,
    RawMaterialReportController controller,
    String label,
    Function() onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), const Icon(Icons.calendar_today, size: 16)],
        ),
      ),
    );
  }

  Widget _buildDateRangeChip(String label, IconData icon, Function() onTap) {
    return ActionChip(
      label: Text(label),
      avatar: Icon(icon, size: 16),
      onPressed: onTap,
    );
  }

  Widget _buildReportContent(RawMaterialReportController controller) {
    switch (controller.selectedReportType.value) {
      case ReportType.consumption:
        return _buildConsumptionReport(controller);
      case ReportType.receipt:
        return _buildReceiptReport(controller);
      case ReportType.inventory:
        return _buildInventoryReport(controller);
    }
  }

  Widget _buildConsumptionReport(RawMaterialReportController controller) {
    if (controller.consumptionData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات استهلاك في هذه الفترة',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConsumptionSummary(controller),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: controller.consumptionData.length,
              itemBuilder: (context, index) {
                final item = controller.consumptionData[index];
                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  elevation: 2,
                  child: ListTile(
                    title: Text(item['itemName'] ?? 'غير معروف'),
                    subtitle: Text('الكود: ${item['itemCode'] ?? '-'}'),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${(item['totalOut'] ?? 0.0).toStringAsFixed(2)} كجم',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${(item['totalCost'] ?? 0.0).toStringAsFixed(2)} جنيه',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildConsumptionSummary(RawMaterialReportController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              const Text(
                'إجمالي الاستهلاك',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.getTotalConsumption().toStringAsFixed(2)} كجم',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Column(
            children: [
              const Text(
                'إجمالي التكلفة',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.getTotalConsumptionCost().toStringAsFixed(2)} جنيه',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptReport(RawMaterialReportController controller) {
    if (controller.receiptData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات استلام في هذه الفترة',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildReceiptSummary(controller),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: controller.receiptData.length,
              itemBuilder: (context, index) {
                final item = controller.receiptData[index];
                final double quantity = item['quantity'] ?? 0.0;
                final double pricePerKg = item['pricePerKg'] ?? 0.0;
                final DateTime date = DateTime.parse(item['date']);

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  elevation: 2,
                  child: ListTile(
                    title: Text('${item['materialName'] ?? 'غير معروف'}'),
                    subtitle: Text(
                      'التاريخ: ${DateFormat('yyyy/MM/dd').format(date)}\n'
                      'الكود: ${item['code'] ?? '-'}',
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${quantity.toStringAsFixed(2)} كجم',
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          '${pricePerKg.toStringAsFixed(2)} جنيه/كجم',
                          style: TextStyle(
                            color: Colors.green.shade700,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${(quantity * pricePerKg).toStringAsFixed(2)} جنيه',
                          style: TextStyle(
                            color: Colors.red.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReceiptSummary(RawMaterialReportController controller) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.green.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              const Text(
                'إجمالي الاستلام',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.getTotalReceipt().toStringAsFixed(2)} كجم',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Column(
            children: [
              const Text(
                'إجمالي التكلفة',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.getTotalReceiptCost().toStringAsFixed(2)} جنيه',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.red.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryReport(RawMaterialReportController controller) {
    if (controller.inventoryData.isEmpty) {
      return const Center(
        child: Text(
          'لا توجد بيانات مخزون',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
      );
    }

    // حساب إجمالي قيمة المخزون
    double totalValue = 0.0;
    for (final item in controller.inventoryData) {
      double quantity = item['availableQuantity'] ?? 0.0;
      double costPerKg = item['costPerKg'] ?? 0.0;
      totalValue += quantity * costPerKg;
    }

    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildInventorySummary(controller, totalValue),
          const SizedBox(height: 16),
          Expanded(
            child: ListView.builder(
              itemCount: controller.inventoryData.length,
              itemBuilder: (context, index) {
                final item = controller.inventoryData[index];
                final double quantity = item['availableQuantity'] ?? 0.0;
                final double costPerKg = item['costPerKg'] ?? 0.0;
                final double totalItemValue = quantity * costPerKg;

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  elevation: 2,
                  child: ListTile(
                    title: Text('${item['name'] ?? 'غير معروف'}'),
                    subtitle: Text(
                      'الكود: ${item['code'] ?? '-'}\n'
                      'اللون: ${item['color'] ?? '-'}',
                    ),
                    trailing: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          '${quantity.toStringAsFixed(2)} كجم',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color:
                                quantity < 10
                                    ? Colors.red.shade700
                                    : Colors.black,
                          ),
                        ),
                        Text(
                          '${costPerKg.toStringAsFixed(2)} جنيه/كجم',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontSize: 12,
                          ),
                        ),
                        Text(
                          '${totalItemValue.toStringAsFixed(2)} جنيه',
                          style: TextStyle(
                            color: Colors.purple.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                    isThreeLine: true,
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInventorySummary(
    RawMaterialReportController controller,
    double totalValue,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              const Text(
                'عدد المواد الخام',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${controller.inventoryData.length}',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          Column(
            children: [
              const Text(
                'قيمة المخزون الإجمالية',
                style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 4),
              Text(
                '${totalValue.toStringAsFixed(2)} جنيه',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
