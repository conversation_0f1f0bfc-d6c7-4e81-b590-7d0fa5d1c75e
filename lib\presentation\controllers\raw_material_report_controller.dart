import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/utils/pdf_generator.dart';

enum ReportType { consumption, receipt, inventory }

class RawMaterialReportController extends GetxController {
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();
  final InventoryRepository _inventoryRepository =
      Get.find<InventoryRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isLoadingData = false.obs;

  // نطاق التاريخ
  final Rx<DateTime> startDate =
      DateTime.now().subtract(const Duration(days: 30)).obs;
  final Rx<DateTime> endDate = DateTime.now().obs;

  // نوع التقرير المحدد
  final Rx<ReportType> selectedReportType = ReportType.consumption.obs;

  // بيانات التقارير
  final RxList<Map<String, dynamic>> consumptionData =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> receiptData = <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> inventoryData =
      <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    loadReportData();
  }

  // تحميل بيانات التقرير
  Future<void> loadReportData() async {
    isLoadingData.value = true;

    try {
      switch (selectedReportType.value) {
        case ReportType.consumption:
          await _loadConsumptionData();
          break;
        case ReportType.receipt:
          await _loadReceiptData();
          break;
        case ReportType.inventory:
          await _loadInventoryData();
          break;
      }
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل بيانات التقرير: $e');
    } finally {
      isLoadingData.value = false;
    }
  }

  // تحميل بيانات استهلاك المواد الخام
  Future<void> _loadConsumptionData() async {
    final data = await _inventoryRepository.getRawMaterialConsumptionReport(
      startDate.value,
      endDate.value,
    );
    consumptionData.assignAll(data);
  }

  // تحميل بيانات استلام المواد الخام
  Future<void> _loadReceiptData() async {
    final materials = await _rawMaterialRepository.getAllRawMaterials();
    final List<Map<String, dynamic>> receiptsData = [];

    for (final material in materials) {
      if (material.id == null) continue;

      final receipts = await _rawMaterialRepository.getRawMaterialReceipts(
        material.id!,
      );

      for (final receipt in receipts) {
        if (receipt.date.isBefore(startDate.value) ||
            receipt.date.isAfter(endDate.value)) {
          continue;
        }

        receiptsData.add({
          'id': receipt.id,
          'materialId': material.id,
          'materialName': material.name,
          'color': material.color,
          'code': material.code,
          'date': receipt.date.toIso8601String(),
          'quantity': receipt.quantity,
          'pricePerKg': receipt.pricePerKg,
        });
      }
    }

    // ترتيب البيانات حسب التاريخ (الأحدث أولاً)
    receiptsData.sort((a, b) {
      final DateTime dateA = DateTime.parse(a['date']);
      final DateTime dateB = DateTime.parse(b['date']);
      return dateB.compareTo(dateA);
    });

    receiptData.assignAll(receiptsData);
  }

  // تحميل بيانات مخزون المواد الخام
  Future<void> _loadInventoryData() async {
    final materials = await _rawMaterialRepository.getAllRawMaterials();
    final List<Map<String, dynamic>> data = [];

    for (final material in materials) {
      data.add(material.toMap());
    }

    // ترتيب البيانات حسب الكمية (الأقل أولاً)
    data.sort((a, b) {
      final double quantityA = a['availableQuantity'] ?? 0.0;
      final double quantityB = b['availableQuantity'] ?? 0.0;
      return quantityA.compareTo(quantityB);
    });

    inventoryData.assignAll(data);
  }

  // اختيار تاريخ البداية
  Future<void> selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: startDate.value,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != startDate.value) {
      startDate.value = picked;
      if (startDate.value.isAfter(endDate.value)) {
        endDate.value = startDate.value;
      }
      await loadReportData();
    }
  }

  // اختيار تاريخ النهاية
  Future<void> selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: endDate.value,
      firstDate: startDate.value,
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != endDate.value) {
      endDate.value = picked;
      await loadReportData();
    }
  }

  // تعيين نطاق الشهر الحالي
  void setDateRangeThisMonth() {
    final now = DateTime.now();
    startDate.value = DateTime(now.year, now.month, 1);
    endDate.value = DateTime(now.year, now.month + 1, 0);
    loadReportData();
  }

  // تعيين نطاق الشهر الماضي
  void setDateRangeLastMonth() {
    final now = DateTime.now();
    startDate.value = DateTime(now.year, now.month - 1, 1);
    endDate.value = DateTime(now.year, now.month, 0);
    loadReportData();
  }

  // تعيين نطاق السنة الحالية
  void setDateRangeThisYear() {
    final now = DateTime.now();
    startDate.value = DateTime(now.year, 1, 1);
    endDate.value = DateTime(now.year, 12, 31);
    loadReportData();
  }

  // تنسيق التاريخ
  String formatDate(DateTime date) {
    return DateFormat('yyyy/MM/dd').format(date);
  }

  // الحصول على إجمالي الاستهلاك
  double getTotalConsumption() {
    double total = 0.0;
    for (final item in consumptionData) {
      total += item['totalOut'] ?? 0.0;
    }
    return total;
  }

  // الحصول على إجمالي تكلفة الاستهلاك
  double getTotalConsumptionCost() {
    double total = 0.0;
    for (final item in consumptionData) {
      total += item['totalCost'] ?? 0.0;
    }
    return total;
  }

  // الحصول على إجمالي الاستلام
  double getTotalReceipt() {
    double total = 0.0;
    for (final item in receiptData) {
      total += item['quantity'] ?? 0.0;
    }
    return total;
  }

  // الحصول على إجمالي تكلفة الاستلام
  double getTotalReceiptCost() {
    double total = 0.0;
    for (final item in receiptData) {
      final double quantity = item['quantity'] ?? 0.0;
      final double pricePerKg = item['pricePerKg'] ?? 0.0;
      total += quantity * pricePerKg;
    }
    return total;
  }

  // إنشاء تقرير PDF
  Future<void> generateReport() async {
    try {
      String title = '';
      switch (selectedReportType.value) {
        case ReportType.consumption:
          title = 'تقرير استهلاك المواد الخام';
          await _generateConsumptionPdf(title);
          break;
        case ReportType.receipt:
          title = 'تقرير استلام المواد الخام';
          await _generateReceiptPdf(title);
          break;
        case ReportType.inventory:
          title = 'تقرير مخزون المواد الخام';
          await _generateInventoryPdf(title);
          break;
      }
    } catch (e) {
      _showError('حدث خطأ أثناء إنشاء التقرير: $e');
    }
  }

  // إنشاء PDF لتقرير الاستهلاك
  Future<void> _generateConsumptionPdf(String title) async {
    final String dateRange =
        '${formatDate(startDate.value)} - ${formatDate(endDate.value)}';
    final String fullTitle = '$title ($dateRange)';

    final file = await PdfGenerator.generateRawMaterialConsumptionReport(
      consumptionData,
      fullTitle,
      startDate.value,
      endDate.value,
      getTotalConsumption(),
      getTotalConsumptionCost(),
    );

    // عرض رسالة نجاح
    _showReportSuccess(file);
  }

  // إنشاء PDF لتقرير الاستلام
  Future<void> _generateReceiptPdf(String title) async {
    final String dateRange =
        '${formatDate(startDate.value)} - ${formatDate(endDate.value)}';
    final String fullTitle = '$title ($dateRange)';

    final file = await PdfGenerator.generateRawMaterialReceiptReport(
      receiptData,
      fullTitle,
      startDate.value,
      endDate.value,
      getTotalReceipt(),
      getTotalReceiptCost(),
    );

    // عرض رسالة نجاح
    _showReportSuccess(file);
  }

  // إنشاء PDF لتقرير المخزون
  Future<void> _generateInventoryPdf(String title) async {
    final file = await PdfGenerator.generateRawMaterialInventoryReport(
      inventoryData,
      title,
    );

    // عرض رسالة نجاح
    _showReportSuccess(file);
  }

  // عرض رسالة نجاح إنشاء التقرير
  void _showReportSuccess(dynamic file) {
    Get.snackbar(
      'تم إنشاء التقرير',
      'يمكنك الآن عرض أو مشاركة التقرير',
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      duration: const Duration(seconds: 5),
      mainButton: TextButton(
        onPressed: () => PdfGenerator.sharePdf(file),
        child: const Text(
          'مشاركة',
          style: TextStyle(color: Colors.green, fontWeight: FontWeight.bold),
        ),
      ),
    );

    // فتح نافذة عرض التقرير
    PdfGenerator.openPdf(file);
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
