import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/themes/app_theme.dart';
import 'molds_controller.dart';

class GridItem {
  final IconData icon;
  final String title;
  final String value;

  GridItem({
    required this.icon,
    required this.title,
    required this.value,
  });
}

class MoldsPage extends StatelessWidget {
  const MoldsPage({super.key});
  
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colors.black87,
        ),
      ),
    );
  }

  Widget _buildInfoGrid(List<GridItem> items) {
    return GridView.count(
      crossAxisCount: 2,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      mainAxisSpacing: 8,
      crossAxisSpacing: 8,
      childAspectRatio: 2,
      children: items.map((item) => _buildGridItem(item)).toList(),
    );
  }

  Widget _buildGridItem(GridItem item) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              children: [
                Icon(item.icon, size: 16, color: Colors.blue),
                const SizedBox(width: 8),
                Text(
                  item.title,
                  style: const TextStyle(fontSize: 12, color: Colors.black54),
                ),
              ],
            ),
            const SizedBox(height: 4),
            Text(
              item.value,
              style: const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final MoldsController controller = Get.put(MoldsController());

    return Scaffold(
      appBar: AppBar(title: const Text('إدارة الإسطمبات'), centerTitle: true),
      drawer: const CustomDrawer(),
      body: Column(
        children: [
          _buildHeader(context),
          Expanded(child: _buildMoldsList(controller)),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddMoldDialog(context, controller),
        tooltip: 'إضافة إسطمبة جديدة',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      color: Colors.grey.shade100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'إسطمبات المصنع',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              _buildFilterButton(context),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة الإسطمبات وحالتها والمنتجات المرتبطة بها',
            style: TextStyle(fontSize: 14, color: Colors.grey.shade700),
          ),
          const SizedBox(height: 16),
          _buildStatisticsBar(),
        ],
      ),
    );
  }

  Widget _buildFilterButton(BuildContext context) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.filter_list),
      onSelected: (value) {
        final controller = Get.find<MoldsController>();
        switch (value) {
          case 'all':
            controller.filterMolds('all');
            break;
          case 'available':
            controller.filterMolds('available');
            break;
          case 'inUse':
            controller.filterMolds('inUse');
            break;
          case 'maintenance':
            controller.filterMolds('maintenance');
            break;
        }
      },
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'all',
          child: Text('جميع الإسطمبات'),
        ),
        const PopupMenuItem(
          value: 'available',
          child: Text('المتاحة'),
        ),
        const PopupMenuItem(
          value: 'inUse',
          child: Text('قيد الاستخدام'),
        ),
        const PopupMenuItem(
          value: 'maintenance',
          child: Text('في الصيانة'),
        ),
      ],
    );
  }

  Widget _buildStatisticsBar() {
    return Obx(() {
      final controller = Get.find<MoldsController>();
      return Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildStatCard(
            'الإجمالي',
            controller.molds.length.toString(),
            Icons.settings,
            Colors.blue,
          ),
          _buildStatCard(
            'متاحة',
            controller.getAvailableMoldsCount().toString(),
            Icons.check_circle,
            Colors.green,
          ),
          _buildStatCard(
            'مستخدمة',
            controller.getInUseMoldsCount().toString(),
            Icons.engineering,
            Colors.orange,
          ),
          _buildStatCard(
            'صيانة',
            controller.getMaintenanceMoldsCount().toString(),
            Icons.build,
            Colors.red,
          ),
        ],
      );
    });
  }

  Widget _buildStatCard(String title, String count, IconData icon, Color color) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              count,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: color),
            ),
            Text(
              title,
              style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMoldsList(MoldsController controller) {
    return Obx(() {
      if (controller.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }

      if (controller.molds.isEmpty) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.settings_outlined,
                size: 80,
                color: Colors.grey.shade400,
              ),
              const SizedBox(height: 16),
              Text(
                'لا توجد إسطمبات مضافة',
                style: TextStyle(fontSize: 18, color: Colors.grey.shade600),
              ),
              const SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: () => _showAddMoldDialog(Get.context!, controller),
                icon: const Icon(Icons.add),
                label: const Text('إضافة إسطمبة جديدة'),
              ),
            ],
          ),
        );
      }

      return RefreshIndicator(
        onRefresh: controller.fetchMolds,
        child: Scrollbar(
          child: ListView.builder(
            padding: const EdgeInsets.all(8),
            itemCount: controller.molds.length,
            itemBuilder: (context, index) {
              final mold = controller.molds[index];
              return _buildMoldCard(context, mold, controller);
            },
          ),
        ),
      );
    });
  }

  Widget _buildMoldCard(
    BuildContext context,
    Mold mold,
    MoldsController controller,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ExpansionTile(
        leading: Icon(
          Icons.precision_manufacturing_outlined,
          size: 40,
          color: mold.status == MoldStatus.available
              ? Colors.green
              : mold.status == MoldStatus.inUse
                  ? Colors.orange
                  : Colors.red,
        ),
        title: Text(
          mold.name,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'كود المنتج: ${mold.productCode}',
          style: TextStyle(color: Colors.grey.shade600),
        ),
        trailing: _buildMoldStatusChip(mold.status),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Divider(),
                const SizedBox(height: 8),
                // معلومات الإسطمبة
                _buildSectionTitle('معلومات الإسطمبة'),
                _buildInfoGrid([
                  GridItem(
                    icon: Icons.grid_view,
                    title: 'عدد التجاويف',
                    value: mold.cavityCount.toString(),
                  ),
                  GridItem(
                    icon: Icons.scale,
                    title: 'وزن القطعة',
                    value: '${mold.singlePartWeight} جرام',
                  ),
                  GridItem(
                    icon: Icons.calendar_today,
                    title: 'تاريخ الإضافة',
                    value: controller.formatDate(mold.createdAt),
                  ),
                  GridItem(
                    icon: Icons.build,
                    title: 'آخر صيانة',
                    value: controller.getLastMaintenanceDate(mold.id!),
                  ),
                ]),
                const SizedBox(height: 16),
                // معلومات الاستخدام
                _buildSectionTitle('معلومات الاستخدام'),
                Obx(() {
                  final currentMachine = controller.getMachineInfo(
                    mold.currentMachineId,
                  );
                  return _buildInfoGrid([
                    GridItem(
                      icon: Icons.precision_manufacturing,
                      title: 'الماكينة الحالية',
                      value: currentMachine != null
                          ? currentMachine['name']
                          : 'غير مركبة',
                    ),
                    GridItem(
                      icon: Icons.timer,
                      title: 'مدة الاستخدام',
                      value: controller.getTotalUsageTime(mold.id!),
                    ),
                    GridItem(
                      icon: Icons.production_quantity_limits,
                      title: 'عدد القطع المنتجة',
                      value: controller.getTotalProducedParts(mold.id!),
                    ),
                    GridItem(
                      icon: Icons.trending_up,
                      title: 'معدل الإنتاج',
                      value: controller.getProductionRate(mold.id!),
                    ),
                  ]);
                }),
                const SizedBox(height: 16),
                // رسم بياني للاستخدام
                _buildSectionTitle('إحصائيات الاستخدام'),
                SizedBox(
                  height: 200,
                  child: Obx(() => controller.buildUsageChart(mold.id!)),
                ),
                const SizedBox(height: 16),
                // أزرار التحكم
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton.icon(
                      onPressed: () => controller.showMoldUsageHistoryDialog(
                        mold.id!,
                        mold.name,
                      ),
                      icon: const Icon(Icons.history, size: 18),
                      label: const Text('سجل الاستخدام'),
                    ),
                    const SizedBox(width: 8),
                    if (mold.status != MoldStatus.inUse)
                      OutlinedButton.icon(
                        onPressed: () => controller.changeMoldStatus(
                          mold.id!,
                          mold.status == MoldStatus.available
                              ? MoldStatus.maintenance
                              : MoldStatus.available,
                        ),
                        icon: Icon(
                          mold.status == MoldStatus.available
                              ? Icons.build
                              : Icons.check_circle,
                        ),
                        label: Text(
                          mold.status == MoldStatus.available
                          ? 'تحويل للصيانة'
                          : 'تحويل لمتاحة',
                        ),
                      ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed:
                          () => _showEditMoldDialog(context, mold, controller),
                      icon: const Icon(Icons.edit),
                      label: const Text('تعديل'),
                    ),
                    const SizedBox(width: 8),
                    TextButton.icon(
                      onPressed:
                          () => _showDeleteConfirmation(context, mold, controller),
                      icon: const Icon(Icons.delete, color: Colors.red),
                      label: const Text('حذف', style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Removed unused method

  Widget _buildMoldStatusChip(String status) {
    Color chipColor;

    switch (status) {
      case MoldStatus.available:
        chipColor = AppTheme.successColor;
        break;
      case MoldStatus.inUse:
        chipColor = Colors.orange;
        break;
      case MoldStatus.maintenance:
        chipColor = AppTheme.errorColor;
        break;
      default:
        chipColor = Colors.grey;
    }

    return Chip(
      label: Text(
        status,
        style: const TextStyle(color: Colors.white, fontSize: 12),
      ),
      backgroundColor: chipColor,
      padding: const EdgeInsets.symmetric(horizontal: 4),
    );
  }

  void _showAddMoldDialog(BuildContext context, MoldsController controller) {
    showDialog(
      context: context,
      builder: (context) => AddEditMoldDialog(onSave: controller.addMold),
    );
  }

  void _showEditMoldDialog(
    BuildContext context,
    Mold mold,
    MoldsController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) =>
              AddEditMoldDialog(mold: mold, onSave: controller.updateMold),
    );
  }

  void _showDeleteConfirmation(
    BuildContext context,
    Mold mold,
    MoldsController controller,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text('هل أنت متأكد من حذف الإسطمبة "${mold.name}"؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.pop(context);
                  controller.deleteMold(mold.id!);
                },
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}

// مربع حوار إضافة/تعديل الإسطمبة
class AddEditMoldDialog extends StatefulWidget {
  final Mold? mold;
  final Function(Mold) onSave;

  const AddEditMoldDialog({super.key, this.mold, required this.onSave});

  @override
  State<AddEditMoldDialog> createState() => _AddEditMoldDialogState();
}

class _AddEditMoldDialogState extends State<AddEditMoldDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _productCodeController = TextEditingController();
  final TextEditingController _cavityCountController = TextEditingController();
  final TextEditingController _partWeightController = TextEditingController();
  String _status = MoldStatus.available;
  final DateTime _createdAt = DateTime.now();

  @override
  void initState() {
    super.initState();
    if (widget.mold != null) {
      _nameController.text = widget.mold!.name;
      _productCodeController.text = widget.mold!.productCode;
      _cavityCountController.text = widget.mold!.cavityCount.toString();
      _partWeightController.text = widget.mold!.singlePartWeight.toString();
      _status = widget.mold!.status;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _productCodeController.dispose();
    _cavityCountController.dispose();
    _partWeightController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEditing = widget.mold != null;

    return AlertDialog(
      title: Text(isEditing ? 'تعديل الإسطمبة' : 'إضافة إسطمبة جديدة'),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(
                  labelText: 'اسم الإسطمبة',
                  hintText: 'أدخل اسم الإسطمبة',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم الإسطمبة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _productCodeController,
                decoration: const InputDecoration(
                  labelText: 'كود المنتج',
                  hintText: 'أدخل كود المنتج المرتبط بالإسطمبة',
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال كود المنتج';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _cavityCountController,
                decoration: const InputDecoration(
                  labelText: 'عدد التجاويف',
                  hintText: 'أدخل عدد التجاويف في الإسطمبة',
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال عدد التجاويف';
                  }
                  try {
                    int count = int.parse(value);
                    if (count <= 0) {
                      return 'يجب أن يكون العدد أكبر من الصفر';
                    }
                  } catch (e) {
                    return 'يرجى إدخال قيمة صحيحة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _partWeightController,
                decoration: const InputDecoration(
                  labelText: 'وزن القطعة (جرام)',
                  hintText: 'أدخل وزن القطعة الواحدة',
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال وزن القطعة';
                  }
                  try {
                    double weight = double.parse(value);
                    if (weight <= 0) {
                      return 'يجب أن يكون الوزن أكبر من الصفر';
                    }
                  } catch (e) {
                    return 'يرجى إدخال قيمة صحيحة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),
              if (!isEditing || widget.mold!.status != MoldStatus.inUse)
                DropdownButtonFormField<String>(
                  value: _status,
                  decoration: const InputDecoration(labelText: 'حالة الإسطمبة'),
                  items: const [
                    DropdownMenuItem(
                      value: MoldStatus.available,
                      child: Text(MoldStatus.available),
                    ),
                    DropdownMenuItem(
                      value: MoldStatus.maintenance,
                      child: Text(MoldStatus.maintenance),
                    ),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() {
                        _status = value;
                      });
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار حالة الإسطمبة';
                    }
                    return null;
                  },
                ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('إلغاء'),
        ),
        ElevatedButton(
          onPressed: _saveMold,
          child: Text(isEditing ? 'تحديث' : 'إضافة'),
        ),
      ],
    );
  }

  void _saveMold() {
    if (_formKey.currentState!.validate()) {
      final mold = Mold(
        id: widget.mold?.id,
        name: _nameController.text,
        productCode: _productCodeController.text,
        status:
            widget.mold?.status == MoldStatus.inUse
                ? MoldStatus.inUse
                : _status,
        currentMachineId: widget.mold?.currentMachineId,
        cavityCount: int.parse(_cavityCountController.text),
        singlePartWeight: double.parse(_partWeightController.text),
        createdAt: _createdAt,
      );

      widget.onSave(mold);
      Navigator.pop(context);
    }
  }
}
