import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/inventory.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:uuid/uuid.dart';

class InventoryRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء عنصر مخزون جديد
  Future<String> createInventoryItem(Inventory item) async {
    final inventoryWithId = Inventory(
      id: _uuid.v4(),
      itemId: item.itemId,
      itemType: item.itemType,
      itemName: item.itemName,
      itemCode: item.itemCode,
      currentQuantity: item.currentQuantity,
      minimumLevel: item.minimumLevel,
      reorderLevel: item.reorderLevel,
      unit: item.unit,
      unitCost: item.unitCost,
      lastUpdate: item.lastUpdate,
      status: item.status,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableInventory,
      inventoryWithId.toMap(),
    );
  }

  // الحصول على جميع عناصر المخزون
  Future<List<Inventory>> getAllInventoryItems() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableInventory,
    );

    return maps.map((map) => Inventory.fromMap(map)).toList();
  }

  // الحصول على عنصر مخزون بمعرف معين
  Future<Inventory?> getInventoryItemById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableInventory,
      id,
    );

    if (map == null) {
      return null;
    }

    return Inventory.fromMap(map);
  }

  // الحصول على عنصر مخزون من خلال معرف العنصر الأصلي ونوعه
  Future<Inventory?> getInventoryItemByItemIdAndType(
    String itemId,
    String itemType,
  ) async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableInventory}
      WHERE itemId = ? AND itemType = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      itemId,
      itemType,
    ]);

    if (result.isEmpty) {
      return null;
    }

    return Inventory.fromMap(result.first);
  }

  // تحديث عنصر مخزون
  Future<int> updateInventoryItem(Inventory item) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableInventory,
      item.toMap(),
    );
  }

  // حذف عنصر مخزون
  Future<int> deleteInventoryItem(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableInventory, id);
  }

  // تحديث كمية عنصر المخزون وإنشاء حركة مخزون
  Future<void> updateInventoryQuantity(
    String inventoryId,
    double quantityChange,
    String referenceType,
    String referenceId, {
    String notes = '',
    double? costPerUnit,
  }) async {
    // الحصول على عنصر المخزون
    final Inventory? item = await getInventoryItemById(inventoryId);
    if (item == null) {
      throw Exception('عنصر المخزون غير موجود');
    }

    // تحديد نوع الحركة (وارد أو صادر)
    final MovementType movementType =
        quantityChange > 0 ? MovementType.incoming : MovementType.outgoing;

    // استخدام التكلفة الحالية إذا لم يتم تحديد تكلفة جديدة
    final double actualCostPerUnit = costPerUnit ?? item.unitCost;

    // إنشاء حركة المخزون
    final InventoryMovement movement = InventoryMovement(
      id: _uuid.v4(),
      inventoryId: inventoryId,
      date: DateTime.now(),
      type: movementType,
      quantity: quantityChange.abs(), // استخدام القيمة المطلقة للكمية
      costPerUnit: actualCostPerUnit,
      referenceType: referenceType,
      referenceId: referenceId,
      notes: notes,
    );

    // حفظ حركة المخزون
    await _databaseHelper.insert(
      DatabaseHelper.tableInventoryMovements,
      movement.toMap(),
    );

    // حساب الكمية الجديدة
    final double newQuantity = item.currentQuantity + quantityChange;

    // تحديد حالة المخزون بناءً على الكمية الجديدة
    final InventoryStatus newStatus = _determineInventoryStatus(
      newQuantity,
      item.minimumLevel,
      item.reorderLevel,
    );

    // تحديث عنصر المخزون
    final Inventory updatedItem = item.copyWith(
      currentQuantity: newQuantity,
      lastUpdate: DateTime.now(),
      status: newStatus,
    );

    await updateInventoryItem(updatedItem);

    // إنشاء تنبيه للمخزون إذا كانت الكمية منخفضة
    if (newStatus != InventoryStatus.normal) {
      await _createInventoryAlert(updatedItem, newStatus);
    }
  }

  // تحديد حالة المخزون بناء على المستويات المحددة
  InventoryStatus _determineInventoryStatus(
    double currentQuantity,
    double minimumLevel,
    double reorderLevel,
  ) {
    if (currentQuantity <= minimumLevel) {
      return InventoryStatus.critical;
    } else if (currentQuantity <= reorderLevel) {
      return InventoryStatus.low;
    } else {
      return InventoryStatus.normal;
    }
  }

  // إنشاء تنبيه للمخزون
  Future<String> _createInventoryAlert(
    Inventory item,
    InventoryStatus status,
  ) async {
    final String alertType =
        status == InventoryStatus.critical
            ? 'مستوى حرج'
            : 'مستوى منخفض - إعادة طلب';

    final double thresholdLevel =
        status == InventoryStatus.critical
            ? item.minimumLevel
            : item.reorderLevel;

    final InventoryAlert alert = InventoryAlert(
      id: _uuid.v4(),
      inventoryId: item.id!,
      itemName: item.itemName,
      itemCode: item.itemCode,
      date: DateTime.now(),
      alertType: alertType,
      currentLevel: item.currentQuantity,
      thresholdLevel: thresholdLevel,
      isResolved: false,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableInventoryAlerts,
      alert.toMap(),
    );
  }

  // الحصول على حركات المخزون لعنصر معين
  Future<List<InventoryMovement>> getInventoryMovements(
    String inventoryId,
  ) async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableInventoryMovements}
      WHERE inventoryId = ?
      ORDER BY date DESC
    ''';

    List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(query, [
      inventoryId,
    ]);

    return maps.map((map) => InventoryMovement.fromMap(map)).toList();
  }

  // الحصول على جميع التنبيهات النشطة (غير المحلولة)
  Future<List<InventoryAlert>> getActiveAlerts() async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableInventoryAlerts}
      WHERE isResolved = 0
      ORDER BY date DESC
    ''';

    List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(query);

    return maps.map((map) => InventoryAlert.fromMap(map)).toList();
  }

  // وضع علامة تنبيه كمحلول
  Future<int> resolveAlert(String alertId) async {
    // الحصول على التنبيه
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableInventoryAlerts}
      WHERE id = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      alertId,
    ]);

    if (result.isEmpty) {
      return 0;
    }

    // تحديث حالة التنبيه
    Map<String, dynamic> alertMap = result.first;
    InventoryAlert alert = InventoryAlert.fromMap(alertMap);
    InventoryAlert updatedAlert = InventoryAlert(
      id: alert.id,
      inventoryId: alert.inventoryId,
      itemName: alert.itemName,
      itemCode: alert.itemCode,
      date: alert.date,
      alertType: alert.alertType,
      currentLevel: alert.currentLevel,
      thresholdLevel: alert.thresholdLevel,
      isResolved: true,
    );

    return await _databaseHelper.update(
      DatabaseHelper.tableInventoryAlerts,
      updatedAlert.toMap(),
    );
  }

  // الحصول على عناصر المخزون ذات المستوى المنخفض
  Future<List<Inventory>> getLowInventoryItems() async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableInventory}
      WHERE status <> 0
      ORDER BY status DESC, lastUpdate ASC
    ''';

    List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(query);

    return maps.map((map) => Inventory.fromMap(map)).toList();
  }

  // الحصول على تقرير استهلاك المواد الخام
  Future<List<Map<String, dynamic>>> getRawMaterialConsumptionReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    String formattedStartDate = startDate.toIso8601String().split('T')[0];
    String formattedEndDate = endDate.toIso8601String().split('T')[0];

    // تحسين الاستعلام باستخدام الفهارس المضافة وتقليل عمليات JOIN
    // 1. استخدام فهرس itemType في جدول المخزون
    // 2. استخدام فهرس date في جدول حركات المخزون
    // 3. استخدام فهرس inventoryId في جدول حركات المخزون
    String query = '''
      SELECT im.inventoryId, i.itemName, i.itemCode, i.unit,
             SUM(CASE WHEN im.type = 1 THEN im.quantity ELSE 0 END) as totalOut,
             SUM(CASE WHEN im.type = 0 THEN im.quantity ELSE 0 END) as totalIn,
             SUM(CASE WHEN im.type = 1 THEN im.quantity * im.costPerUnit ELSE 0 END) as totalCost
      FROM ${DatabaseHelper.tableInventoryMovements} im
      JOIN ${DatabaseHelper.tableInventory} i ON im.inventoryId = i.id
      WHERE i.itemType = 'raw_material'
        AND date(im.date) BETWEEN ? AND ?
      GROUP BY im.inventoryId
      ORDER BY totalOut DESC
      LIMIT 1000 -- إضافة حد للنتائج لتحسين الأداء
    ''';

    // إضافة تخزين مؤقت للاستعلام
    final cacheKey =
        'raw_material_consumption_$formattedStartDate'
        '_$formattedEndDate';
    final cachedResult = await _getCachedResult(cacheKey);

    if (cachedResult != null) {
      return cachedResult;
    }

    final result = await _databaseHelper.rawQuery(query, [
      formattedStartDate,
      formattedEndDate,
    ]);

    // تخزين النتيجة في التخزين المؤقت
    await _cacheResult(cacheKey, result);

    return result;
  }

  // تخزين نتيجة الاستعلام في التخزين المؤقت
  Future<void> _cacheResult(
    String key,
    List<Map<String, dynamic>> result,
  ) async {
    // يمكن استخدام shared_preferences أو أي آلية تخزين أخرى
    // هنا نستخدم متغير في الذاكرة للتبسيط
    _queryCache[key] = {
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': result,
    };
  }

  // الحصول على نتيجة مخزنة مؤقتًا
  Future<List<Map<String, dynamic>>?> _getCachedResult(String key) async {
    final cachedData = _queryCache[key];
    if (cachedData != null) {
      final timestamp = cachedData['timestamp'] as int;
      final now = DateTime.now().millisecondsSinceEpoch;

      // التحقق من صلاحية التخزين المؤقت (10 دقائق)
      if (now - timestamp < 10 * 60 * 1000) {
        return cachedData['data'] as List<Map<String, dynamic>>;
      }
    }
    return null;
  }

  // تخزين مؤقت للاستعلامات
  final Map<String, Map<String, dynamic>> _queryCache = {};

  // الحصول على تقرير إنتاج المنتجات
  Future<List<Map<String, dynamic>>> getProductionReport(
    DateTime startDate,
    DateTime endDate,
  ) async {
    String formattedStartDate = startDate.toIso8601String().split('T')[0];
    String formattedEndDate = endDate.toIso8601String().split('T')[0];

    // تحسين الاستعلام باستخدام الفهارس المضافة
    String query = '''
      SELECT im.inventoryId, i.itemName, i.itemCode, i.unit,
             SUM(CASE WHEN im.type = 0 THEN im.quantity ELSE 0 END) as totalIn,
             SUM(CASE WHEN im.type = 0 THEN im.quantity * im.costPerUnit ELSE 0 END) as totalValue
      FROM ${DatabaseHelper.tableInventoryMovements} im
      JOIN ${DatabaseHelper.tableInventory} i ON im.inventoryId = i.id
      WHERE i.itemType = 'product'
        AND date(im.date) BETWEEN ? AND ?
        AND im.referenceType = 'production'
      GROUP BY im.inventoryId
      ORDER BY totalIn DESC
      LIMIT 1000 -- إضافة حد للنتائج لتحسين الأداء
    ''';

    // إضافة تخزين مؤقت للاستعلام
    final cacheKey =
        'production_report_$formattedStartDate'
        '_$formattedEndDate';
    final cachedResult = await _getCachedResult(cacheKey);

    if (cachedResult != null) {
      return cachedResult;
    }

    final result = await _databaseHelper.rawQuery(query, [
      formattedStartDate,
      formattedEndDate,
    ]);

    // تخزين النتيجة في التخزين المؤقت
    await _cacheResult(cacheKey, result);

    return result;
  }

  // إضافة قطع بلاستيكية إلى المخزون
  Future<void> addPlasticPartToInventory(
    String moldId,
    String moldName,
    String productCode,
    int quantity,
    double weight,
    double rawMaterialUsed,
    String rawMaterialId,
    String referenceType,
    String? referenceId,
  ) async {
    // التحقق من وجود العنصر في المخزون
    Inventory? existingItem = await getInventoryItemByItemIdAndType(
      moldId,
      'plastic_part',
    );

    DateTime now = DateTime.now();

    // حساب تكلفة الوحدة (يمكن أن تختلف حسب احتياجات العمل)
    double unitCost = 0.0;
    if (quantity > 0) {
      // حساب متوسط تكلفة القطعة بناءً على كمية المواد الخام المستهلكة
      RawMaterial? rawMaterial = await _getRawMaterialCost(rawMaterialId);
      if (rawMaterial != null) {
        unitCost = (rawMaterialUsed * rawMaterial.costPerKg) / quantity;
      }
    }

    if (existingItem == null) {
      // إنشاء عنصر جديد في المخزون
      Inventory newItem = Inventory(
        itemId: moldId,
        itemType: 'plastic_part',
        itemName: moldName,
        itemCode: productCode,
        currentQuantity: quantity.toDouble(),
        minimumLevel: 0.0, // يمكن تعديله حسب متطلبات العمل
        reorderLevel: 100.0, // يمكن تعديله حسب متطلبات العمل
        unit: 'قطعة',
        unitCost: unitCost,
        lastUpdate: now,
        status: InventoryStatus.normal,
      );

      String inventoryId = await createInventoryItem(newItem);

      // إنشاء حركة مخزون
      InventoryMovement movement = InventoryMovement(
        inventoryId: inventoryId,
        date: now,
        type: MovementType.incoming,
        quantity: quantity.toDouble(),
        costPerUnit: unitCost,
        referenceType: referenceType,
        referenceId: referenceId ?? '',
        notes: 'إنتاج جديد - إسطمبة: $moldName',
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableInventoryMovements,
        movement.toMap(),
      );
    } else {
      // تحديث كمية العنصر الموجود
      await updateInventoryQuantity(
        existingItem.id!,
        quantity.toDouble(),
        referenceType,
        referenceId ?? '',
        notes: 'إنتاج جديد - إسطمبة: $moldName',
        costPerUnit: unitCost,
      );
    }
  }

  // الحصول على تكلفة المادة الخام
  Future<RawMaterial?> _getRawMaterialCost(String rawMaterialId) async {
    String query = '''
      SELECT * FROM ${DatabaseHelper.tableRawMaterials}
      WHERE id = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      rawMaterialId,
    ]);

    if (result.isEmpty) {
      return null;
    }

    return RawMaterial.fromMap(result.first);
  }
}
