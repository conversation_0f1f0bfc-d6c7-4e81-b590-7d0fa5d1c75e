# دليل استخدام التحسينات في نظام إدارة المصنع

## مقدمة

تم تنفيذ مجموعة من التحسينات في نظام إدارة المصنع لتحسين الأداء وزيادة موثوقية النظام. يشمل هذا الدليل شرحًا مفصلاً لكيفية استخدام هذه التحسينات والاستفادة منها.

## جدول المحتويات

1. [تحسينات أداء قاعدة البيانات SQLite](#تحسينات-أداء-قاعدة-البيانات-sqlite)
2. [نظام النسخ الاحتياطي التلقائي](#نظام-النسخ-الاحتياطي-التلقائي)
3. [أداة اختبارات الأداء](#أداة-اختبارات-الأداء)

---

## تحسينات أداء قاعدة البيانات SQLite

تم تحسين أداء قاعدة البيانات SQLite من خلال إضافة فهارس للجداول الأكثر استخدامًا وتحسين استعلامات SQL وإضافة آلية تخزين مؤقت للاستعلامات المتكررة.

### الفهارس المضافة

تم إضافة الفهارس التالية لتحسين أداء الاستعلامات:

- **جدول الإنتاج**:
  - فهرس على حقل التاريخ (`date`)
  - فهرس على حقل الماكينة (`machineId`)
  - فهرس على حقل الإسطمبة (`moldId`)
  - فهرس على حقل المادة الخام (`rawMaterialId`)

- **جدول المخزون**:
  - فهرس على حقل نوع العنصر (`itemType`)
  - فهرس على حقل معرف العنصر (`itemId`)
  - فهرس على حقل الحالة (`status`)
  - فهرس على حقل الكود (`itemCode`)

- **جدول حركات المخزون**:
  - فهرس على حقل معرف المخزون (`inventoryId`)
  - فهرس على حقل التاريخ (`date`)
  - فهرس على حقل النوع (`type`)
  - فهرس على حقول نوع المرجع ومعرف المرجع (`referenceType`, `referenceId`)

- **جدول المبيعات**:
  - فهرس على حقل التاريخ (`date`)
  - فهرس على حقل حالة الشحن (`shippingStatus`)
  - فهرس على حقل اسم العميل (`customerName`)

### التخزين المؤقت للاستعلامات

تم إضافة آلية تخزين مؤقت للاستعلامات المتكررة لتحسين الأداء. تعمل هذه الآلية على تخزين نتائج الاستعلامات المتكررة في الذاكرة لفترة محددة (10 دقائق) لتجنب إعادة تنفيذ نفس الاستعلام عدة مرات.

**ملاحظة**: يتم تحديث التخزين المؤقت تلقائيًا عند تغيير البيانات.

### تحسينات استعلامات SQL

تم تحسين استعلامات SQL في المستودعات الرئيسية مثل:

- `inventory_repository.dart`: تحسين استعلامات تقارير استهلاك المواد الخام وتقارير الإنتاج
- `sales_repository.dart`: تحسين استعلامات المبيعات وتقارير المبيعات

### الفوائد المتوقعة

- تحسين سرعة الاستجابة في الشاشات التي تعرض كميات كبيرة من البيانات
- تقليل استهلاك موارد النظام عند تنفيذ استعلامات معقدة
- تحسين تجربة المستخدم من خلال تقليل وقت الانتظار

---

## نظام النسخ الاحتياطي التلقائي

تم إضافة نظام نسخ احتياطي تلقائي لقاعدة البيانات لضمان سلامة البيانات وتجنب فقدانها في حالة حدوث أي مشكلة.

### كيفية الوصول إلى نظام النسخ الاحتياطي

1. افتح القائمة الجانبية في التطبيق
2. انتقل إلى قسم "إعدادات النظام"
3. اضغط على "النسخ الاحتياطي"

### إنشاء نسخة احتياطية يدوية

1. في صفحة النسخ الاحتياطي، اضغط على زر "إنشاء نسخة احتياطية" أو الزر العائم (+) في أسفل الشاشة
2. أدخل اسمًا للنسخة الاحتياطية (اختياري)
3. اضغط على "إنشاء"
4. سيتم إنشاء نسخة احتياطية من قاعدة البيانات وتخزينها في مجلد النسخ الاحتياطي

### جدولة النسخ الاحتياطي التلقائي

1. في صفحة النسخ الاحتياطي، انتقل إلى قسم "جدولة النسخ الاحتياطي التلقائي"
2. اختر أحد الخيارات التالية:
   - يدوي (بدون جدولة)
   - يومي (كل 24 ساعة)
   - أسبوعي (كل 7 أيام)
3. سيقوم النظام بإنشاء نسخ احتياطية تلقائية حسب الجدول المحدد

### استعادة قاعدة البيانات من نسخة احتياطية

1. في صفحة النسخ الاحتياطي، انتقل إلى قسم "النسخ الاحتياطية المتاحة"
2. اضغط على أيقونة "استعادة" بجانب النسخة الاحتياطية التي تريد استعادتها
3. اقرأ التحذير وتأكد من أنك تريد استبدال قاعدة البيانات الحالية
4. اضغط على "استعادة" للتأكيد
5. سيتم استعادة قاعدة البيانات من النسخة الاحتياطية المحددة

### إدارة النسخ الاحتياطية

- **حذف نسخة احتياطية**: اضغط على أيقونة "حذف" بجانب النسخة الاحتياطية التي تريد حذفها
- **تغيير مجلد الحفظ**: اضغط على زر "تغيير مجلد الحفظ" واختر المجلد الذي تريد تخزين النسخ الاحتياطية فيه

### نصائح وإرشادات

- قم بإنشاء نسخة احتياطية قبل إجراء أي تغييرات كبيرة في النظام
- احتفظ بنسخ احتياطية في مكان آمن خارج الجهاز (مثل محرك أقراص خارجي أو خدمة سحابية)
- قم بمراجعة النسخ الاحتياطية بانتظام للتأكد من صلاحيتها

---

## أداة اختبارات الأداء

تم إضافة أداة اختبارات الأداء لقياس أداء التطبيق وتحديد نقاط الضعف وتحسينها.

### كيفية الوصول إلى أداة اختبارات الأداء

1. افتح القائمة الجانبية في التطبيق
2. انتقل إلى قسم "إعدادات النظام"
3. اضغط على "اختبارات الأداء"

### مراقبة الأداء

1. في صفحة اختبارات الأداء، اضغط على زر "بدء المراقبة" في قسم "مراقبة الأداء"
2. سيبدأ النظام في مراقبة أداء التطبيق وتسجيل البيانات
3. استخدم التطبيق بشكل طبيعي أثناء المراقبة
4. عندما تنتهي، اضغط على زر "إيقاف المراقبة"

### اختبارات أداء قاعدة البيانات

1. في صفحة اختبارات الأداء، اضغط على زر "تشغيل اختبارات قاعدة البيانات" في قسم "اختبارات أداء قاعدة البيانات"
2. سيقوم النظام بتنفيذ مجموعة من الاختبارات لقياس أداء قاعدة البيانات
3. ستظهر النتائج في قسم "نتائج الاختبارات"

### قياس زمن استجابة واجهة المستخدم

1. في صفحة اختبارات الأداء، اضغط على زر "قياس زمن الاستجابة" في قسم "قياس زمن استجابة واجهة المستخدم"
2. أدخل اسم الشاشة التي تريد قياس زمن استجابتها
3. اضغط على "قياس"
4. ستظهر النتائج في قسم "نتائج الاختبارات"

### قياس استهلاك الذاكرة

1. في صفحة اختبارات الأداء، اضغط على زر "قياس استهلاك الذاكرة" في قسم "قياس استهلاك الذاكرة"
2. سيقوم النظام بقياس استهلاك الذاكرة الحالي للتطبيق
3. ستظهر النتائج في قسم "نتائج الاختبارات"

### إنشاء تقرير أداء

1. في صفحة اختبارات الأداء، اضغط على زر "إنشاء تقرير" في قسم "تقرير الأداء"
2. سيقوم النظام بإنشاء تقرير أداء مفصل يتضمن جميع البيانات التي تم جمعها
3. يمكنك مشاركة التقرير أو حفظه على الجهاز

### تفسير نتائج الاختبارات

- **زمن استجابة واجهة المستخدم**: يجب أن يكون أقل من 100 مللي ثانية للاستجابة الفورية
- **أداء قاعدة البيانات**: يجب أن تكون الاستعلامات البسيطة أقل من 50 مللي ثانية والاستعلامات المعقدة أقل من 200 مللي ثانية
- **استهلاك الذاكرة**: يجب أن يكون في حدود معقولة (أقل من 100 ميجابايت للاستخدام العادي)

### نصائح لتحسين الأداء

- قم بإغلاق التطبيقات الأخرى التي تعمل في الخلفية أثناء استخدام التطبيق
- قم بتنظيف ذاكرة التخزين المؤقت بانتظام
- تجنب تخزين كميات كبيرة من البيانات غير الضرورية في قاعدة البيانات
- قم بتحديث التطبيق إلى أحدث إصدار دائمًا

---

## الدعم الفني

إذا واجهتك أي مشكلة في استخدام هذه التحسينات أو كان لديك أي استفسار، يرجى التواصل مع فريق الدعم الفني.

---

*تم التحديث في: [التاريخ]*
