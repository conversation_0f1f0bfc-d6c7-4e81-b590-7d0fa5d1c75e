import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:mostafa_final/data/models/predictive_analytics.dart';
import 'package:mostafa_final/data/models/breakdown.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/repositories/maintenance_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:uuid/uuid.dart';

/// خدمة التحليل التنبؤي والذكاء الاصطناعي
class PredictiveAnalyticsService {
  final MaintenanceRepository _maintenanceRepository = MaintenanceRepository();
  final ProductionRepository _productionRepository = ProductionRepository();
  final MachineRepository _machineRepository = MachineRepository();
  final _uuid = const Uuid();

  /// التنبؤ بالأعطال للماكينات
  Future<List<FailurePrediction>> predictMachineFailures({
    int daysAhead = 30,
  }) async {
    try {
      final machines = await _machineRepository.getAllMachines();
      final predictions = <FailurePrediction>[];

      for (var machine in machines) {
        final prediction = await _predictSingleMachineFailure(
          machine,
          daysAhead,
        );
        if (prediction != null) {
          predictions.add(prediction);
        }
      }

      // ترتيب حسب احتمالية الفشل
      predictions.sort(
        (a, b) => b.failureProbability.compareTo(a.failureProbability),
      );

      return predictions;
    } catch (e) {
      debugPrint('خطأ في التنبؤ بالأعطال: $e');
      return [];
    }
  }

  /// التنبؤ بعطل ماكينة واحدة
  Future<FailurePrediction?> _predictSingleMachineFailure(
    Machine machine,
    int daysAhead,
  ) async {
    try {
      // جمع البيانات التاريخية
      final historicalData = await _gatherMachineHistoricalData(
        machine.id!,
        daysAhead * 2,
      );

      if (historicalData['breakdowns'].isEmpty) {
        return null; // لا توجد بيانات كافية
      }

      // حساب المؤشرات الإحصائية
      final stats = _calculateMachineStatistics(historicalData);

      // تطبيق نموذج التنبؤ
      final prediction = _applyFailurePredictionModel(
        machine,
        stats,
        daysAhead,
      );

      return prediction;
    } catch (e) {
      debugPrint('خطأ في التنبؤ بعطل الماكينة ${machine.name}: $e');
      return null;
    }
  }

  /// جمع البيانات التاريخية للماكينة
  Future<Map<String, dynamic>> _gatherMachineHistoricalData(
    String machineId,
    int days,
  ) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    // الحصول على الأعطال التاريخية
    final breakdowns = await _maintenanceRepository.getBreakdownsByMachine(
      machineId,
    );
    final recentBreakdowns =
        breakdowns
            .where(
              (b) =>
                  b.startTime.isAfter(startDate) &&
                  b.startTime.isBefore(endDate),
            )
            .toList();

    // الحصول على بيانات الإنتاج
    final productions = await _productionRepository.getAllProduction();
    final machineProductions =
        productions
            .where(
              (p) =>
                  p.machineId == machineId &&
                  p.date.isAfter(startDate) &&
                  p.date.isBefore(endDate),
            )
            .toList();

    // الحصول على سجلات الصيانة
    final maintenanceRecords = await _maintenanceRepository
        .getMaintenanceRecordsByMachine(machineId);
    final recentMaintenance =
        maintenanceRecords
            .where(
              (m) =>
                  m.startTime.isAfter(startDate) &&
                  m.startTime.isBefore(endDate),
            )
            .toList();

    return {
      'breakdowns': recentBreakdowns,
      'productions': machineProductions,
      'maintenance': recentMaintenance,
      'timeWindow': days,
    };
  }

  /// حساب الإحصائيات للماكينة
  Map<String, dynamic> _calculateMachineStatistics(Map<String, dynamic> data) {
    final breakdowns = data['breakdowns'] as List<Breakdown>;
    final productions = data['productions'] as List<Production>;
    final timeWindow = data['timeWindow'] as int;

    // حساب MTBF (Mean Time Between Failures)
    double mtbf = 0.0;
    if (breakdowns.length > 1) {
      final intervals = <double>[];
      for (int i = 1; i < breakdowns.length; i++) {
        final interval =
            breakdowns[i].startTime
                .difference(
                  breakdowns[i - 1].endTime ?? breakdowns[i - 1].startTime,
                )
                .inHours
                .toDouble();
        intervals.add(interval);
      }
      mtbf =
          intervals.isNotEmpty
              ? intervals.reduce((a, b) => a + b) / intervals.length
              : 0.0;
    }

    // حساب معدل الأعطال
    final failureRate = breakdowns.length / timeWindow; // أعطال لكل يوم

    // حساب متوسط ساعات التشغيل
    final totalOperatingHours = productions.fold(
      0.0,
      (sum, p) => sum + (p.partsProduced * p.cycleTime / 3600),
    );
    final avgOperatingHours =
        productions.isNotEmpty ? totalOperatingHours / productions.length : 0.0;

    // حساب كفاءة الإنتاج
    final avgEfficiency =
        productions.isNotEmpty
            ? productions.fold(
                  0.0,
                  (sum, p) => sum + (30.0 / p.cycleTime * 100),
                ) /
                productions.length
            : 0.0;

    // تحليل أنواع الأعطال
    final failureTypes = <BreakdownType, int>{};
    for (var breakdown in breakdowns) {
      failureTypes[breakdown.type] = (failureTypes[breakdown.type] ?? 0) + 1;
    }

    return {
      'mtbf': mtbf,
      'failureRate': failureRate,
      'avgOperatingHours': avgOperatingHours,
      'avgEfficiency': avgEfficiency,
      'totalBreakdowns': breakdowns.length,
      'failureTypes': failureTypes,
      'lastBreakdown': breakdowns.isNotEmpty ? breakdowns.last.startTime : null,
    };
  }

  /// تطبيق نموذج التنبؤ بالأعطال
  FailurePrediction _applyFailurePredictionModel(
    Machine machine,
    Map<String, dynamic> stats,
    int daysAhead,
  ) {
    // نموذج بسيط للتنبؤ بناءً على الإحصائيات
    final mtbf = stats['mtbf'] as double;
    final failureRate = stats['failureRate'] as double;
    final avgEfficiency = stats['avgEfficiency'] as double;
    final totalBreakdowns = stats['totalBreakdowns'] as int;
    final lastBreakdown = stats['lastBreakdown'] as DateTime?;

    // حساب احتمالية الفشل
    double failureProbability = 0.0;

    // العامل الأول: معدل الأعطال التاريخي
    failureProbability += (failureRate * daysAhead) * 30;

    // العامل الثاني: MTBF
    if (mtbf > 0) {
      final daysSinceLastFailure =
          lastBreakdown != null
              ? DateTime.now().difference(lastBreakdown).inDays
              : daysAhead;
      failureProbability += (daysSinceLastFailure / (mtbf / 24)) * 25;
    }

    // العامل الثالث: كفاءة الإنتاج
    if (avgEfficiency < 70) {
      failureProbability += (70 - avgEfficiency) * 0.5;
    }

    // العامل الرابع: عمر الماكينة (افتراضي)
    final machineAge = 2.0; // عمر افتراضي بالسنوات
    failureProbability += machineAge * 2;

    // تحديد نوع العطل المتوقع
    final failureTypes = stats['failureTypes'] as Map<BreakdownType, int>;
    final mostCommonFailureType = _getMostCommonFailureType(failureTypes);

    // حساب الأيام حتى العطل المتوقع
    int daysUntilFailure = daysAhead;
    if (mtbf > 0 && lastBreakdown != null) {
      final expectedDays =
          (mtbf / 24) - DateTime.now().difference(lastBreakdown).inDays;
      daysUntilFailure = math.max(1, expectedDays.round());
    }

    // حساب مستوى الثقة
    final confidenceLevel = _calculateConfidenceLevel(
      totalBreakdowns,
      mtbf,
      failureRate,
    );

    // تحديد العوامل المساهمة
    final contributingFactors = _identifyContributingFactors(stats);

    // تحديد الإجراء الموصى به
    final recommendedAction = _generateRecommendedAction(
      failureProbability,
      mostCommonFailureType,
    );

    return FailurePrediction(
      id: _uuid.v4(),
      machineId: machine.id!,
      machineName: machine.name,
      predictedFailureDate: DateTime.now().add(
        Duration(days: daysUntilFailure),
      ),
      failureProbability: math.min(100.0, math.max(0.0, failureProbability)),
      predictedFailureType: _mapBreakdownTypeToFailureType(
        mostCommonFailureType,
      ),
      daysUntilFailure: daysUntilFailure,
      confidenceLevel: confidenceLevel,
      contributingFactors: contributingFactors,
      recommendedAction: recommendedAction,
      createdAt: DateTime.now(),
      status: PredictionStatus.active,
    );
  }

  /// تحديد نوع العطل الأكثر شيوعاً
  BreakdownType _getMostCommonFailureType(
    Map<BreakdownType, int> failureTypes,
  ) {
    if (failureTypes.isEmpty) return BreakdownType.mechanical;

    return failureTypes.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  /// حساب مستوى الثقة في التنبؤ
  double _calculateConfidenceLevel(
    int totalBreakdowns,
    double mtbf,
    double failureRate,
  ) {
    double confidence = 50.0; // قاعدة أساسية

    // زيادة الثقة مع زيادة البيانات التاريخية
    confidence += math.min(30.0, totalBreakdowns * 3);

    // زيادة الثقة مع استقرار MTBF
    if (mtbf > 0) {
      confidence += math.min(20.0, mtbf / 100);
    }

    return math.min(100.0, confidence);
  }

  /// تحديد العوامل المساهمة
  List<String> _identifyContributingFactors(Map<String, dynamic> stats) {
    final factors = <String>[];

    final mtbf = stats['mtbf'] as double;
    final failureRate = stats['failureRate'] as double;
    final avgEfficiency = stats['avgEfficiency'] as double;

    if (mtbf < 168) {
      // أقل من أسبوع
      factors.add('انخفاض متوسط الوقت بين الأعطال');
    }

    if (failureRate > 0.1) {
      // أكثر من عطل كل 10 أيام
      factors.add('ارتفاع معدل الأعطال');
    }

    if (avgEfficiency < 70) {
      factors.add('انخفاض كفاءة الإنتاج');
    }

    if (factors.isEmpty) {
      factors.add('التآكل الطبيعي مع الاستخدام');
    }

    return factors;
  }

  /// إنشاء الإجراء الموصى به
  String _generateRecommendedAction(
    double failureProbability,
    BreakdownType failureType,
  ) {
    if (failureProbability > 80) {
      return 'إجراء صيانة طارئة فورية - احتمالية عطل عالية جداً';
    } else if (failureProbability > 60) {
      return 'جدولة صيانة وقائية خلال الأسبوع القادم';
    } else if (failureProbability > 40) {
      return 'مراقبة مكثفة وفحص دوري للماكينة';
    } else if (failureProbability > 20) {
      return 'صيانة وقائية حسب الجدولة العادية';
    } else {
      return 'متابعة التشغيل العادي مع المراقبة الروتينية';
    }
  }

  /// تحويل نوع العطل
  FailureType _mapBreakdownTypeToFailureType(BreakdownType breakdownType) {
    switch (breakdownType) {
      case BreakdownType.mechanical:
        return FailureType.mechanical;
      case BreakdownType.electrical:
        return FailureType.electrical;
      case BreakdownType.hydraulic:
        return FailureType.hydraulic;
      case BreakdownType.software:
        return FailureType.software;
      case BreakdownType.tooling:
        return FailureType.wear;
      case BreakdownType.material:
        return FailureType.wear;
      case BreakdownType.operator:
        return FailureType.mechanical;
      case BreakdownType.environmental:
        return FailureType.overheating;
    }
  }

  /// تحليل الاتجاهات للمؤشرات
  Future<List<TrendAnalysis>> analyzeTrends({int analysisWindow = 30}) async {
    try {
      final trends = <TrendAnalysis>[];

      // تحليل اتجاه الإنتاج
      final productionTrend = await _analyzeProductionTrend(analysisWindow);
      if (productionTrend != null) trends.add(productionTrend);

      // تحليل اتجاه الكفاءة
      final efficiencyTrend = await _analyzeEfficiencyTrend(analysisWindow);
      if (efficiencyTrend != null) trends.add(efficiencyTrend);

      // تحليل اتجاه الأعطال
      final breakdownTrend = await _analyzeBreakdownTrend(analysisWindow);
      if (breakdownTrend != null) trends.add(breakdownTrend);

      return trends;
    } catch (e) {
      debugPrint('خطأ في تحليل الاتجاهات: $e');
      return [];
    }
  }

  /// تحليل اتجاه الإنتاج
  Future<TrendAnalysis?> _analyzeProductionTrend(int days) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));

      final productions = await _productionRepository.getAllProduction();
      final filteredProductions =
          productions
              .where(
                (p) => p.date.isAfter(startDate) && p.date.isBefore(endDate),
              )
              .toList();

      if (filteredProductions.length < 5) return null;

      // تجميع البيانات اليومية
      final dailyData = <DateTime, double>{};
      for (var production in filteredProductions) {
        final date = DateTime(
          production.date.year,
          production.date.month,
          production.date.day,
        );
        dailyData[date] = (dailyData[date] ?? 0) + production.partsProduced;
      }

      // تحويل إلى نقاط بيانات
      final dataPoints =
          dailyData.entries
              .map((e) => DataPoint(date: e.key, value: e.value))
              .toList()
            ..sort((a, b) => a.date.compareTo(b.date));

      // حساب الاتجاه باستخدام الانحدار الخطي البسيط
      final trendResult = _calculateLinearTrend(dataPoints);

      // التنبؤ للأيام القادمة
      final predictedData = _generatePredictedData(dataPoints, trendResult, 7);

      return TrendAnalysis(
        id: _uuid.v4(),
        metricName: 'إجمالي الإنتاج اليومي',
        direction: trendResult['direction'],
        trendStrength: trendResult['strength'],
        changeRate: trendResult['changeRate'],
        historicalData: dataPoints,
        predictedData: predictedData,
        analysisDate: DateTime.now(),
        analysisWindow: days,
      );
    } catch (e) {
      debugPrint('خطأ في تحليل اتجاه الإنتاج: $e');
      return null;
    }
  }

  /// تحليل اتجاه الكفاءة
  Future<TrendAnalysis?> _analyzeEfficiencyTrend(int days) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));

      final productions = await _productionRepository.getAllProduction();
      final filteredProductions =
          productions
              .where(
                (p) => p.date.isAfter(startDate) && p.date.isBefore(endDate),
              )
              .toList();

      if (filteredProductions.length < 5) return null;

      // حساب الكفاءة اليومية
      final dailyEfficiency = <DateTime, List<double>>{};
      for (var production in filteredProductions) {
        final date = DateTime(
          production.date.year,
          production.date.month,
          production.date.day,
        );
        final efficiency = (30.0 / production.cycleTime) * 100; // كفاءة بسيطة
        dailyEfficiency[date] = (dailyEfficiency[date] ?? [])..add(efficiency);
      }

      // حساب متوسط الكفاءة اليومية
      final dataPoints =
          dailyEfficiency.entries.map((e) {
              final avgEfficiency =
                  e.value.reduce((a, b) => a + b) / e.value.length;
              return DataPoint(date: e.key, value: avgEfficiency);
            }).toList()
            ..sort((a, b) => a.date.compareTo(b.date));

      // حساب الاتجاه
      final trendResult = _calculateLinearTrend(dataPoints);

      // التنبؤ
      final predictedData = _generatePredictedData(dataPoints, trendResult, 7);

      return TrendAnalysis(
        id: _uuid.v4(),
        metricName: 'كفاءة الإنتاج اليومية',
        direction: trendResult['direction'],
        trendStrength: trendResult['strength'],
        changeRate: trendResult['changeRate'],
        historicalData: dataPoints,
        predictedData: predictedData,
        analysisDate: DateTime.now(),
        analysisWindow: days,
      );
    } catch (e) {
      debugPrint('خطأ في تحليل اتجاه الكفاءة: $e');
      return null;
    }
  }

  /// تحليل اتجاه الأعطال
  Future<TrendAnalysis?> _analyzeBreakdownTrend(int days) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(Duration(days: days));

      final breakdowns = await _maintenanceRepository.getAllBreakdowns();
      final filteredBreakdowns =
          breakdowns
              .where(
                (b) =>
                    b.startTime.isAfter(startDate) &&
                    b.startTime.isBefore(endDate),
              )
              .toList();

      if (filteredBreakdowns.length < 3) return null;

      // تجميع الأعطال اليومية
      final dailyBreakdowns = <DateTime, int>{};
      for (var breakdown in filteredBreakdowns) {
        final date = DateTime(
          breakdown.startTime.year,
          breakdown.startTime.month,
          breakdown.startTime.day,
        );
        dailyBreakdowns[date] = (dailyBreakdowns[date] ?? 0) + 1;
      }

      // تحويل إلى نقاط بيانات
      final dataPoints =
          dailyBreakdowns.entries
              .map((e) => DataPoint(date: e.key, value: e.value.toDouble()))
              .toList()
            ..sort((a, b) => a.date.compareTo(b.date));

      // حساب الاتجاه
      final trendResult = _calculateLinearTrend(dataPoints);

      // التنبؤ
      final predictedData = _generatePredictedData(dataPoints, trendResult, 7);

      return TrendAnalysis(
        id: _uuid.v4(),
        metricName: 'عدد الأعطال اليومية',
        direction: trendResult['direction'],
        trendStrength: trendResult['strength'],
        changeRate: trendResult['changeRate'],
        historicalData: dataPoints,
        predictedData: predictedData,
        analysisDate: DateTime.now(),
        analysisWindow: days,
      );
    } catch (e) {
      debugPrint('خطأ في تحليل اتجاه الأعطال: $e');
      return null;
    }
  }

  /// حساب الاتجاه الخطي البسيط
  Map<String, dynamic> _calculateLinearTrend(List<DataPoint> data) {
    if (data.length < 2) {
      return {
        'direction': TrendDirection.stable,
        'strength': 0.0,
        'changeRate': 0.0,
        'slope': 0.0,
        'intercept': 0.0,
      };
    }

    // تحويل التواريخ إلى أرقام (أيام من نقطة البداية)
    final baseDate = data.first.date;
    final x =
        data.map((d) => d.date.difference(baseDate).inDays.toDouble()).toList();
    final y = data.map((d) => d.value).toList();

    final n = data.length;
    final sumX = x.reduce((a, b) => a + b);
    final sumY = y.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => x[i] * y[i]).reduce((a, b) => a + b);
    final sumX2 = x.map((xi) => xi * xi).reduce((a, b) => a + b);

    // حساب معامل الانحدار (slope)
    final slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
    final intercept = (sumY - slope * sumX) / n;

    // تحديد الاتجاه
    TrendDirection direction;
    if (slope.abs() < 0.1) {
      direction = TrendDirection.stable;
    } else if (slope > 0) {
      direction = TrendDirection.increasing;
    } else {
      direction = TrendDirection.decreasing;
    }

    // حساب قوة الاتجاه (R-squared مبسط)
    final yMean = sumY / n;
    final ssTotal = y
        .map((yi) => math.pow(yi - yMean, 2))
        .reduce((a, b) => a + b);
    final ssRes = List.generate(
      n,
      (i) => math.pow(y[i] - (slope * x[i] + intercept), 2),
    ).reduce((a, b) => a + b);
    final rSquared = ssTotal > 0 ? 1 - (ssRes / ssTotal) : 0.0;

    return {
      'direction': direction,
      'strength': (rSquared * 100).clamp(0.0, 100.0),
      'changeRate': slope,
      'slope': slope,
      'intercept': intercept,
    };
  }

  /// إنشاء بيانات متوقعة
  List<DataPoint> _generatePredictedData(
    List<DataPoint> historicalData,
    Map<String, dynamic> trendResult,
    int futureDays,
  ) {
    final predictedData = <DataPoint>[];

    if (historicalData.isEmpty) return predictedData;

    final lastDate = historicalData.last.date;
    final baseDate = historicalData.first.date;
    final slope = trendResult['slope'] as double;
    final intercept = trendResult['intercept'] as double;

    for (int i = 1; i <= futureDays; i++) {
      final futureDate = lastDate.add(Duration(days: i));
      final x = futureDate.difference(baseDate).inDays.toDouble();
      final predictedValue = slope * x + intercept;

      predictedData.add(
        DataPoint(
          date: futureDate,
          value: math.max(0, predictedValue), // تجنب القيم السالبة
        ),
      );
    }

    return predictedData;
  }

  /// إنشاء توقعات مستقبلية شاملة
  Future<List<FutureForecast>> generateFutureForecasts({
    int forecastDays = 30,
  }) async {
    try {
      final forecasts = <FutureForecast>[];

      // توقعات الإنتاج
      final productionForecast = await _generateProductionForecast(
        forecastDays,
      );
      if (productionForecast != null) forecasts.add(productionForecast);

      // توقعات الصيانة
      final maintenanceForecast = await _generateMaintenanceForecast(
        forecastDays,
      );
      if (maintenanceForecast != null) forecasts.add(maintenanceForecast);

      // توقعات الكفاءة
      final efficiencyForecast = await _generateEfficiencyForecast(
        forecastDays,
      );
      if (efficiencyForecast != null) forecasts.add(efficiencyForecast);

      return forecasts;
    } catch (e) {
      debugPrint('خطأ في إنشاء التوقعات المستقبلية: $e');
      return [];
    }
  }

  /// توقعات الإنتاج
  Future<FutureForecast?> _generateProductionForecast(int days) async {
    try {
      // تحليل البيانات التاريخية
      final productionTrend = await _analyzeProductionTrend(60);
      if (productionTrend == null) return null;

      // حساب التوقعات
      final predictions = <String, double>{};

      // إجمالي الإنتاج المتوقع
      final avgDailyProduction =
          productionTrend.historicalData.isNotEmpty
              ? productionTrend.historicalData
                      .map((d) => d.value)
                      .reduce((a, b) => a + b) /
                  productionTrend.historicalData.length
              : 0;

      final expectedTotalProduction =
          avgDailyProduction *
          days *
          (1 + productionTrend.changeRate * days / 100);
      predictions['إجمالي الإنتاج المتوقع'] = expectedTotalProduction;

      // معدل النمو المتوقع
      predictions['معدل النمو اليومي %'] = productionTrend.changeRate;

      // الطاقة الإنتاجية المطلوبة
      predictions['الطاقة المطلوبة %'] = math.min(
        100,
        expectedTotalProduction / (10000 * days) * 100,
      );

      return FutureForecast(
        id: _uuid.v4(),
        type: ForecastType.production,
        title: 'توقعات الإنتاج للـ $days يوم القادمة',
        description: 'توقعات مبنية على تحليل الاتجاهات التاريخية للإنتاج',
        forecastDate: DateTime.now().add(Duration(days: days)),
        forecastDays: days,
        predictions: predictions,
        accuracy: productionTrend.trendStrength,
        assumptions: [
          'استمرار نفس ظروف التشغيل الحالية',
          'عدم حدوث أعطال كبيرة',
          'توفر المواد الخام والعمالة',
        ],
        recommendations: _generateProductionRecommendations(predictions),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('خطأ في توقعات الإنتاج: $e');
      return null;
    }
  }

  /// توقعات الصيانة
  Future<FutureForecast?> _generateMaintenanceForecast(int days) async {
    try {
      // تحليل بيانات الأعطال
      final breakdownTrend = await _analyzeBreakdownTrend(60);
      if (breakdownTrend == null) return null;

      final predictions = <String, double>{};

      // عدد الأعطال المتوقعة
      final avgDailyBreakdowns =
          breakdownTrend.historicalData.isNotEmpty
              ? breakdownTrend.historicalData
                      .map((d) => d.value)
                      .reduce((a, b) => a + b) /
                  breakdownTrend.historicalData.length
              : 0;

      final expectedBreakdowns =
          avgDailyBreakdowns *
          days *
          (1 + breakdownTrend.changeRate * days / 100);
      predictions['عدد الأعطال المتوقعة'] = math.max(0, expectedBreakdowns);

      // تكلفة الصيانة المتوقعة
      final avgMaintenanceCost = 5000.0; // تكلفة افتراضية لكل عطل
      predictions['تكلفة الصيانة المتوقعة'] =
          expectedBreakdowns * avgMaintenanceCost;

      // ساعات التوقف المتوقعة
      final avgDowntime = 4.0; // ساعات افتراضية لكل عطل
      predictions['ساعات التوقف المتوقعة'] = expectedBreakdowns * avgDowntime;

      return FutureForecast(
        id: _uuid.v4(),
        type: ForecastType.maintenance,
        title: 'توقعات الصيانة للـ $days يوم القادمة',
        description: 'توقعات الأعطال والصيانة المطلوبة',
        forecastDate: DateTime.now().add(Duration(days: days)),
        forecastDays: days,
        predictions: predictions,
        accuracy: breakdownTrend.trendStrength,
        assumptions: [
          'استمرار نفس أنماط الاستخدام',
          'عدم تغيير برنامج الصيانة الوقائية',
          'نفس ظروف التشغيل البيئية',
        ],
        recommendations: _generateMaintenanceRecommendations(predictions),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('خطأ في توقعات الصيانة: $e');
      return null;
    }
  }

  /// توقعات الكفاءة
  Future<FutureForecast?> _generateEfficiencyForecast(int days) async {
    try {
      final efficiencyTrend = await _analyzeEfficiencyTrend(60);
      if (efficiencyTrend == null) return null;

      final predictions = <String, double>{};

      // الكفاءة المتوقعة
      final currentEfficiency =
          efficiencyTrend.historicalData.isNotEmpty
              ? efficiencyTrend.historicalData.last.value
              : 75.0;

      final expectedEfficiency =
          currentEfficiency + (efficiencyTrend.changeRate * days);
      predictions['الكفاءة المتوقعة %'] = math.max(
        0,
        math.min(100, expectedEfficiency),
      );

      // التحسن المتوقع
      predictions['التحسن المتوقع %'] = efficiencyTrend.changeRate * days;

      // الوفورات المتوقعة
      final efficiencyGain = expectedEfficiency - currentEfficiency;
      predictions['الوفورات المتوقعة'] =
          efficiencyGain * 1000; // وفورات افتراضية

      return FutureForecast(
        id: _uuid.v4(),
        type: ForecastType.efficiency,
        title: 'توقعات الكفاءة للـ $days يوم القادمة',
        description: 'توقعات تطور كفاءة الإنتاج',
        forecastDate: DateTime.now().add(Duration(days: days)),
        forecastDays: days,
        predictions: predictions,
        accuracy: efficiencyTrend.trendStrength,
        assumptions: [
          'استمرار برامج التحسين الحالية',
          'عدم تغيير العمليات الإنتاجية',
          'استقرار مهارات العمالة',
        ],
        recommendations: _generateEfficiencyRecommendations(predictions),
        createdAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('خطأ في توقعات الكفاءة: $e');
      return null;
    }
  }

  /// إنشاء توصيات الإنتاج
  List<String> _generateProductionRecommendations(
    Map<String, double> predictions,
  ) {
    final recommendations = <String>[];

    final totalProduction = predictions['إجمالي الإنتاج المتوقع'] ?? 0;
    final growthRate = predictions['معدل النمو اليومي %'] ?? 0;
    final capacityRequired = predictions['الطاقة المطلوبة %'] ?? 0;

    if (growthRate > 5) {
      recommendations.add(
        'التحضير لزيادة الطلب - قد تحتاج لزيادة الطاقة الإنتاجية',
      );
    } else if (growthRate < -5) {
      recommendations.add('مراجعة استراتيجية الإنتاج - هناك انخفاض في الطلب');
    }

    if (capacityRequired > 90) {
      recommendations.add('التخطيط لتوسيع الطاقة الإنتاجية أو تحسين الكفاءة');
    }

    if (totalProduction > 0) {
      recommendations.add('ضمان توفر المواد الخام والعمالة للفترة القادمة');
    }

    return recommendations;
  }

  /// إنشاء توصيات الصيانة
  List<String> _generateMaintenanceRecommendations(
    Map<String, double> predictions,
  ) {
    final recommendations = <String>[];

    final expectedBreakdowns = predictions['عدد الأعطال المتوقعة'] ?? 0;
    final maintenanceCost = predictions['تكلفة الصيانة المتوقعة'] ?? 0;
    final downtime = predictions['ساعات التوقف المتوقعة'] ?? 0;

    if (expectedBreakdowns > 10) {
      recommendations.add('تكثيف برنامج الصيانة الوقائية لتقليل الأعطال');
    }

    if (maintenanceCost > 50000) {
      recommendations.add('مراجعة ميزانية الصيانة وتخصيص موارد إضافية');
    }

    if (downtime > 50) {
      recommendations.add('تحسين سرعة الاستجابة للأعطال وتوفير قطع الغيار');
    }

    recommendations.add('مراقبة مستمرة للماكينات عالية المخاطر');

    return recommendations;
  }

  /// إنشاء توصيات الكفاءة
  List<String> _generateEfficiencyRecommendations(
    Map<String, double> predictions,
  ) {
    final recommendations = <String>[];

    final expectedEfficiency = predictions['الكفاءة المتوقعة %'] ?? 0;
    final improvement = predictions['التحسن المتوقع %'] ?? 0;

    if (expectedEfficiency < 70) {
      recommendations.add('تطبيق برامج تحسين الكفاءة وتدريب العمالة');
    }

    if (improvement < 0) {
      recommendations.add(
        'مراجعة العمليات الإنتاجية وتحديد أسباب انخفاض الكفاءة',
      );
    } else if (improvement > 0) {
      recommendations.add('الاستمرار في برامج التحسين الحالية');
    }

    recommendations.add('مراقبة مؤشرات الكفاءة بشكل دوري');

    return recommendations;
  }

  /// تحليل الارتباطات بين المتغيرات
  Future<List<CorrelationAnalysis>> analyzeCorrelations() async {
    try {
      final correlations = <CorrelationAnalysis>[];

      // جمع البيانات
      final data = await _gatherCorrelationData();

      // تحليل الارتباط بين الإنتاج والكفاءة
      final productionEfficiencyCorr = _calculateCorrelation(
        data['production'] ?? [],
        data['efficiency'] ?? [],
        'الإنتاج',
        'الكفاءة',
      );
      if (productionEfficiencyCorr != null) {
        correlations.add(productionEfficiencyCorr);
      }

      // تحليل الارتباط بين الأعطال والكفاءة
      final breakdownEfficiencyCorr = _calculateCorrelation(
        data['breakdowns'] ?? [],
        data['efficiency'] ?? [],
        'الأعطال',
        'الكفاءة',
      );
      if (breakdownEfficiencyCorr != null) {
        correlations.add(breakdownEfficiencyCorr);
      }

      // تحليل الارتباط بين ساعات التشغيل والأعطال
      final operatingBreakdownCorr = _calculateCorrelation(
        data['operatingHours'] ?? [],
        data['breakdowns'] ?? [],
        'ساعات التشغيل',
        'الأعطال',
      );
      if (operatingBreakdownCorr != null) {
        correlations.add(operatingBreakdownCorr);
      }

      return correlations;
    } catch (e) {
      debugPrint('خطأ في تحليل الارتباطات: $e');
      return [];
    }
  }

  /// جمع بيانات الارتباط
  Future<Map<String, List<double>>> _gatherCorrelationData() async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(const Duration(days: 60));

    // جمع بيانات الإنتاج
    final productions = await _productionRepository.getAllProduction();
    final filteredProductions =
        productions
            .where((p) => p.date.isAfter(startDate) && p.date.isBefore(endDate))
            .toList();

    // جمع بيانات الأعطال
    final breakdowns = await _maintenanceRepository.getAllBreakdowns();
    final filteredBreakdowns =
        breakdowns
            .where(
              (b) =>
                  b.startTime.isAfter(startDate) &&
                  b.startTime.isBefore(endDate),
            )
            .toList();

    // تجميع البيانات اليومية
    final dailyData = <DateTime, Map<String, double>>{};

    // إضافة بيانات الإنتاج
    for (var production in filteredProductions) {
      final date = DateTime(
        production.date.year,
        production.date.month,
        production.date.day,
      );
      dailyData[date] = dailyData[date] ?? {};
      dailyData[date]!['production'] =
          (dailyData[date]!['production'] ?? 0) + production.partsProduced;
      dailyData[date]!['efficiency'] = (30.0 / production.cycleTime) * 100;
      dailyData[date]!['operatingHours'] =
          (dailyData[date]!['operatingHours'] ?? 0) +
          (production.partsProduced * production.cycleTime / 3600);
    }

    // إضافة بيانات الأعطال
    for (var breakdown in filteredBreakdowns) {
      final date = DateTime(
        breakdown.startTime.year,
        breakdown.startTime.month,
        breakdown.startTime.day,
      );
      dailyData[date] = dailyData[date] ?? {};
      dailyData[date]!['breakdowns'] =
          (dailyData[date]!['breakdowns'] ?? 0) + 1;
    }

    // تحويل إلى قوائم
    final result = <String, List<double>>{
      'production': [],
      'efficiency': [],
      'operatingHours': [],
      'breakdowns': [],
    };

    for (var dayData in dailyData.values) {
      result['production']!.add(dayData['production'] ?? 0);
      result['efficiency']!.add(dayData['efficiency'] ?? 0);
      result['operatingHours']!.add(dayData['operatingHours'] ?? 0);
      result['breakdowns']!.add(dayData['breakdowns'] ?? 0);
    }

    return result;
  }

  /// حساب معامل الارتباط
  CorrelationAnalysis? _calculateCorrelation(
    List<double> x,
    List<double> y,
    String var1,
    String var2,
  ) {
    if (x.length != y.length || x.length < 5) return null;

    final n = x.length;
    final sumX = x.reduce((a, b) => a + b);
    final sumY = y.reduce((a, b) => a + b);
    final sumXY = List.generate(n, (i) => x[i] * y[i]).reduce((a, b) => a + b);
    final sumX2 = x.map((xi) => xi * xi).reduce((a, b) => a + b);
    final sumY2 = y.map((yi) => yi * yi).reduce((a, b) => a + b);

    final numerator = n * sumXY - sumX * sumY;
    final denominator = math.sqrt(
      (n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY),
    );

    if (denominator == 0) return null;

    final correlation = numerator / denominator;

    // تحديد قوة الارتباط
    final absCorr = correlation.abs();
    CorrelationStrength strength;
    if (absCorr < 0.2) {
      strength = CorrelationStrength.veryWeak;
    } else if (absCorr < 0.4) {
      strength = CorrelationStrength.weak;
    } else if (absCorr < 0.6) {
      strength = CorrelationStrength.moderate;
    } else if (absCorr < 0.8) {
      strength = CorrelationStrength.strong;
    } else {
      strength = CorrelationStrength.veryStrong;
    }

    // تحديد نوع الارتباط
    CorrelationType type;
    if (correlation > 0.1) {
      type = CorrelationType.positive;
    } else if (correlation < -0.1) {
      type = CorrelationType.negative;
    } else {
      type = CorrelationType.noCorrelation;
    }

    return CorrelationAnalysis(
      id: _uuid.v4(),
      variable1: var1,
      variable2: var2,
      correlationCoefficient: correlation,
      strength: strength,
      type: type,
      pValue: 0.05, // قيمة افتراضية
      isSignificant: absCorr > 0.3,
      sampleSize: n,
      analysisDate: DateTime.now(),
    );
  }
}
