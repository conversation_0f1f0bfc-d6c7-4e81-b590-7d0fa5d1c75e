import 'package:flutter/foundation.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/di/dependency_injection.dart';
import 'package:mostafa_final/presentation/controllers/performance_test_controller.dart';
import 'package:mostafa_final/utils/performance_monitor.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  // تهيئة حقن التبعيات
  setUpAll(() {
    DependencyInjection.init();
  });

  group('اختبار أداة مراقبة الأداء', () {
    late PerformanceMonitor performanceMonitor;

    setUp(() {
      // تهيئة أداة مراقبة الأداء
      performanceMonitor = PerformanceMonitor();
    });

    test('قياس زمن استجابة واجهة المستخدم', () async {
      // قياس زمن استجابة واجهة المستخدم
      final responseTime = await performanceMonitor.measureUIResponseTime(
        () async {
          // محاكاة عملية تستغرق 100 مللي ثانية
          await Future.delayed(const Duration(milliseconds: 100));
        },
        'test_screen',
      );

      // التحقق من أن زمن الاستجابة أكبر من أو يساوي 100 مللي ثانية
      expect(responseTime, greaterThanOrEqualTo(100.0));

      // طباعة زمن الاستجابة
      debugPrint('زمن استجابة واجهة المستخدم: $responseTime ms');
    });

    test('قياس أداء استعلام قاعدة البيانات', () async {
      // قياس أداء استعلام قاعدة البيانات
      final queryTime = await performanceMonitor.measureDatabaseQuery(() async {
        // محاكاة استعلام يستغرق 50 مللي ثانية
        await Future.delayed(const Duration(milliseconds: 50));
        return [
          {'result': 'test'},
        ];
      }, 'test_query');

      // التحقق من أن زمن الاستعلام أكبر من أو يساوي 50 مللي ثانية
      expect(queryTime, greaterThanOrEqualTo(50.0));

      // طباعة زمن الاستعلام
      debugPrint('زمن استعلام قاعدة البيانات: $queryTime ms');
    });

    test('إنشاء تقرير أداء', () async {
      // قياس بعض العمليات
      await performanceMonitor.measureUIResponseTime(
        () async => await Future.delayed(const Duration(milliseconds: 100)),
        'screen_1',
      );

      await performanceMonitor.measureUIResponseTime(
        () async => await Future.delayed(const Duration(milliseconds: 150)),
        'screen_2',
      );

      await performanceMonitor.measureDatabaseQuery(() async {
        await Future.delayed(const Duration(milliseconds: 50));
        return [
          {'result': 'test'},
        ];
      }, 'query_1');

      // إنشاء تقرير أداء
      final reportPath = await performanceMonitor.generatePerformanceReport();

      // التحقق من أن مسار التقرير غير فارغ
      expect(reportPath, isNotEmpty);

      // طباعة مسار التقرير
      debugPrint('تم إنشاء تقرير الأداء في: $reportPath');
    });
  });

  group('اختبار وحدة تحكم اختبارات الأداء', () {
    late PerformanceTestController controller;

    setUp(() {
      // تهيئة وحدة تحكم اختبارات الأداء
      controller = Get.put(PerformanceTestController());
    });

    tearDown(() {
      // إزالة وحدة تحكم اختبارات الأداء
      Get.delete<PerformanceTestController>();
    });

    test('بدء وإيقاف مراقبة الأداء', () {
      // بدء مراقبة الأداء
      controller.startMonitoring();

      // التحقق من أن المراقبة قيد التشغيل
      expect(controller.isMonitoring.value, isTrue);

      // إيقاف مراقبة الأداء
      controller.stopMonitoring();

      // التحقق من أن المراقبة متوقفة
      expect(controller.isMonitoring.value, isFalse);
    });

    test('قياس زمن استجابة واجهة المستخدم', () async {
      // قياس زمن استجابة واجهة المستخدم
      await controller.measureUIResponseTime('test_screen');

      // التحقق من وجود نتائج
      expect(controller.uiTestResults.containsKey('test_screen'), isTrue);

      // طباعة زمن الاستجابة
      debugPrint(
        'زمن استجابة واجهة المستخدم: ${controller.formatResponseTime(controller.uiTestResults['test_screen'])}',
      );
    });

    test('إعادة تعيين بيانات المراقبة', () async {
      // قياس زمن استجابة واجهة المستخدم
      await controller.measureUIResponseTime('test_screen');

      // التحقق من وجود نتائج
      expect(controller.uiTestResults.isNotEmpty, isTrue);

      // إعادة تعيين بيانات المراقبة
      controller.resetMonitoringData();

      // التحقق من عدم وجود نتائج
      expect(controller.uiTestResults.isEmpty, isTrue);
    });
  });
}
