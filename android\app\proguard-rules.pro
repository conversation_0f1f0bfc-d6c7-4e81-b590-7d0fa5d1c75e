# Keep printing library
-keep class net.nfet.flutter.printing.** { *; }

# Keep pdf library
-keep class com.github.DavBfr.dart_pdf.** { *; }

# Keep R8 from stripping interface information
-keep class * implements android.os.Parcelable {
  public static final android.os.Parcelable$Creator *;
}

# Keep lStar attribute
-keepclassmembers class **.R$* {
    public static <fields>;
}

# Keep printing resources
-keep class **.R$attr { *; }
