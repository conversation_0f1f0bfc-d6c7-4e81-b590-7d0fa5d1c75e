import 'dart:async';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path_provider/path_provider.dart';
import 'package:logger/logger.dart';

class DatabaseHelper {
  static const _databaseName = "factory_management.db";
  static const _databaseVersion = 8; // زيادة رقم الإصدار لإضافة الفهارس

  final _logger = Logger();

  // جداول قاعدة البيانات
  static const String tableRawMaterials = 'raw_materials';
  static const String tableRawMaterialReceipts = 'raw_material_receipts';
  static const String tableMachines = 'machines';
  static const String tableMolds = 'molds';
  static const String tableProduction = 'production';
  static const String tableOrders = 'orders';
  static const String tableOrderItems = 'order_items';
  static const String tableProducts = 'products';
  static const String tableProductComponents = 'product_components';
  static const String tableProductionPlans = 'production_plans';
  static const String tableProductionPlanItems = 'production_plan_items';
  static const String tableMoldChangeHistory = 'mold_change_history';

  // جداول التجميع والعمال
  static const String tableWorkers = 'workers';
  static const String tableAssembly = 'assembly';
  static const String tableAssemblyComponents = 'assembly_components';

  // جداول المخزون الجديدة
  static const String tableInventory = 'inventory';
  static const String tableInventoryMovements = 'inventory_movements';
  static const String tableInventoryAlerts = 'inventory_alerts';
  static const String tableInventoryHistory = 'inventory_history';

  // جداول المبيعات
  static const String tableSales = 'sales';
  static const String tableSaleItems = 'sale_items';

  // نمط Singleton
  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  // فتح قاعدة البيانات/إنشاؤها إذا لم تكن موجودة
  _initDatabase() async {
    var documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, _databaseName);
    return await openDatabase(
      path,
      version: _databaseVersion,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  // إنشاء جداول قاعدة البيانات
  Future _onCreate(Database db, int version) async {
    // جدول المواد الخام
    await db.execute('''
      CREATE TABLE $tableRawMaterials (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        availableQuantity REAL NOT NULL,
        costPerKg REAL NOT NULL,
        color TEXT NOT NULL
      )
    ''');

    // جدول استلام المواد الخام
    await db.execute('''
      CREATE TABLE $tableRawMaterialReceipts (
        id TEXT PRIMARY KEY,
        rawMaterialId TEXT NOT NULL,
        date TEXT NOT NULL,
        quantity REAL NOT NULL,
        pricePerKg REAL NOT NULL,
        FOREIGN KEY (rawMaterialId) REFERENCES $tableRawMaterials (id)
      )
    ''');

    // جدول الماكينات
    await db.execute('''
      CREATE TABLE $tableMachines (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        model TEXT NOT NULL,
        powerConsumption REAL NOT NULL,
        status TEXT NOT NULL,
        currentMoldId TEXT,
        lastMoldChange TEXT
      )
    ''');

    // جدول الإسطمبات
    await db.execute('''
      CREATE TABLE $tableMolds (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        productCode TEXT NOT NULL,
        status TEXT NOT NULL,
        currentMachineId TEXT,
        cavityCount INTEGER NOT NULL,
        singlePartWeight REAL NOT NULL,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول تاريخ تغيير الإسطمبات
    await db.execute('''
      CREATE TABLE $tableMoldChangeHistory (
        id TEXT PRIMARY KEY,
        machineId TEXT NOT NULL,
        moldId TEXT NOT NULL,
        changeDate TEXT NOT NULL,
        previousMoldId TEXT,
        notes TEXT,
        FOREIGN KEY (machineId) REFERENCES $tableMachines (id),
        FOREIGN KEY (moldId) REFERENCES $tableMolds (id)
      )
    ''');

    // جدول الإنتاج
    await db.execute('''
      CREATE TABLE $tableProduction (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        machineId TEXT NOT NULL,
        moldId TEXT NOT NULL,
        rawMaterialId TEXT NOT NULL,
        rawMaterialUsed REAL NOT NULL,
        partsProduced INTEGER NOT NULL,
        cycleTime REAL NOT NULL,
        electricityCost REAL NOT NULL,
        operatorCost REAL NOT NULL,
        shiftsCount REAL DEFAULT 1.0,
        FOREIGN KEY (machineId) REFERENCES $tableMachines (id),
        FOREIGN KEY (moldId) REFERENCES $tableMolds (id),
        FOREIGN KEY (rawMaterialId) REFERENCES $tableRawMaterials (id)
      )
    ''');

    // جدول الطلبيات
    await db.execute('''
      CREATE TABLE $tableOrders (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        transportCost REAL NOT NULL,
        shippingCost REAL NOT NULL,
        clearanceCost REAL NOT NULL,
        customsDuty REAL NOT NULL,
        exchangeRate REAL NOT NULL,
        currency TEXT NOT NULL
      )
    ''');

    // جدول عناصر الطلبية
    await db.execute('''
      CREATE TABLE $tableOrderItems (
        id TEXT PRIMARY KEY,
        orderId TEXT NOT NULL,
        accessoryId TEXT NOT NULL,
        name TEXT NOT NULL,
        price REAL NOT NULL,
        quantity INTEGER NOT NULL,
        weight REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES $tableOrders (id)
      )
    ''');

    // جدول المنتجات
    await db.execute('''
      CREATE TABLE $tableProducts (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        assemblyTime REAL NOT NULL,
        packagingCost REAL NOT NULL
      )
    ''');

    // جدول مكونات المنتج
    await db.execute('''
      CREATE TABLE $tableProductComponents (
        id TEXT PRIMARY KEY,
        productId TEXT NOT NULL,
        name TEXT NOT NULL,
        partId TEXT NOT NULL,
        type INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        cost REAL NOT NULL,
        FOREIGN KEY (productId) REFERENCES $tableProducts (id)
      )
    ''');

    // جدول خطط الإنتاج
    await db.execute('''
      CREATE TABLE $tableProductionPlans (
        id TEXT PRIMARY KEY,
        startDate TEXT NOT NULL,
        endDate TEXT NOT NULL,
        status TEXT NOT NULL
      )
    ''');

    // جدول عناصر خطة الإنتاج
    await db.execute('''
      CREATE TABLE $tableProductionPlanItems (
        id TEXT PRIMARY KEY,
        planId TEXT NOT NULL,
        productId TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        priority INTEGER NOT NULL,
        plannedStart TEXT,
        plannedEnd TEXT,
        completedQuantity INTEGER NOT NULL,
        FOREIGN KEY (planId) REFERENCES $tableProductionPlans (id),
        FOREIGN KEY (productId) REFERENCES $tableProducts (id)
      )
    ''');

    // إنشاء جداول المخزون الجديدة
    await _createInventoryTables(db);

    // إنشاء جداول التجميع والعمال
    await _createAssemblyTables(db);

    // إنشاء جداول المبيعات
    await _createSalesTables(db);
  }

  // ترقية قاعدة البيانات
  Future _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // إنشاء جداول المخزون في حالة الترقية من إصدار سابق
      await _createInventoryTables(db);
    }

    if (oldVersion < 3) {
      // إنشاء جدول تاريخ تغيير الإسطمبات
      await db.execute('''
        CREATE TABLE $tableMoldChangeHistory (
          id TEXT PRIMARY KEY,
          machineId TEXT NOT NULL,
          moldId TEXT NOT NULL,
          changeDate TEXT NOT NULL,
          previousMoldId TEXT,
          notes TEXT,
          FOREIGN KEY (machineId) REFERENCES $tableMachines (id),
          FOREIGN KEY (moldId) REFERENCES $tableMolds (id)
        )
      ''');
    }

    if (oldVersion < 4) {
      // إنشاء جداول التجميع والعمال
      await _createAssemblyTables(db);
    }

    if (oldVersion < 5) {
      // إنشاء جداول المبيعات
      await _createSalesTables(db);
    }

    if (oldVersion < 6) {
      // إضافة عمود createdAt إلى جدول الإسطمبات
      try {
        await db.execute('''
          ALTER TABLE $tableMolds ADD COLUMN createdAt TEXT
        ''');

        // تحديث القيم الموجودة بتاريخ حالي
        await db.execute(
          '''
          UPDATE $tableMolds SET createdAt = ?
        ''',
          [DateTime.now().toIso8601String()],
        );
      } catch (e) {
        _logger.e('خطأ أثناء ترقية جدول الإسطمبات: $e');
      }
    }

    if (oldVersion < 7) {
      // إضافة عمود shiftsCount إلى جدول الإنتاج
      try {
        await db.execute('''
          ALTER TABLE $tableProduction ADD COLUMN shiftsCount REAL DEFAULT 1.0
        ''');

        // تحديث القيم الموجودة بالقيمة الافتراضية (وردية واحدة)
        await db.execute('''
          UPDATE $tableProduction SET shiftsCount = 1.0
        ''');

        _logger.e('تم ترقية جدول الإنتاج بنجاح وإضافة عمود عدد الورديات');
      } catch (e) {
        _logger.e('خطأ أثناء ترقية جدول الإنتاج: $e');
      }
    }

    if (oldVersion < 8) {
      // إضافة فهارس لتحسين أداء قاعدة البيانات
      try {
        await _createDatabaseIndexes(db);
        _logger.i('تم إنشاء الفهارس بنجاح لتحسين أداء قاعدة البيانات');
      } catch (e) {
        _logger.e('خطأ أثناء إنشاء الفهارس: $e');
      }
    }
  }

  // إنشاء جداول المخزون
  Future _createInventoryTables(Database db) async {
    // جدول المخزون
    await db.execute('''
      CREATE TABLE $tableInventory (
        id TEXT PRIMARY KEY,
        itemId TEXT NOT NULL,
        itemType TEXT NOT NULL,
        itemName TEXT NOT NULL,
        itemCode TEXT NOT NULL,
        currentQuantity REAL NOT NULL,
        minimumLevel REAL NOT NULL,
        reorderLevel REAL NOT NULL,
        unit TEXT NOT NULL,
        unitCost REAL NOT NULL,
        lastUpdate TEXT NOT NULL,
        status INTEGER NOT NULL
      )
    ''');

    // جدول حركات المخزون
    await db.execute('''
      CREATE TABLE $tableInventoryMovements (
        id TEXT PRIMARY KEY,
        inventoryId TEXT NOT NULL,
        date TEXT NOT NULL,
        type INTEGER NOT NULL,
        quantity REAL NOT NULL,
        costPerUnit REAL NOT NULL,
        referenceType TEXT NOT NULL,
        referenceId TEXT NOT NULL,
        notes TEXT NOT NULL,
        FOREIGN KEY (inventoryId) REFERENCES $tableInventory (id)
      )
    ''');

    // جدول تنبيهات المخزون
    await db.execute('''
      CREATE TABLE $tableInventoryAlerts (
        id TEXT PRIMARY KEY,
        inventoryId TEXT NOT NULL,
        itemName TEXT NOT NULL,
        itemCode TEXT NOT NULL,
        date TEXT NOT NULL,
        alertType TEXT NOT NULL,
        currentLevel REAL NOT NULL,
        thresholdLevel REAL NOT NULL,
        isResolved INTEGER NOT NULL,
        FOREIGN KEY (inventoryId) REFERENCES $tableInventory (id)
      )
    ''');
  }

  // إنشاء جداول التجميع والعمال
  Future _createAssemblyTables(Database db) async {
    // جدول العمال
    await db.execute('''
      CREATE TABLE $tableWorkers (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        code TEXT NOT NULL,
        jobTitle TEXT NOT NULL,
        hourlyRate REAL NOT NULL,
        department TEXT NOT NULL,
        isActive INTEGER NOT NULL
      )
    ''');

    // جدول التجميع
    await db.execute('''
      CREATE TABLE $tableAssembly (
        id TEXT PRIMARY KEY,
        productId TEXT NOT NULL,
        date TEXT NOT NULL,
        workerId TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        laborCostPerMinute REAL NOT NULL,
        totalAssemblyCost REAL NOT NULL,
        totalPackagingCost REAL NOT NULL,
        status TEXT NOT NULL,
        FOREIGN KEY (productId) REFERENCES $tableProducts (id),
        FOREIGN KEY (workerId) REFERENCES $tableWorkers (id)
      )
    ''');

    // جدول مكونات التجميع
    await db.execute('''
      CREATE TABLE $tableAssemblyComponents (
        id TEXT PRIMARY KEY,
        assemblyId TEXT NOT NULL,
        componentId TEXT NOT NULL,
        name TEXT NOT NULL,
        partId TEXT NOT NULL,
        componentType INTEGER NOT NULL,
        quantity INTEGER NOT NULL,
        cost REAL NOT NULL,
        FOREIGN KEY (assemblyId) REFERENCES $tableAssembly (id)
      )
    ''');
  }

  // إنشاء جداول المبيعات
  Future _createSalesTables(Database db) async {
    // جدول المبيعات
    await db.execute('''
      CREATE TABLE $tableSales (
        id TEXT PRIMARY KEY,
        invoiceNumber TEXT NOT NULL,
        date TEXT NOT NULL,
        customerName TEXT NOT NULL,
        customerPhone TEXT NOT NULL,
        shippingAddress TEXT NOT NULL,
        totalAmount REAL NOT NULL,
        shippingStatus TEXT NOT NULL
      )
    ''');

    // جدول عناصر المبيعات
    await db.execute('''
      CREATE TABLE $tableSaleItems (
        id TEXT PRIMARY KEY,
        saleId TEXT NOT NULL,
        productId TEXT NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        unitPrice REAL NOT NULL,
        totalPrice REAL NOT NULL,
        FOREIGN KEY (saleId) REFERENCES $tableSales (id) ON DELETE CASCADE
      )
    ''');
  }

  // إدراج صف في الجدول
  Future<String> insert(String table, Map<String, dynamic> row) async {
    Database db = await instance.database;
    await db.insert(table, row);
    return row['id'];
  }

  // استعلام عن جميع الصفوف
  Future<List<Map<String, dynamic>>> queryAllRows(String table) async {
    Database db = await instance.database;
    return await db.query(table);
  }

  // استعلام عن صف بمعرف معين
  Future<Map<String, dynamic>?> queryRow(String table, String id) async {
    Database db = await instance.database;
    List<Map<String, dynamic>> result = await db.query(
      table,
      where: 'id = ?',
      whereArgs: [id],
    );
    return result.isNotEmpty ? result.first : null;
  }

  // تحديث صف
  Future<int> update(String table, Map<String, dynamic> row) async {
    Database db = await instance.database;
    String id = row['id'];
    return await db.update(table, row, where: 'id = ?', whereArgs: [id]);
  }

  // حذف صف
  Future<int> delete(String table, String id) async {
    Database db = await instance.database;
    return await db.delete(table, where: 'id = ?', whereArgs: [id]);
  }

  // استعلام مخصص
  Future<List<Map<String, dynamic>>> rawQuery(
    String query, [
    List<dynamic>? arguments,
  ]) async {
    Database db = await instance.database;
    return await db.rawQuery(query, arguments);
  }

  // إنشاء فهارس لتحسين أداء قاعدة البيانات
  Future _createDatabaseIndexes(Database db) async {
    _logger.i('بدء إنشاء الفهارس لتحسين أداء قاعدة البيانات...');

    // فهارس جدول الإنتاج
    await db.execute(
      'CREATE INDEX idx_production_date ON $tableProduction (date)',
    );
    await db.execute(
      'CREATE INDEX idx_production_machine ON $tableProduction (machineId)',
    );
    await db.execute(
      'CREATE INDEX idx_production_mold ON $tableProduction (moldId)',
    );
    await db.execute(
      'CREATE INDEX idx_production_raw_material ON $tableProduction (rawMaterialId)',
    );

    // فهارس جدول المخزون
    await db.execute(
      'CREATE INDEX idx_inventory_item_type ON $tableInventory (itemType)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_item_id ON $tableInventory (itemId)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_status ON $tableInventory (status)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_code ON $tableInventory (itemCode)',
    );

    // فهارس جدول حركات المخزون
    await db.execute(
      'CREATE INDEX idx_inventory_movements_inventory_id ON $tableInventoryMovements (inventoryId)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_movements_date ON $tableInventoryMovements (date)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_movements_type ON $tableInventoryMovements (type)',
    );
    await db.execute(
      'CREATE INDEX idx_inventory_movements_reference ON $tableInventoryMovements (referenceType, referenceId)',
    );

    // فهارس جدول المبيعات
    await db.execute('CREATE INDEX idx_sales_date ON $tableSales (date)');
    await db.execute(
      'CREATE INDEX idx_sales_status ON $tableSales (shippingStatus)',
    );
    await db.execute(
      'CREATE INDEX idx_sales_customer ON $tableSales (customerName)',
    );

    // فهارس جدول عناصر المبيعات
    await db.execute(
      'CREATE INDEX idx_sale_items_sale_id ON $tableSaleItems (saleId)',
    );
    await db.execute(
      'CREATE INDEX idx_sale_items_product_id ON $tableSaleItems (productId)',
    );

    // فهارس جدول تغيير الإسطمبات
    await db.execute(
      'CREATE INDEX idx_mold_change_history_machine ON $tableMoldChangeHistory (machineId)',
    );
    await db.execute(
      'CREATE INDEX idx_mold_change_history_mold ON $tableMoldChangeHistory (moldId)',
    );
    await db.execute(
      'CREATE INDEX idx_mold_change_history_date ON $tableMoldChangeHistory (changeDate)',
    );

    // فهارس جدول التجميع
    await db.execute(
      'CREATE INDEX idx_assembly_product ON $tableAssembly (productId)',
    );
    await db.execute('CREATE INDEX idx_assembly_date ON $tableAssembly (date)');
    await db.execute(
      'CREATE INDEX idx_assembly_worker ON $tableAssembly (workerId)',
    );

    _logger.i('تم إنشاء الفهارس بنجاح');
  }
}
