import 'package:intl/intl.dart';

class FormatUtils {
  // تنسيق العملة
  static String formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      symbol: '',
      decimalDigits: 2,
      locale: 'ar',
    );
    return '${formatter.format(amount)} جنيه';
  }

  // تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    final formatter = NumberFormat.percentPattern('ar');
    return formatter.format(percentage / 100);
  }

  // تنسيق التاريخ
  static String formatDate(DateTime date) {
    final formatter = DateFormat('yyyy-MM-dd', 'ar');
    return formatter.format(date);
  }

  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    final formatter = DateFormat('yyyy-MM-dd HH:mm', 'ar');
    return formatter.format(dateTime);
  }

  // تنسيق الرقم
  static String formatNumber(num number, {int fractionDigits = 2}) {
    final formatter =
        NumberFormat.decimalPattern('ar')
          ..minimumFractionDigits = fractionDigits
          ..maximumFractionDigits = fractionDigits;
    return formatter.format(number);
  }

  // تنسيق الوزن
  static String formatWeight(double weight) {
    return '${formatNumber(weight)} كجم';
  }
}
