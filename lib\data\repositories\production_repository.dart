import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:uuid/uuid.dart';

class ProductionRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final RawMaterialRepository _rawMaterialRepository = RawMaterialRepository();
  final _uuid = const Uuid();

  // إنشاء سجل إنتاج جديد
  Future<String> createProduction(Production production) async {
    // إنشاء معرف جديد للإنتاج
    final productionWithId = Production(
      id: _uuid.v4(),
      date: production.date,
      machineId: production.machineId,
      moldId: production.moldId,
      rawMaterialId: production.rawMaterialId,
      rawMaterialUsed: production.rawMaterialUsed,
      partsProduced: production.partsProduced,
      cycleTime: production.cycleTime,
      electricityCost: production.electricityCost,
      operatorCost: production.operatorCost,
    );

    // خصم المواد الخام المستخدمة من المخزون
    await _rawMaterialRepository.updateRawMaterialQuantity(
      production.rawMaterialId,
      -production.rawMaterialUsed,
    );

    // حفظ سجل الإنتاج
    return await _databaseHelper.insert(
      DatabaseHelper.tableProduction,
      productionWithId.toMap(),
    );
  }

  // الحصول على جميع سجلات الإنتاج
  Future<List<Production>> getAllProduction() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableProduction,
    );

    return maps.map((map) => Production.fromMap(map)).toList();
  }

  // الحصول على سجل إنتاج بمعرف معين
  Future<Production?> getProductionById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableProduction,
      id,
    );

    if (map == null) {
      return null;
    }

    return Production.fromMap(map);
  }

  // تحديث سجل إنتاج
  Future<int> updateProduction(Production production) async {
    return await _databaseHelper.update(
      DatabaseHelper.tableProduction,
      production.toMap(),
    );
  }

  // حذف سجل إنتاج
  Future<int> deleteProduction(String id) async {
    return await _databaseHelper.delete(DatabaseHelper.tableProduction, id);
  }

  // الحصول على الإنتاج لتاريخ معين
  Future<List<Map<String, dynamic>>> getProductionByDate(DateTime date) async {
    String formattedDate = date.toIso8601String().split('T')[0];

    String query = '''
      SELECT p.*, 
             m.name as machineName, 
             mold.name as moldName, 
             mold.cavityCount, 
             mold.singlePartWeight,
             rm.name as rawMaterialName,
             rm.color as rawMaterialColor
      FROM ${DatabaseHelper.tableProduction} p
      JOIN ${DatabaseHelper.tableMachines} m ON p.machineId = m.id
      JOIN ${DatabaseHelper.tableMolds} mold ON p.moldId = mold.id
      JOIN ${DatabaseHelper.tableRawMaterials} rm ON p.rawMaterialId = rm.id
      WHERE date(p.date) = ?
      ORDER BY p.date DESC
    ''';

    return await _databaseHelper.rawQuery(query, [formattedDate]);
  }

  // الحصول على الإنتاج لماكينة معينة
  Future<List<Map<String, dynamic>>> getProductionByMachine(
    String machineId,
  ) async {
    String query = '''
      SELECT p.*, 
             mold.name as moldName, 
             rm.name as rawMaterialName
      FROM ${DatabaseHelper.tableProduction} p
      JOIN ${DatabaseHelper.tableMolds} mold ON p.moldId = mold.id
      JOIN ${DatabaseHelper.tableRawMaterials} rm ON p.rawMaterialId = rm.id
      WHERE p.machineId = ?
      ORDER BY p.date DESC
    ''';

    return await _databaseHelper.rawQuery(query, [machineId]);
  }

  // حساب عدد القطع المنتجة من إسطمبة معينة
  Future<int> getPartsProducedByMold(String moldId) async {
    String query = '''
      SELECT SUM(partsProduced) as totalParts
      FROM ${DatabaseHelper.tableProduction}
      WHERE moldId = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      moldId,
    ]);

    if (result.isEmpty || result[0]['totalParts'] == null) {
      return 0;
    }

    return result[0]['totalParts'];
  }

  // حساب تكلفة الحقن لقطعة من إسطمبة معينة
  Future<double> calculateAverageInjectionCostPerPart(String moldId) async {
    String query = '''
      SELECT SUM(electricityCost + operatorCost) as totalCost, SUM(partsProduced) as totalParts
      FROM ${DatabaseHelper.tableProduction}
      WHERE moldId = ?
    ''';

    List<Map<String, dynamic>> result = await _databaseHelper.rawQuery(query, [
      moldId,
    ]);

    if (result.isEmpty ||
        result[0]['totalCost'] == null ||
        result[0]['totalParts'] == null ||
        result[0]['totalParts'] == 0) {
      return 0;
    }

    double totalCost = result[0]['totalCost'];
    int totalParts = result[0]['totalParts'];

    return totalCost / totalParts;
  }

  // الحصول على إحصائيات الإنتاج
  Future<Map<String, dynamic>> getProductionStatistics() async {
    // الحصول على إجمالي تكلفة الإنتاج
    String costQuery = '''
      SELECT SUM(electricityCost + operatorCost) as totalCost, SUM(partsProduced) as totalParts, SUM(rawMaterialUsed) as totalMaterialUsed
      FROM ${DatabaseHelper.tableProduction}
    ''';

    List<Map<String, dynamic>> costResult = await _databaseHelper.rawQuery(
      costQuery,
    );

    double totalCost =
        costResult.isNotEmpty && costResult[0]['totalCost'] != null
            ? costResult[0]['totalCost']
            : 0.0;

    int totalParts =
        costResult.isNotEmpty && costResult[0]['totalParts'] != null
            ? costResult[0]['totalParts']
            : 0;

    double totalMaterialUsed =
        costResult.isNotEmpty && costResult[0]['totalMaterialUsed'] != null
            ? costResult[0]['totalMaterialUsed']
            : 0.0;

    // الحصول على عدد عمليات الإنتاج
    String countQuery = '''
      SELECT COUNT(id) as productionCount
      FROM ${DatabaseHelper.tableProduction}
    ''';

    List<Map<String, dynamic>> countResult = await _databaseHelper.rawQuery(
      countQuery,
    );
    int productionCount =
        countResult.isNotEmpty && countResult[0]['productionCount'] != null
            ? countResult[0]['productionCount']
            : 0;

    return {
      'totalCost': totalCost,
      'totalParts': totalParts,
      'totalMaterialUsed': totalMaterialUsed,
      'productionCount': productionCount,
      'averageCostPerPart': totalParts > 0 ? totalCost / totalParts : 0.0,
    };
  }
}
