import 'dart:async';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/utils/backup_service.dart';
import 'package:mostafa_final/utils/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// وحدة تحكم النسخ الاحتياطي
class BackupController extends GetxController {
  final BackupService _backupService = BackupService();

  // متغيرات تفاعلية
  final RxBool isLoading = false.obs;
  final RxBool isCreatingBackup = false.obs;
  final RxBool isRestoringBackup = false.obs;
  final RxString errorMessage = ''.obs;
  final RxString successMessage = ''.obs;
  final RxList<Map<String, dynamic>> backups = <Map<String, dynamic>>[].obs;
  final Rx<DateTime?> lastBackupTime = Rx<DateTime?>(null);
  final RxString backupSchedule = ''.obs;
  final RxString backupFolderPath = ''.obs;

  // مؤقت للنسخ الاحتياطي التلقائي
  Timer? _backupTimer;

  // مفاتيح التخزين المشترك
  static const String _autoBackupEnabledKey = 'auto_backup_enabled';
  static const String _autoBackupIntervalKey = 'auto_backup_interval_hours';

  @override
  void onInit() {
    super.onInit();
    loadBackups();
    loadBackupSettings();
    _setupAutoBackup();
  }

  @override
  void onClose() {
    _backupTimer?.cancel();
    super.onClose();
  }

  /// تحميل قائمة النسخ الاحتياطية
  Future<void> loadBackups() async {
    isLoading.value = true;
    errorMessage.value = '';

    try {
      backups.value = await _backupService.getAvailableBackups();
      final lastTime = await _backupService.getLastBackupTime();
      lastBackupTime.value = lastTime;
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء تحميل النسخ الاحتياطية: $e';
      AppLogger.e('حدث خطأ أثناء تحميل النسخ الاحتياطية', e);
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل إعدادات النسخ الاحتياطي
  Future<void> loadBackupSettings() async {
    try {
      final schedule = await _backupService.getBackupSchedule();
      backupSchedule.value = schedule ?? 'manual';

      final prefs = await SharedPreferences.getInstance();
      final isAutoBackupEnabled = prefs.getBool(_autoBackupEnabledKey) ?? false;
      final autoBackupInterval = prefs.getInt(_autoBackupIntervalKey) ?? 24;

      if (isAutoBackupEnabled) {
        backupSchedule.value = 'auto_$autoBackupInterval';
      }
    } catch (e) {
      AppLogger.e('حدث خطأ أثناء تحميل إعدادات النسخ الاحتياطي', e);
    }
  }

  /// إنشاء نسخة احتياطية جديدة
  Future<bool> createBackup({String? customName}) async {
    isCreatingBackup.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final backupPath = await _backupService.createBackup(
        customName: customName,
      );

      if (backupPath != null) {
        successMessage.value = 'تم إنشاء النسخة الاحتياطية بنجاح';
        await loadBackups();
        return true;
      } else {
        errorMessage.value = 'فشل في إنشاء النسخة الاحتياطية';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e';
      AppLogger.e('حدث خطأ أثناء إنشاء النسخة الاحتياطية', e);
      return false;
    } finally {
      isCreatingBackup.value = false;
    }
  }

  /// استعادة قاعدة البيانات من نسخة احتياطية
  Future<bool> restoreBackup(String backupPath) async {
    isRestoringBackup.value = true;
    errorMessage.value = '';
    successMessage.value = '';

    try {
      final result = await _backupService.restoreBackup(backupPath);

      if (result) {
        successMessage.value = 'تم استعادة قاعدة البيانات بنجاح';
        return true;
      } else {
        errorMessage.value = 'فشل في استعادة قاعدة البيانات';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء استعادة قاعدة البيانات: $e';
      AppLogger.e('حدث خطأ أثناء استعادة قاعدة البيانات', e);
      return false;
    } finally {
      isRestoringBackup.value = false;
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(String backupPath) async {
    errorMessage.value = '';

    try {
      final result = await _backupService.deleteBackup(backupPath);

      if (result) {
        await loadBackups();
        return true;
      } else {
        errorMessage.value = 'فشل في حذف النسخة الاحتياطية';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'حدث خطأ أثناء حذف النسخة الاحتياطية: $e';
      AppLogger.e('حدث خطأ أثناء حذف النسخة الاحتياطية', e);
      return false;
    }
  }

  /// تعيين جدول النسخ الاحتياطي التلقائي
  Future<bool> setBackupSchedule(String schedule) async {
    try {
      // تحديث الجدول في الخدمة
      await _backupService.setBackupSchedule(schedule);
      backupSchedule.value = schedule;

      // تحديث إعدادات النسخ الاحتياطي التلقائي
      final prefs = await SharedPreferences.getInstance();

      if (schedule.startsWith('auto_')) {
        final intervalHours = int.tryParse(schedule.split('_')[1]) ?? 24;
        await prefs.setBool(_autoBackupEnabledKey, true);
        await prefs.setInt(_autoBackupIntervalKey, intervalHours);
      } else {
        await prefs.setBool(_autoBackupEnabledKey, false);
      }

      // إعادة تعيين مؤقت النسخ الاحتياطي التلقائي
      _setupAutoBackup();

      return true;
    } catch (e) {
      AppLogger.e('حدث خطأ أثناء تعيين جدول النسخ الاحتياطي', e);
      return false;
    }
  }

  /// تعيين مسار مجلد النسخ الاحتياطي
  Future<bool> setBackupDirectory(String path) async {
    try {
      final result = await _backupService.setBackupDirectory(path);
      if (result) {
        backupFolderPath.value = path;
        return true;
      }
      return false;
    } catch (e) {
      AppLogger.e('حدث خطأ أثناء تعيين مسار مجلد النسخ الاحتياطي', e);
      return false;
    }
  }

  /// إعداد النسخ الاحتياطي التلقائي
  Future<void> _setupAutoBackup() async {
    // إلغاء المؤقت الحالي إذا كان موجوداً
    _backupTimer?.cancel();

    try {
      final prefs = await SharedPreferences.getInstance();
      final isAutoBackupEnabled = prefs.getBool(_autoBackupEnabledKey) ?? false;

      if (!isAutoBackupEnabled) {
        return;
      }

      final autoBackupInterval = prefs.getInt(_autoBackupIntervalKey) ?? 24;
      final intervalDuration = Duration(hours: autoBackupInterval);

      // التحقق من وقت آخر نسخة احتياطية
      final lastBackup = await _backupService.getLastBackupTime();

      if (lastBackup != null) {
        final nextBackupTime = lastBackup.add(intervalDuration);
        final now = DateTime.now();

        if (nextBackupTime.isAfter(now)) {
          // جدولة النسخة الاحتياطية التالية
          final delay = nextBackupTime.difference(now);
          _backupTimer = Timer(delay, _performAutoBackup);
          return;
        }
      }

      // إنشاء نسخة احتياطية الآن إذا لم يتم إنشاء نسخة احتياطية من قبل أو إذا كان الوقت قد حان
      _performAutoBackup();
    } catch (e) {
      AppLogger.e('حدث خطأ أثناء إعداد النسخ الاحتياطي التلقائي', e);
    }
  }

  /// تنفيذ النسخ الاحتياطي التلقائي
  Future<void> _performAutoBackup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final autoBackupInterval = prefs.getInt(_autoBackupIntervalKey) ?? 24;

      // إنشاء اسم للنسخة الاحتياطية التلقائية
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final backupName = 'auto_backup_$timestamp';

      // إنشاء النسخة الاحتياطية
      await createBackup(customName: backupName);

      // جدولة النسخة الاحتياطية التالية
      _backupTimer = Timer(
        Duration(hours: autoBackupInterval),
        _performAutoBackup,
      );
    } catch (e) {
      AppLogger.e('حدث خطأ أثناء تنفيذ النسخ الاحتياطي التلقائي', e);
    }
  }

  /// تنسيق حجم الملف
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }

  /// تنسيق تاريخ التعديل
  String formatModifiedDate(DateTime date) {
    return DateFormat('yyyy-MM-dd HH:mm').format(date);
  }
}
