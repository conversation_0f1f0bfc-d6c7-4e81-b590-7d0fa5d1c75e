import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/mold_change_history_repository.dart';
import 'package:intl/intl.dart';

class MachinesController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final MoldChangeHistoryRepository _historyRepository =
      Get.find<MoldChangeHistoryRepository>();

  final RxBool isLoading = false.obs;
  final RxBool isLoadingHistory = false.obs;
  List<Machine> machines = <Machine>[];
  List<Mold> molds = <Mold>[];
  Map<String, Map<String, dynamic>> moldsInfo =
      <String, Map<String, dynamic>>{};

  // تاريخ تغيير الإسطمبات
  final RxList<Map<String, dynamic>> moldChangeHistory =
      <Map<String, dynamic>>[].obs;

  @override
  void onInit() {
    super.onInit();
    fetchMachines();
    fetchMolds();
  }

  // الحصول على قائمة الماكينات
  Future<void> fetchMachines() async {
    isLoading.value = true;
    update();
    try {
      final machinesList = await _machineRepository.getAllMachines();
      machines = machinesList;
      update();
    } catch (e) {
      _showError('حدث خطأ أثناء جلب الماكينات: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // الحصول على قائمة الإسطمبات
  Future<void> fetchMolds() async {
    try {
      final moldsList = await _moldRepository.getAllMolds();
      molds = moldsList;

      // إنشاء قاموس للوصول السريع إلى معلومات الإسطمبات
      for (var mold in moldsList) {
        if (mold.id != null) {
          moldsInfo[mold.id!] = {
            'id': mold.id,
            'name': mold.name,
            'productCode': mold.productCode,
            'status': mold.status,
          };
        }
      }
      update();
    } catch (e) {
      _showError('حدث خطأ أثناء جلب الإسطمبات: $e');
    }
  }

  // الحصول على معلومات الإسطمبة
  Map<String, dynamic>? getMoldInfo(String? moldId) {
    if (moldId == null) return null;
    return moldsInfo[moldId];
  }

  // إضافة ماكينة جديدة
  Future<void> addMachine(Machine machine) async {
    isLoading.value = true;
    update();
    try {
      await _machineRepository.createMachine(machine);
      await fetchMachines();
      Get.back();
      _showSuccess('تم إضافة الماكينة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة الماكينة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // تحديث ماكينة
  Future<void> updateMachine(Machine machine) async {
    isLoading.value = true;
    update();
    try {
      await _machineRepository.updateMachine(machine);
      await fetchMachines();
      Get.back();
      _showSuccess('تم تحديث الماكينة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث الماكينة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // حذف ماكينة
  Future<void> deleteMachine(String id) async {
    isLoading.value = true;
    update();
    try {
      await _machineRepository.deleteMachine(id);
      machines.removeWhere((machine) => machine.id == id);
      update();
      _showSuccess('تم حذف الماكينة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء حذف الماكينة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // عرض مربع حوار تغيير الإسطمبة
  void showChangeMoldDialog(String machineId) {
    final availableMolds =
        molds
            .where(
              (mold) =>
                  mold.status == MoldStatus.available ||
                  (mold.currentMachineId != null &&
                      mold.currentMachineId == machineId),
            )
            .toList();

    if (availableMolds.isEmpty) {
      _showError('لا توجد إسطمبات متاحة للتغيير');
      return;
    }

    String selectedMoldId = '';
    final TextEditingController notesController = TextEditingController();

    Get.dialog(
      StatefulBuilder(
        builder: (context, setState) {
          return AlertDialog(
            title: const Text('تغيير الإسطمبة'),
            content: SingleChildScrollView(
              child: SizedBox(
                width: 300,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('اختر الإسطمبة الجديدة:'),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          value: selectedMoldId.isEmpty ? null : selectedMoldId,
                          hint: const Text('اختر الإسطمبة'),
                          isExpanded: true,
                          items:
                              availableMolds.map((mold) {
                                return DropdownMenuItem<String>(
                                  value: mold.id!,
                                  child: Text(mold.name),
                                );
                              }).toList(),
                          onChanged: (value) {
                            if (value != null) {
                              setState(() {
                                selectedMoldId = value;
                              });
                            }
                          },
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text('ملاحظات (اختياري):'),
                    const SizedBox(height: 8),
                    TextField(
                      controller: notesController,
                      decoration: const InputDecoration(
                        border: OutlineInputBorder(),
                        hintText: 'أدخل ملاحظات عن عملية التغيير',
                      ),
                      maxLines: 3,
                    ),
                  ],
                ),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed:
                    selectedMoldId.isEmpty
                        ? null
                        : () => changeMold(
                          machineId,
                          selectedMoldId,
                          notesController.text.trim(),
                        ),
                child: const Text('تغيير'),
              ),
            ],
          );
        },
      ),
    );
  }

  // تغيير الإسطمبة المركبة على الماكينة
  Future<void> changeMold(String machineId, String moldId, String notes) async {
    isLoading.value = true;
    update();
    try {
      await _machineRepository.changeMold(
        machineId,
        moldId,
        notes: notes.isNotEmpty ? notes : null,
      );
      await fetchMachines();
      await fetchMolds();
      Get.back();
      _showSuccess('تم تغيير الإسطمبة بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تغيير الإسطمبة: $e');
    } finally {
      isLoading.value = false;
      update();
    }
  }

  // جلب تاريخ تغيير الإسطمبات لماكينة معينة
  Future<void> fetchMoldChangeHistory(String machineId) async {
    isLoadingHistory.value = true;
    update();

    try {
      final history = await _historyRepository.getMoldChangeHistoryForMachine(
        machineId,
      );
      moldChangeHistory.assignAll(history);
    } catch (e) {
      _showError('حدث خطأ أثناء جلب سجل تغيير الإسطمبات: $e');
    } finally {
      isLoadingHistory.value = false;
      update();
    }
  }

  // عرض سجل تغيير الإسطمبات لماكينة معينة
  void showMoldChangeHistoryDialog(String machineId, String machineName) async {
    await fetchMoldChangeHistory(machineId);

    Get.dialog(
      AlertDialog(
        title: Text('سجل تغيير الإسطمبات - $machineName'),
        content: SizedBox(
          width: 500,
          height: 400,
          child: Obx(() {
            if (isLoadingHistory.value) {
              return const Center(child: CircularProgressIndicator());
            }

            if (moldChangeHistory.isEmpty) {
              return const Center(
                child: Text('لا يوجد سجل لتغيير الإسطمبات لهذه الماكينة'),
              );
            }

            return ListView.builder(
              itemCount: moldChangeHistory.length,
              itemBuilder: (context, index) {
                final record = moldChangeHistory[index];
                final DateTime changeDate = DateTime.parse(
                  record['changeDate'],
                );
                final String formattedDate = DateFormat(
                  'yyyy/MM/dd - HH:mm',
                ).format(changeDate);

                return Card(
                  margin: const EdgeInsets.only(bottom: 8),
                  child: ListTile(
                    title: Text('تم تركيب: ${record['moldName']}'),
                    subtitle: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('التاريخ: $formattedDate'),
                        if (record['previousMoldName'] != null)
                          Text(
                            'الإسطمبة السابقة: ${record['previousMoldName']}',
                          ),
                        if (record['notes'] != null &&
                            record['notes'].toString().isNotEmpty)
                          Text('ملاحظات: ${record['notes']}'),
                      ],
                    ),
                    leading: const Icon(Icons.swap_horiz, size: 32),
                  ),
                );
              },
            );
          }),
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إغلاق')),
        ],
      ),
    );
  }

  // عرض رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
