import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/production_plan.dart';
import 'package:uuid/uuid.dart';

class ProductionPlanRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء خطة إنتاج جديدة
  Future<String> createProductionPlan(ProductionPlan plan) async {
    final planWithId = ProductionPlan(
      id: _uuid.v4(),
      startDate: plan.startDate,
      endDate: plan.endDate,
      items: plan.items,
      status: plan.status,
    );

    // حفظ خطة الإنتاج
    final planId = await _databaseHelper.insert(
      DatabaseHelper.tableProductionPlans,
      planWithId.toMap(),
    );

    // حفظ عناصر خطة الإنتاج
    for (var item in planWithId.items) {
      final itemWithIds = ProductionPlanItem(
        id: _uuid.v4(),
        planId: planWithId.id,
        productId: item.productId,
        quantity: item.quantity,
        priority: item.priority,
        plannedStart: item.plannedStart,
        plannedEnd: item.plannedEnd,
        completedQuantity: item.completedQuantity,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableProductionPlanItems,
        itemWithIds.toMap(),
      );
    }

    return planId;
  }

  // الحصول على جميع خطط الإنتاج
  Future<List<ProductionPlan>> getAllProductionPlans() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableProductionPlans,
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getPlanItems(map['id']);
        return ProductionPlan.fromMap(map, items);
      }).toList(),
    );
  }

  // الحصول على خطط الإنتاج النشطة (قيد التنفيذ)
  Future<List<ProductionPlan>> getActiveProductionPlans() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableProductionPlans} WHERE status = ?',
      [ProductionPlanStatus.inProgress],
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getPlanItems(map['id']);
        return ProductionPlan.fromMap(map, items);
      }).toList(),
    );
  }

  // الحصول على خطة إنتاج بمعرف معين
  Future<ProductionPlan?> getProductionPlanById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableProductionPlans,
      id,
    );

    if (map == null) {
      return null;
    }

    final items = await _getPlanItems(id);
    return ProductionPlan.fromMap(map, items);
  }

  // تحديث خطة إنتاج
  Future<void> updateProductionPlan(ProductionPlan plan) async {
    // تحديث بيانات الخطة
    await _databaseHelper.update(
      DatabaseHelper.tableProductionPlans,
      plan.toMap(),
    );

    // حذف العناصر القديمة
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableProductionPlanItems} WHERE planId = ?',
      [plan.id],
    );

    // إضافة العناصر الجديدة
    for (var item in plan.items) {
      final itemWithIds = ProductionPlanItem(
        id: _uuid.v4(),
        planId: plan.id,
        productId: item.productId,
        quantity: item.quantity,
        priority: item.priority,
        plannedStart: item.plannedStart,
        plannedEnd: item.plannedEnd,
        completedQuantity: item.completedQuantity,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableProductionPlanItems,
        itemWithIds.toMap(),
      );
    }
  }

  // تحديث حالة خطة إنتاج
  Future<void> updateProductionPlanStatus(String planId, String status) async {
    await _databaseHelper.rawQuery(
      'UPDATE ${DatabaseHelper.tableProductionPlans} SET status = ? WHERE id = ?',
      [status, planId],
    );
  }

  // تحديث كمية الإنتاج المكتملة لعنصر في خطة الإنتاج
  Future<void> updateItemCompletedQuantity(
    String itemId,
    int completedQuantity,
  ) async {
    await _databaseHelper.rawQuery(
      'UPDATE ${DatabaseHelper.tableProductionPlanItems} SET completedQuantity = ? WHERE id = ?',
      [completedQuantity, itemId],
    );
  }

  // حذف خطة إنتاج
  Future<void> deleteProductionPlan(String id) async {
    // حذف عناصر الخطة أولاً
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableProductionPlanItems} WHERE planId = ?',
      [id],
    );

    // ثم حذف الخطة نفسها
    await _databaseHelper.delete(DatabaseHelper.tableProductionPlans, id);
  }

  // الحصول على عناصر خطة إنتاج
  Future<List<ProductionPlanItem>> _getPlanItems(String planId) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableProductionPlanItems} WHERE planId = ? ORDER BY priority ASC',
      [planId],
    );

    return maps.map((map) => ProductionPlanItem.fromMap(map)).toList();
  }

  // الحصول على خطط الإنتاج بناءً على التاريخ
  Future<List<ProductionPlan>> getProductionPlansByDate(DateTime date) async {
    final String dateStr = date.toIso8601String().split('T')[0];
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableProductionPlans} WHERE date(startDate) <= ? AND date(endDate) >= ?',
      [dateStr, dateStr],
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getPlanItems(map['id']);
        return ProductionPlan.fromMap(map, items);
      }).toList(),
    );
  }
}
