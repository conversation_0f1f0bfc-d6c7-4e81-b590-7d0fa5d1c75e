import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';

class EnhancedSearchBar extends StatelessWidget {
  final TextEditingController controller;
  final String hintText;
  final Function(String) onChanged;
  final VoidCallback? onClear;
  final Widget? leading;
  final List<Widget>? actions;
  final bool autoFocus;
  final bool showBorder;

  const EnhancedSearchBar({
    super.key,
    required this.controller,
    required this.hintText,
    required this.onChanged,
    this.onClear,
    this.leading,
    this.actions,
    this.autoFocus = false,
    this.showBorder = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
        boxShadow: showBorder ? null : EnhancedAppTheme.cardShadow,
        border:
            showBorder
                ? Border.all(color: EnhancedAppTheme.textLight.with<PERSON><PERSON><PERSON>(128))
                : null,
      ),
      child: Row(
        children: [
          if (leading != null)
            Padding(padding: const EdgeInsets.only(right: 8.0), child: leading)
          else
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 12.0),
              child: Icon(Icons.search, color: EnhancedAppTheme.textSecondary),
            ),
          Expanded(
            child: TextField(
              controller: controller,
              autofocus: autoFocus,
              decoration: InputDecoration(
                hintText: hintText,
                hintStyle: const TextStyle(
                  color: EnhancedAppTheme.textLight,
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 15),
              ),
              style: const TextStyle(
                color: EnhancedAppTheme.textPrimary,
                fontSize: 14,
              ),
              onChanged: onChanged,
            ),
          ),
          if (controller.text.isNotEmpty)
            IconButton(
              icon: const Icon(
                Icons.close,
                color: EnhancedAppTheme.textSecondary,
                size: 20,
              ),
              onPressed: () {
                controller.clear();
                onChanged('');
                if (onClear != null) {
                  onClear!();
                }
              },
            ),
          if (actions != null) ...actions!,
        ],
      ),
    );
  }
}

class EnhancedFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;
  final IconData? icon;

  const EnhancedFilterChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
    this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        margin: const EdgeInsets.only(left: 8),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? EnhancedAppTheme.primaryColor
                  : EnhancedAppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color:
                isSelected
                    ? EnhancedAppTheme.primaryColor
                    : EnhancedAppTheme.textLight,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null)
              Icon(
                icon,
                size: 16,
                color:
                    isSelected
                        ? EnhancedAppTheme.textOnPrimary
                        : EnhancedAppTheme.textSecondary,
              ),
            if (icon != null) const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                fontSize: 12,
                color:
                    isSelected
                        ? EnhancedAppTheme.textOnPrimary
                        : EnhancedAppTheme.textSecondary,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
