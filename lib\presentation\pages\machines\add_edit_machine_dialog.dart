import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

class AddEditMachineDialog extends StatefulWidget {
  const AddEditMachineDialog({super.key, this.machine, required this.onSave})
    : isEditing = machine != null;

  final bool isEditing;
  final Machine? machine;
  final Function(Machine) onSave;

  @override
  State<AddEditMachineDialog> createState() => _AddEditMachineDialogState();
}

class _AddEditMachineDialogState extends State<AddEditMachineDialog> {
  final _formKey = GlobalKey<FormState>();
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _modelController = TextEditingController();
  final TextEditingController _powerConsumptionController =
      TextEditingController();
  String _status = MachineStatus.operating;

  @override
  void initState() {
    super.initState();
    if (widget.machine != null) {
      _nameController.text = widget.machine!.name;
      _modelController.text = widget.machine!.model;
      _powerConsumptionController.text =
          widget.machine!.powerConsumption.toString();
      _status = widget.machine!.status;
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _modelController.dispose();
    _powerConsumptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final isEditing = widget.machine != null;

    return AlertDialog(
      title: TextUtils.responsiveText(
        isEditing ? 'تعديل الماكينة' : 'إضافة ماكينة جديدة',
        style: TextStyle(
          fontSize: ScreenSize.isSmallScreen ? 16 : 18,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
      ),
      contentPadding: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(24),
        vertical: ScreenSize.getPadding(16),
      ),
      content: Form(
        key: _formKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'اسم الماكينة',
                  hintText: 'أدخل اسم الماكينة',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال اسم الماكينة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextFormField(
                controller: _modelController,
                decoration: InputDecoration(
                  labelText: 'الموديل',
                  hintText: 'أدخل موديل الماكينة',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال موديل الماكينة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextFormField(
                controller: _powerConsumptionController,
                decoration: InputDecoration(
                  labelText: 'استهلاك الطاقة (كيلوواط)',
                  hintText: 'أدخل استهلاك الطاقة',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                keyboardType: const TextInputType.numberWithOptions(
                  decimal: true,
                ),
                inputFormatters: [
                  FilteringTextInputFormatter.allow(RegExp(r'^\d+\.?\d{0,2}')),
                ],
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى إدخال استهلاك الطاقة';
                  }
                  try {
                    double.parse(value);
                  } catch (e) {
                    return 'يرجى إدخال قيمة صحيحة';
                  }
                  return null;
                },
                textInputAction: TextInputAction.next,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'حالة الماكينة',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                value: _status,
                items: [
                  DropdownMenuItem<String>(
                    value: MachineStatus.operating,
                    child: TextUtils.responsiveText(
                      MachineStatus.operating,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  DropdownMenuItem<String>(
                    value: MachineStatus.stopped,
                    child: TextUtils.responsiveText(
                      MachineStatus.stopped,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                  DropdownMenuItem<String>(
                    value: MachineStatus.maintenance,
                    child: TextUtils.responsiveText(
                      MachineStatus.maintenance,
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _status = value;
                    });
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار حالة الماكينة';
                  }
                  return null;
                },
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                isExpanded: true,
                icon: Icon(
                  Icons.arrow_drop_down,
                  size: ScreenSize.isSmallScreen ? 20 : 24,
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          style: TextButton.styleFrom(
            padding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(8),
            ),
          ),
          child: TextUtils.responsiveText(
            'إلغاء',
            style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
            maxLines: 1,
          ),
        ),
        ElevatedButton(
          onPressed: _saveMachine,
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(
              horizontal: ScreenSize.getPadding(16),
              vertical: ScreenSize.getPadding(8),
            ),
          ),
          child: TextUtils.responsiveText(
            isEditing ? 'تحديث' : 'إضافة',
            style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
            maxLines: 1,
          ),
        ),
      ],
    );
  }

  void _saveMachine() {
    if (_formKey.currentState!.validate()) {
      final machine = Machine(
        id: widget.machine?.id,
        name: _nameController.text,
        model: _modelController.text,
        powerConsumption: double.parse(_powerConsumptionController.text),
        status: _status,
        currentMoldId: widget.machine?.currentMoldId,
        lastMoldChange: widget.machine?.lastMoldChange,
      );

      widget.onSave(machine);
    }
  }
}
