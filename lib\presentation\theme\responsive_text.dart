import 'package:flutter/material.dart';

/// مساعد لتنسيق النصوص بشكل متجاوب مع حجم الشاشة
class ResponsiveText {
  static TextStyle get titleLarge => const TextStyle(
        fontSize: 22,
        fontWeight: FontWeight.bold,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get titleMedium => const TextStyle(
        fontSize: 18,
        fontWeight: FontWeight.bold,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get titleSmall => const TextStyle(
        fontSize: 16,
        fontWeight: FontWeight.bold,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get bodyLarge => const TextStyle(
        fontSize: 16,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get bodyMedium => const TextStyle(
        fontSize: 14,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get bodySmall => const TextStyle(
        fontSize: 12,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get labelMedium => const TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w500,
        overflow: TextOverflow.ellipsis,
      );

  static TextStyle get labelSmall => const TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        overflow: TextOverflow.ellipsis,
      );
}

/// امتداد لتطبيق التنسيق المتجاوب على النصوص
extension ResponsiveTextExtension on Text {
  /// تطبيق تنسيق متجاوب على النص
  Text responsive({
    TextStyle? style,
    TextOverflow overflow = TextOverflow.ellipsis,
    int? maxLines,
    TextAlign? textAlign,
  }) {
    return Text(
      data ?? '',
      style: (this.style ?? const TextStyle()).copyWith(
        overflow: overflow,
      ).merge(style),
      maxLines: maxLines ?? this.maxLines,
      textAlign: textAlign ?? this.textAlign,
      softWrap: true,
    );
  }
}

/// امتداد لتطبيق التنسيق المتجاوب على الحاويات
extension ResponsiveBoxExtension on Widget {
  /// تغليف العنصر في حاوية متجاوبة
  Widget responsiveBox({
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    Alignment? alignment,
    BoxConstraints? constraints,
    Color? color,
    Decoration? decoration,
    BorderRadius? borderRadius,
  }) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      margin: margin,
      alignment: alignment,
      constraints: constraints,
      decoration: decoration ??
          (borderRadius != null || color != null
              ? BoxDecoration(
                  color: color,
                  borderRadius: borderRadius,
                )
              : null),
      child: this,
    );
  }

  /// تغليف العنصر في حاوية مرنة
  Widget expanded({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: this,
    );
  }

  /// تغليف العنصر في حاوية مرنة
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: this,
    );
  }
}
