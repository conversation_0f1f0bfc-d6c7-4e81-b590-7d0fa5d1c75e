import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:mostafa_final/data/models/breakdown.dart';
import 'package:mostafa_final/data/models/maintenance.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/repositories/maintenance_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/services/maintenance_service.dart';

class MaintenanceController extends GetxController {
  final MaintenanceRepository _maintenanceRepository = MaintenanceRepository();
  final MachineRepository _machineRepository = MachineRepository();
  final MaintenanceService _maintenanceService = MaintenanceService();

  // حالة التحميل
  final isLoading = false.obs;
  final isAnalyzing = false.obs;

  // البيانات الأساسية
  final breakdowns = <Breakdown>[].obs;
  final maintenanceList = <Maintenance>[].obs;
  final maintenanceRecords = <MaintenanceRecord>[].obs;
  final machines = <Machine>[].obs;

  // التحليلات والإحصائيات
  final breakdownAnalysis = <String, dynamic>{}.obs;
  final maintenanceStats = <String, dynamic>{}.obs;
  final maintenanceAlerts = <Map<String, dynamic>>[].obs;
  final efficiencyAnalysis = <String, dynamic>{}.obs;

  // المرشحات والفلاتر
  final selectedMachineId = ''.obs;
  final selectedDateRange = DateTimeRange(
    start: DateTime.now().subtract(const Duration(days: 30)),
    end: DateTime.now(),
  ).obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  /// تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        loadMachines(),
        loadBreakdowns(),
        loadMaintenance(),
        loadMaintenanceRecords(),
        generateMaintenanceAlerts(),
      ]);
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  /// تحميل قائمة الماكينات
  Future<void> loadMachines() async {
    try {
      final machineList = await _machineRepository.getAllMachines();
      machines.value = machineList;
    } catch (e) {
      debugPrint('خطأ في تحميل الماكينات: $e');
    }
  }

  /// تحميل الأعطال
  Future<void> loadBreakdowns() async {
    try {
      final breakdownList = selectedMachineId.value.isEmpty
          ? await _maintenanceRepository.getAllBreakdowns()
          : await _maintenanceRepository.getBreakdownsByMachine(selectedMachineId.value);
      breakdowns.value = breakdownList;
    } catch (e) {
      debugPrint('خطأ في تحميل الأعطال: $e');
    }
  }

  /// تحميل الصيانة
  Future<void> loadMaintenance() async {
    try {
      final maintenance = selectedMachineId.value.isEmpty
          ? await _maintenanceRepository.getAllMaintenance()
          : await _maintenanceRepository.getMaintenanceByMachine(selectedMachineId.value);
      maintenanceList.value = maintenance;
    } catch (e) {
      debugPrint('خطأ في تحميل الصيانة: $e');
    }
  }

  /// تحميل سجلات الصيانة
  Future<void> loadMaintenanceRecords() async {
    try {
      final records = selectedMachineId.value.isEmpty
          ? await _maintenanceRepository.getMaintenanceRecords()
          : await _maintenanceRepository.getMaintenanceRecordsByMachine(selectedMachineId.value);
      maintenanceRecords.value = records;
    } catch (e) {
      debugPrint('خطأ في تحميل سجلات الصيانة: $e');
    }
  }

  /// إضافة عطل جديد
  Future<void> addBreakdown(Breakdown breakdown) async {
    try {
      await _maintenanceRepository.addBreakdown(breakdown);
      await loadBreakdowns();
      Get.snackbar('نجح', 'تم إضافة العطل بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إضافة العطل: $e');
    }
  }

  /// إنهاء عطل
  Future<void> finishBreakdown(String breakdownId, {
    String? fixedBy,
    String? fixDescription,
    double? repairCost,
    List<String>? spareParts,
  }) async {
    try {
      await _maintenanceRepository.finishBreakdown(
        breakdownId,
        fixedBy: fixedBy,
        fixDescription: fixDescription,
        repairCost: repairCost,
        spareParts: spareParts,
      );
      await loadBreakdowns();
      Get.snackbar('نجح', 'تم إنهاء العطل بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إنهاء العطل: $e');
    }
  }

  /// إضافة صيانة وقائية
  Future<void> addMaintenance(Maintenance maintenance) async {
    try {
      await _maintenanceRepository.addMaintenance(maintenance);
      await loadMaintenance();
      Get.snackbar('نجح', 'تم إضافة الصيانة بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إضافة الصيانة: $e');
    }
  }

  /// تحديث صيانة
  Future<void> updateMaintenance(Maintenance maintenance) async {
    try {
      await _maintenanceRepository.updateMaintenance(maintenance);
      await loadMaintenance();
      Get.snackbar('نجح', 'تم تحديث الصيانة بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحديث الصيانة: $e');
    }
  }

  /// إضافة سجل صيانة منجزة
  Future<void> addMaintenanceRecord(MaintenanceRecord record) async {
    try {
      await _maintenanceRepository.addMaintenanceRecord(record);
      
      // تحديث موعد الصيانة القادمة
      await _maintenanceRepository.updateNextMaintenanceDue(record.maintenanceId);
      
      await loadMaintenanceRecords();
      await loadMaintenance();
      Get.snackbar('نجح', 'تم تسجيل الصيانة المنجزة بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تسجيل الصيانة: $e');
    }
  }

  /// تحليل الأعطال
  Future<void> analyzeBreakdowns() async {
    isAnalyzing.value = true;
    try {
      final analysis = await _maintenanceService.analyzeBreakdownsAndRecommendMaintenance();
      breakdownAnalysis.value = analysis;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحليل الأعطال: $e');
    } finally {
      isAnalyzing.value = false;
    }
  }

  /// تحليل كفاءة الصيانة
  Future<void> analyzeMaintenanceEffectiveness() async {
    isAnalyzing.value = true;
    try {
      final analysis = await _maintenanceService.analyzeMaintenanceEffectiveness();
      efficiencyAnalysis.value = analysis;
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في تحليل كفاءة الصيانة: $e');
    } finally {
      isAnalyzing.value = false;
    }
  }

  /// إنشاء تنبيهات الصيانة
  Future<void> generateMaintenanceAlerts() async {
    try {
      final alerts = await _maintenanceService.generateMaintenanceAlerts();
      maintenanceAlerts.value = alerts;
    } catch (e) {
      debugPrint('خطأ في إنشاء تنبيهات الصيانة: $e');
    }
  }

  /// إنشاء جدولة صيانة وقائية تلقائية
  Future<void> generatePreventiveMaintenanceSchedule(String machineId) async {
    try {
      final schedule = await _maintenanceService.generatePreventiveMaintenanceSchedule(machineId);
      
      // إضافة كل صيانة في الجدولة
      for (var maintenance in schedule) {
        await _maintenanceRepository.addMaintenance(maintenance);
      }
      
      await loadMaintenance();
      Get.snackbar('نجح', 'تم إنشاء جدولة الصيانة الوقائية بنجاح');
    } catch (e) {
      Get.snackbar('خطأ', 'فشل في إنشاء جدولة الصيانة: $e');
    }
  }

  /// تغيير الماكينة المحددة
  void changeMachine(String machineId) {
    selectedMachineId.value = machineId;
    loadBreakdowns();
    loadMaintenance();
    loadMaintenanceRecords();
  }

  /// تغيير نطاق التاريخ
  void changeDateRange(DateTimeRange dateRange) {
    selectedDateRange.value = dateRange;
    loadBreakdowns();
    loadMaintenanceRecords();
  }

  /// عرض نافذة إضافة عطل
  void showAddBreakdownDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة عطل جديد'),
        content: _buildAddBreakdownForm(),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _submitBreakdown,
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// عرض نافذة إضافة صيانة
  void showAddMaintenanceDialog() {
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة صيانة وقائية'),
        content: _buildAddMaintenanceForm(),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _submitMaintenance,
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }

  /// بناء نموذج إضافة عطل
  Widget _buildAddBreakdownForm() {
    // سيتم تطوير النموذج في واجهة المستخدم
    return const SizedBox(
      width: 400,
      height: 300,
      child: Center(child: Text('نموذج إضافة العطل')),
    );
  }

  /// بناء نموذج إضافة صيانة
  Widget _buildAddMaintenanceForm() {
    // سيتم تطوير النموذج في واجهة المستخدم
    return const SizedBox(
      width: 400,
      height: 400,
      child: Center(child: Text('نموذج إضافة الصيانة')),
    );
  }

  /// إرسال بيانات العطل
  void _submitBreakdown() {
    // سيتم تطوير المنطق في واجهة المستخدم
    Get.back();
  }

  /// إرسال بيانات الصيانة
  void _submitMaintenance() {
    // سيتم تطوير المنطق في واجهة المستخدم
    Get.back();
  }

  /// الحصول على الأعطال النشطة
  List<Breakdown> get activeBreakdowns {
    return breakdowns.where((breakdown) => breakdown.isOngoing).toList();
  }

  /// الحصول على الصيانات المستحقة
  List<Maintenance> get dueMaintenance {
    return maintenanceList.where((maintenance) => maintenance.isDue).toList();
  }

  /// الحصول على الصيانات المتأخرة
  List<Maintenance> get overdueMaintenance {
    return maintenanceList.where((maintenance) => maintenance.isOverdue).toList();
  }

  /// إحصائيات سريعة
  Map<String, int> get quickStats {
    return {
      'totalBreakdowns': breakdowns.length,
      'activeBreakdowns': activeBreakdowns.length,
      'totalMaintenance': maintenanceList.length,
      'dueMaintenance': dueMaintenance.length,
      'overdueMaintenance': overdueMaintenance.length,
      'maintenanceAlerts': maintenanceAlerts.length,
    };
  }
}
