import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/presentation/controllers/production_priority_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';
import 'package:mostafa_final/data/models/production_plan.dart';

class ProductionPriorityPage extends StatelessWidget {
  const ProductionPriorityPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ProductionPriorityController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('أولويات الإنتاج'),
        actions: [
          Obx(
            () =>
                controller.currentPlan.value != null
                    ? IconButton(
                      icon: const Icon(Icons.play_arrow),
                      tooltip: 'بدء الخطة',
                      onPressed:
                          () => controller.updatePlanStatus(
                            ProductionPlanStatus.inProgress,
                          ),
                    )
                    : const SizedBox(),
          ),
          Obx(
            () =>
                controller.currentPlan.value != null
                    ? IconButton(
                      icon: const Icon(Icons.check),
                      tooltip: 'إنهاء الخطة',
                      onPressed:
                          () => controller.updatePlanStatus(
                            ProductionPlanStatus.completed,
                          ),
                    )
                    : const SizedBox(),
          ),
          IconButton(
            icon: const Icon(Icons.save),
            tooltip: 'حفظ الخطة',
            onPressed: () {
              if (controller.currentPlan.value != null) {
                controller.updateCurrentPlan();
              } else {
                controller.createNewPlan();
              }
            },
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      body: Obx(
        () =>
            controller.isLoading.value
                ? const LoadingIndicator(message: 'جاري تحميل البيانات...')
                : _buildContent(context, controller),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddProductDialog(context, controller),
        tooltip: 'إضافة منتج',
        child: const Icon(Icons.add),
      ),
    );
  }

  // بناء محتوى الصفحة
  Widget _buildContent(
    BuildContext context,
    ProductionPriorityController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPlanHeader(context, controller),
          const SizedBox(height: 16),
          Obx(
            () =>
                controller.isSaving.value
                    ? const Center(child: CircularProgressIndicator())
                    : const SizedBox(),
          ),
          const Divider(),
          const SizedBox(height: 8),
          _buildPlanItemsHeader(),
          const SizedBox(height: 8),
          Expanded(child: _buildPlanItemsList(controller)),
        ],
      ),
    );
  }

  // بناء رأس الخطة
  Widget _buildPlanHeader(
    BuildContext context,
    ProductionPriorityController controller,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'خطة الإنتاج',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Obx(
                  () =>
                      controller.currentPlan.value == null
                          ? const Text('خطة جديدة')
                          : Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getPlanStatusColor(
                                controller.currentPlan.value!.status,
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              controller.currentPlan.value!.status,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDateSelector(
                    context,
                    'من: ${DateFormat('yyyy/MM/dd').format(controller.startDate.value)}',
                    () => controller.selectStartDate(context),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDateSelector(
                    context,
                    'إلى: ${DateFormat('yyyy/MM/dd').format(controller.endDate.value)}',
                    () => controller.selectEndDate(context),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Obx(
              () =>
                  controller.planItems.isEmpty
                      ? const Center(
                        child: Padding(
                          padding: EdgeInsets.all(8.0),
                          child: Text(
                            'لا توجد منتجات في خطة الإنتاج. أضف منتجات باستخدام زر الإضافة.',
                            textAlign: TextAlign.center,
                            style: TextStyle(color: Colors.grey),
                          ),
                        ),
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'عدد المنتجات: ${controller.planItems.length}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                          Text(
                            'إجمالي القطع: ${_getTotalQuantity(controller)}',
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        ],
                      ),
            ),
          ],
        ),
      ),
    );
  }

  // بناء منتقي التاريخ
  Widget _buildDateSelector(
    BuildContext context,
    String label,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey.shade300),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), const Icon(Icons.calendar_today, size: 16)],
        ),
      ),
    );
  }

  // بناء رأس قائمة عناصر الخطة
  Widget _buildPlanItemsHeader() {
    return const Row(
      children: [
        SizedBox(width: 32), // للأيقونات
        Expanded(
          flex: 2,
          child: Text('المنتج', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        Expanded(
          flex: 1,
          child: Text('الكود', style: TextStyle(fontWeight: FontWeight.bold)),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'الكمية',
            style: TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        Expanded(
          flex: 1,
          child: Text(
            'الأولوية',
            style: TextStyle(fontWeight: FontWeight.bold),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(width: 100), // للأزرار
      ],
    );
  }

  // بناء قائمة عناصر الخطة
  Widget _buildPlanItemsList(ProductionPriorityController controller) {
    return Obx(
      () =>
          controller.planItems.isEmpty
              ? const Center(
                child: Text(
                  'لا توجد منتجات في خطة الإنتاج',
                  style: TextStyle(color: Colors.grey),
                ),
              )
              : ReorderableListView.builder(
                itemCount: controller.planItems.length,
                onReorder: (oldIndex, newIndex) {
                  if (oldIndex < newIndex) {
                    newIndex -= 1;
                  }

                  // تحديث الأولويات بعد إعادة الترتيب
                  final movedItem = controller.planItems[oldIndex];
                  final items = List<ProductionPlanItemWithDetails>.from(
                    controller.planItems,
                  );

                  items.removeAt(oldIndex);
                  items.insert(newIndex, movedItem);

                  // تحديث الأولويات
                  for (int i = 0; i < items.length; i++) {
                    if (items[i].id != null) {
                      controller.updateItemPriority(items[i].id!, i + 1);
                    }
                  }
                },
                itemBuilder: (context, index) {
                  final item = controller.planItems[index];
                  return _buildPlanItemCard(context, controller, item, index);
                },
              ),
    );
  }

  // بناء بطاقة عنصر الخطة
  Widget _buildPlanItemCard(
    BuildContext context,
    ProductionPriorityController controller,
    ProductionPlanItemWithDetails item,
    int index,
  ) {
    return Card(
      key: ValueKey(item.id ?? index.toString()),
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Icon(Icons.drag_handle, color: Colors.grey.shade400, size: 24),
            Expanded(
              flex: 2,
              child: Text(item.productName, overflow: TextOverflow.ellipsis),
            ),
            Expanded(
              flex: 1,
              child: Text(item.productCode, overflow: TextOverflow.ellipsis),
            ),
            Expanded(
              flex: 1,
              child: Text(
                item.quantity.toString(),
                textAlign: TextAlign.center,
              ),
            ),
            Expanded(
              flex: 1,
              child: Text(
                item.priority.toString(),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(
              width: 100,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  IconButton(
                    icon: const Icon(Icons.edit, size: 20),
                    onPressed:
                        () => _showEditItemDialog(context, controller, item),
                    color: Colors.blue,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'تعديل',
                  ),
                  const SizedBox(width: 8),
                  IconButton(
                    icon: const Icon(Icons.delete, size: 20),
                    onPressed:
                        () => _showDeleteConfirmationDialog(
                          context,
                          controller,
                          item,
                        ),
                    color: Colors.red,
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                    tooltip: 'حذف',
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // عرض مربع حوار إضافة منتج
  void _showAddProductDialog(
    BuildContext context,
    ProductionPriorityController controller,
  ) {
    if (controller.products.isEmpty) {
      Get.snackbar(
        'خطأ',
        'لا توجد منتجات متاحة للإضافة',
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    String selectedProductId = controller.products.first.id!;
    int quantity = 1000;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إضافة منتج إلى خطة الإنتاج'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                DropdownButtonFormField<String>(
                  decoration: const InputDecoration(
                    labelText: 'المنتج',
                    border: OutlineInputBorder(),
                  ),
                  value: selectedProductId,
                  items:
                      controller.products
                          .map(
                            (product) => DropdownMenuItem<String>(
                              value: product.id,
                              child: Text('${product.name} (${product.code})'),
                            ),
                          )
                          .toList(),
                  onChanged: (value) {
                    if (value != null) {
                      selectedProductId = value;
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  initialValue: quantity.toString(),
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      quantity = int.tryParse(value) ?? 1000;
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  controller.addProductToPlan(selectedProductId, quantity);
                  Navigator.pop(context);
                },
                child: const Text('إضافة'),
              ),
            ],
          ),
    );
  }

  // عرض مربع حوار تعديل عنصر
  void _showEditItemDialog(
    BuildContext context,
    ProductionPriorityController controller,
    ProductionPlanItemWithDetails item,
  ) {
    int quantity = item.quantity;
    int priority = item.priority;

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تعديل منتج في خطة الإنتاج'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'المنتج: ${item.productName}',
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الكمية',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  initialValue: quantity.toString(),
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      quantity = int.tryParse(value) ?? quantity;
                    }
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  decoration: const InputDecoration(
                    labelText: 'الأولوية',
                    border: OutlineInputBorder(),
                  ),
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  initialValue: priority.toString(),
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      priority = int.tryParse(value) ?? priority;
                    }
                  },
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  if (quantity != item.quantity) {
                    controller.updateItemQuantity(item.id!, quantity);
                  }
                  if (priority != item.priority) {
                    controller.updateItemPriority(item.id!, priority);
                  }
                  Navigator.pop(context);
                },
                child: const Text('حفظ'),
              ),
            ],
          ),
    );
  }

  // عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(
    BuildContext context,
    ProductionPriorityController controller,
    ProductionPlanItemWithDetails item,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: Text(
              'هل أنت متأكد من حذف المنتج "${item.productName}" من خطة الإنتاج؟',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () {
                  controller.removeProductFromPlan(item.id!);
                  Navigator.pop(context);
                },
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );
  }

  // الحصول على لون حالة الخطة
  Color _getPlanStatusColor(String status) {
    switch (status) {
      case ProductionPlanStatus.planned:
        return Colors.orange;
      case ProductionPlanStatus.inProgress:
        return Colors.green;
      case ProductionPlanStatus.completed:
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  // حساب إجمالي الكمية
  int _getTotalQuantity(ProductionPriorityController controller) {
    int total = 0;
    for (final item in controller.planItems) {
      total += item.quantity;
    }
    return total;
  }
}
