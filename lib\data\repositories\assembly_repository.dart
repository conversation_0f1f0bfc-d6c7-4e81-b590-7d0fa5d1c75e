import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/assembly.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/models/inventory.dart';
import 'package:uuid/uuid.dart';

class AssemblyRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final InventoryRepository _inventoryRepository = InventoryRepository();
  final _uuid = const Uuid();

  // إنشاء عملية تجميع جديدة
  Future<String> createAssembly(Assembly assembly) async {
    // إنشاء معرف جديد لعملية التجميع
    final assemblyWithId = Assembly(
      id: _uuid.v4(),
      productId: assembly.productId,
      date: assembly.date,
      workerId: assembly.workerId,
      quantity: assembly.quantity,
      laborCostPerMinute: assembly.laborCostPerMinute,
      totalAssemblyCost: assembly.totalAssemblyCost,
      totalPackagingCost: assembly.totalPackagingCost,
      status: assembly.status,
      componentsUsed: assembly.componentsUsed,
    );

    // حفظ عملية التجميع
    final assemblyId = await _databaseHelper.insert(
      DatabaseHelper.tableAssembly,
      assemblyWithId.toMap(),
    );

    // حفظ مكونات التجميع
    for (var component in assemblyWithId.componentsUsed) {
      final componentWithIds = AssemblyComponentUsage(
        id: _uuid.v4(),
        assemblyId: assemblyWithId.id,
        componentId: component.componentId,
        name: component.name,
        partId: component.partId,
        componentType: component.componentType,
        quantity: component.quantity,
        cost: component.cost,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableAssemblyComponents,
        componentWithIds.toMap(),
      );

      // خصم المكونات من المخزون
      await _deductComponentFromInventory(
        component.partId,
        component.quantity,
        assemblyWithId.id!,
      );
    }

    // إضافة المنتج المجمع إلى المخزون
    await _addAssembledProductToInventory(
      assemblyWithId.productId,
      assemblyWithId.quantity,
      assemblyWithId.id!,
    );

    return assemblyId;
  }

  // الحصول على جميع عمليات التجميع
  Future<List<Assembly>> getAllAssemblies() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableAssembly,
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getAssemblyComponents(map['id']);
        return Assembly.fromMap(map, components);
      }).toList(),
    );
  }

  // الحصول على عمليات التجميع بتاريخ معين
  Future<List<Assembly>> getAssembliesByDate(DateTime date) async {
    final dateStr = date.toString().split(' ')[0];

    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableAssembly} WHERE date(date) = ?',
      [dateStr],
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getAssemblyComponents(map['id']);
        return Assembly.fromMap(map, components);
      }).toList(),
    );
  }

  // الحصول على عمليات التجميع حسب نطاق تاريخي
  Future<List<Assembly>> getAssembliesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final startDateStr = startDate.toString().split(' ')[0];
    final endDateStr = endDate.toString().split(' ')[0];

    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableAssembly} WHERE date(date) >= ? AND date(date) <= ?',
      [startDateStr, endDateStr],
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getAssemblyComponents(map['id']);
        return Assembly.fromMap(map, components);
      }).toList(),
    );
  }

  // الحصول على عمليات التجميع بمعرف المنتج
  Future<List<Assembly>> getAssembliesByProduct(String productId) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableAssembly} WHERE productId = ?',
      [productId],
    );

    return Future.wait(
      maps.map((map) async {
        final components = await _getAssemblyComponents(map['id']);
        return Assembly.fromMap(map, components);
      }).toList(),
    );
  }

  // الحصول على عملية تجميع بمعرف معين
  Future<Assembly?> getAssemblyById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableAssembly,
      id,
    );

    if (map == null) {
      return null;
    }

    final components = await _getAssemblyComponents(id);
    return Assembly.fromMap(map, components);
  }

  // تحديث حالة عملية تجميع
  Future<int> updateAssemblyStatus(String id, String status) async {
    final assembly = await getAssemblyById(id);

    if (assembly == null) {
      return 0;
    }

    // تحديث حالة عملية التجميع
    return await _databaseHelper
        .rawQuery(
          'UPDATE ${DatabaseHelper.tableAssembly} SET status = ? WHERE id = ?',
          [status, id],
        )
        .then((_) => 1);
  }

  // حذف عملية تجميع
  Future<int> deleteAssembly(String id) async {
    // حذف مكونات التجميع أولاً
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableAssemblyComponents} WHERE assemblyId = ?',
      [id],
    );

    // ثم حذف عملية التجميع نفسها
    return await _databaseHelper.delete(DatabaseHelper.tableAssembly, id);
  }

  // الحصول على مكونات عملية تجميع
  Future<List<AssemblyComponentUsage>> _getAssemblyComponents(
    String assemblyId,
  ) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableAssemblyComponents} WHERE assemblyId = ?',
      [assemblyId],
    );

    return maps.map((map) => AssemblyComponentUsage.fromMap(map)).toList();
  }

  // حساب تكلفة التجميع للمنتج
  Future<Map<String, double>> calculateAssemblyCost(
    String productId,
    int quantity,
    double laborCostPerMinute,
    List<AssemblyComponentUsage> components,
  ) async {
    // الحصول على بيانات المنتج
    final productMap = await _databaseHelper.queryRow(
      DatabaseHelper.tableProducts,
      productId,
    );

    if (productMap == null) {
      return {
        'componentsCost': 0,
        'laborCost': 0,
        'packagingCost': 0,
        'totalCost': 0,
      };
    }

    // حساب تكلفة المكونات
    double componentsCost = components.fold(
      0,
      (sum, component) => sum + component.cost,
    );

    // حساب تكلفة العمالة
    double assemblyTime = productMap['assemblyTime'];
    double laborCost = assemblyTime * laborCostPerMinute * quantity;

    // حساب تكلفة التغليف
    double packagingCost = productMap['packagingCost'] * quantity;

    // حساب التكلفة الإجمالية
    double totalCost = componentsCost + laborCost + packagingCost;

    return {
      'componentsCost': componentsCost,
      'laborCost': laborCost,
      'packagingCost': packagingCost,
      'totalCost': totalCost,
    };
  }

  // خصم المكونات من المخزون
  Future<void> _deductComponentFromInventory(
    String partId,
    int quantity,
    String assemblyId,
  ) async {
    // تحديد نوع المكون (قطعة بلاستيكية أو إكسسوار)
    final String itemType = await _determineComponentType(partId);

    // الحصول على معرف المخزون للمكون
    final inventoryItem = await _inventoryRepository
        .getInventoryItemByItemIdAndType(partId, itemType);

    if (inventoryItem != null) {
      // خصم المكون من المخزون مع إضافة مرجع لعملية التجميع
      await _inventoryRepository.updateInventoryQuantity(
        inventoryItem.id!,
        -quantity.toDouble(),
        'assembly',
        assemblyId,
        notes: 'استخدام في عملية التجميع',
      );
    }
  }

  // تحديد نوع المكون
  Future<String> _determineComponentType(String partId) async {
    // التحقق من وجود المكون في جدول الإسطمبات أولاً
    final moldQuery = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableMolds} WHERE id = ?',
      [partId],
    );

    if (moldQuery.isNotEmpty) {
      return 'plastic_part';
    } else {
      return 'accessory';
    }
  }

  // إضافة المنتج المجمع إلى المخزون
  Future<void> _addAssembledProductToInventory(
    String productId,
    int quantity,
    String assemblyId,
  ) async {
    // التحقق مما إذا كان المنتج موجود بالفعل في المخزون
    final productInventory = await _inventoryRepository
        .getInventoryItemByItemIdAndType(productId, 'product');

    if (productInventory != null) {
      // تحديث كمية المنتج المجمع في المخزون
      await _inventoryRepository.updateInventoryQuantity(
        productInventory.id!,
        quantity.toDouble(),
        'assembly',
        assemblyId,
        notes: 'إضافة منتج مجمع إلى المخزون',
      );
    } else {
      // الحصول على بيانات المنتج
      final productMap = await _databaseHelper.queryRow(
        DatabaseHelper.tableProducts,
        productId,
      );

      if (productMap != null) {
        // إنشاء عنصر مخزون جديد للمنتج المجمع
        final newInventoryItem = Inventory(
          itemId: productId,
          itemType: 'product',
          itemName: productMap['name'],
          itemCode: productMap['code'],
          currentQuantity: quantity.toDouble(),
          minimumLevel: 0.0,
          reorderLevel: 10.0,
          unit: 'قطعة',
          unitCost: 0.0, // سيتم حسابها لاحقًا
          lastUpdate: DateTime.now(),
          status: InventoryStatus.normal,
        );

        // إضافة المنتج إلى المخزون
        await _inventoryRepository.createInventoryItem(newInventoryItem);
      }
    }
  }

  // الحصول على إحصائيات التجميع
  Future<Map<String, dynamic>> getAssemblyStatistics() async {
    // إجمالي عدد المنتجات المجمعة
    final totalQuantityResult = await _databaseHelper.rawQuery(
      'SELECT SUM(quantity) as total FROM ${DatabaseHelper.tableAssembly}',
    );

    final int totalQuantity = totalQuantityResult.first['total'] ?? 0;

    // إجمالي عدد عمليات التجميع
    final totalAssembliesResult = await _databaseHelper.rawQuery(
      'SELECT COUNT(id) as count FROM ${DatabaseHelper.tableAssembly}',
    );

    final int totalAssemblies = totalAssembliesResult.first['count'] ?? 0;

    // إجمالي تكلفة التجميع
    final totalCostResult = await _databaseHelper.rawQuery(
      'SELECT SUM(totalAssemblyCost + totalPackagingCost) as cost FROM ${DatabaseHelper.tableAssembly}',
    );

    final double totalCost = totalCostResult.first['cost'] ?? 0;

    // متوسط تكلفة التجميع للقطعة الواحدة
    final double averageCost =
        totalQuantity > 0 ? totalCost / totalQuantity : 0;

    return {
      'totalQuantity': totalQuantity,
      'totalAssemblies': totalAssemblies,
      'totalCost': totalCost,
      'averageCost': averageCost,
    };
  }
}
