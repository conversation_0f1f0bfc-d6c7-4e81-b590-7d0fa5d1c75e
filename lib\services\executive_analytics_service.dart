import 'package:flutter/foundation.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/maintenance_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/services/maintenance_service.dart';

/// خدمة تحليل البيانات التنفيذية مع طرق مساعدة للحصول على البيانات
extension ProductionRepositoryExtension on ProductionRepository {
  /// حساب إجمالي الإنتاج في فترة زمنية
  Future<int> getTotalProductionInPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final productions = await getAllProduction();
      return productions
          .where(
            (p) =>
                p.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                p.date.isBefore(endDate.add(const Duration(days: 1))),
          )
          .fold<int>(0, (sum, p) => sum + p.partsProduced);
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي الإنتاج: $e');
      return 0;
    }
  }

  /// الحصول على الإنتاج حسب الماكينة والفترة الزمنية
  Future<List<Production>> getProductionByMachineAndDateRange(
    String machineId,
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final productions = await getAllProduction();
      return productions
          .where(
            (p) =>
                p.machineId == machineId &&
                p.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                p.date.isBefore(endDate.add(const Duration(days: 1))),
          )
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على إنتاج الماكينة: $e');
      return [];
    }
  }

  /// الحصول على الإنتاج في فترة زمنية
  Future<List<Production>> getProductionInDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final productions = await getAllProduction();
      return productions
          .where(
            (p) =>
                p.date.isAfter(startDate.subtract(const Duration(days: 1))) &&
                p.date.isBefore(endDate.add(const Duration(days: 1))),
          )
          .toList();
    } catch (e) {
      debugPrint('خطأ في الحصول على الإنتاج في الفترة: $e');
      return [];
    }
  }
}

/// امتداد لمستودع المبيعات
extension SalesRepositoryExtension on SalesRepository {
  /// حساب إجمالي المبيعات في فترة زمنية
  Future<double> getTotalSalesInPeriod(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final sales = await getSalesByDateRange(startDate, endDate);
      return sales.fold<double>(0.0, (sum, sale) => sum + sale.totalAmount);
    } catch (e) {
      debugPrint('خطأ في حساب إجمالي المبيعات: $e');
      return 0.0;
    }
  }
}

class ExecutiveAnalyticsService {
  final ProductionRepository _productionRepository = ProductionRepository();
  final MaintenanceRepository _maintenanceRepository = MaintenanceRepository();
  final SalesRepository _salesRepository = SalesRepository();
  final MachineRepository _machineRepository = MachineRepository();
  final MaintenanceService _maintenanceService = MaintenanceService();

  /// حساب المؤشرات الرئيسية للأداء
  Future<List<KPI>> calculateKPIs({int days = 30}) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    final kpis = <KPI>[];

    // مؤشرات الإنتاج
    kpis.addAll(await _calculateProductionKPIs(startDate, endDate));

    // مؤشرات الصيانة
    kpis.addAll(await _calculateMaintenanceKPIs(startDate, endDate));

    // مؤشرات مالية
    kpis.addAll(await _calculateFinancialKPIs(startDate, endDate));

    // مؤشرات الجودة
    kpis.addAll(await _calculateQualityKPIs(startDate, endDate));

    return kpis;
  }

  /// حساب مؤشرات الإنتاج
  Future<List<KPI>> _calculateProductionKPIs(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final kpis = <KPI>[];

    try {
      // إجمالي الإنتاج
      final totalProduction = await _productionRepository
          .getTotalProductionInPeriod(startDate, endDate);
      final previousProduction = await _productionRepository
          .getTotalProductionInPeriod(
            startDate.subtract(
              Duration(days: endDate.difference(startDate).inDays),
            ),
            startDate,
          );

      kpis.add(
        KPI(
          id: 'total_production',
          name: 'إجمالي الإنتاج',
          description: 'إجمالي القطع المنتجة في الفترة',
          currentValue: totalProduction.toDouble(),
          targetValue: 10000, // هدف افتراضي
          previousValue: previousProduction.toDouble(),
          type: KPIType.higherIsBetter,
          unit: KPIUnit.pieces,
          trend:
              totalProduction > previousProduction
                  ? KPITrend.up
                  : totalProduction < previousProduction
                  ? KPITrend.down
                  : KPITrend.stable,
          lastUpdated: DateTime.now(),
          icon: 'production',
          color: '#2196F3',
        ),
      );

      // كفاءة الإنتاج
      final efficiency = await _calculateProductionEfficiency(
        startDate,
        endDate,
      );
      final previousEfficiency = await _calculateProductionEfficiency(
        startDate.subtract(
          Duration(days: endDate.difference(startDate).inDays),
        ),
        startDate,
      );

      kpis.add(
        KPI(
          id: 'production_efficiency',
          name: 'كفاءة الإنتاج',
          description: 'متوسط كفاءة الماكينات في الإنتاج',
          currentValue: efficiency,
          targetValue: 85.0,
          previousValue: previousEfficiency,
          type: KPIType.higherIsBetter,
          unit: KPIUnit.percentage,
          trend:
              efficiency > previousEfficiency
                  ? KPITrend.up
                  : efficiency < previousEfficiency
                  ? KPITrend.down
                  : KPITrend.stable,
          lastUpdated: DateTime.now(),
          icon: 'efficiency',
          color: '#4CAF50',
        ),
      );

      // استغلال الطاقة الإنتاجية
      final capacityUtilization = await _calculateCapacityUtilization(
        startDate,
        endDate,
      );

      kpis.add(
        KPI(
          id: 'capacity_utilization',
          name: 'استغلال الطاقة',
          description: 'نسبة استغلال الطاقة الإنتاجية المتاحة',
          currentValue: capacityUtilization,
          targetValue: 80.0,
          previousValue: 75.0, // قيمة افتراضية
          type: KPIType.higherIsBetter,
          unit: KPIUnit.percentage,
          trend: KPITrend.up,
          lastUpdated: DateTime.now(),
          icon: 'capacity',
          color: '#FF9800',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في حساب مؤشرات الإنتاج: $e');
    }

    return kpis;
  }

  /// حساب مؤشرات الصيانة
  Future<List<KPI>> _calculateMaintenanceKPIs(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final kpis = <KPI>[];

    try {
      final maintenanceAnalysis =
          await _maintenanceService.analyzeMaintenanceEffectiveness();

      // متوسط الوقت بين الأعطال (MTBF)
      final mtbf = maintenanceAnalysis['mtbf'] as double;
      kpis.add(
        KPI(
          id: 'mtbf',
          name: 'متوسط الوقت بين الأعطال',
          description: 'متوسط الوقت بين حدوث الأعطال',
          currentValue: mtbf,
          targetValue: 168.0, // أسبوع
          previousValue: 120.0, // قيمة افتراضية
          type: KPIType.higherIsBetter,
          unit: KPIUnit.hours,
          trend: mtbf > 120 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'mtbf',
          color: '#9C27B0',
        ),
      );

      // متوسط وقت الإصلاح (MTTR)
      final mttr = maintenanceAnalysis['mttr'] as double;
      kpis.add(
        KPI(
          id: 'mttr',
          name: 'متوسط وقت الإصلاح',
          description: 'متوسط الوقت المطلوب لإصلاح الأعطال',
          currentValue: mttr,
          targetValue: 4.0,
          previousValue: 6.0, // قيمة افتراضية
          type: KPIType.lowerIsBetter,
          unit: KPIUnit.hours,
          trend: mttr < 6 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'mttr',
          color: '#F44336',
        ),
      );

      // معدل التوفر
      final availability = maintenanceAnalysis['availability'] as double;
      kpis.add(
        KPI(
          id: 'availability',
          name: 'معدل التوفر',
          description: 'نسبة الوقت المتاح للإنتاج',
          currentValue: availability,
          targetValue: 95.0,
          previousValue: 90.0, // قيمة افتراضية
          type: KPIType.higherIsBetter,
          unit: KPIUnit.percentage,
          trend: availability > 90 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'availability',
          color: '#00BCD4',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في حساب مؤشرات الصيانة: $e');
    }

    return kpis;
  }

  /// حساب المؤشرات المالية
  Future<List<KPI>> _calculateFinancialKPIs(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final kpis = <KPI>[];

    try {
      // إجمالي المبيعات
      final totalSales = await _salesRepository.getTotalSalesInPeriod(
        startDate,
        endDate,
      );
      final previousSales = await _salesRepository.getTotalSalesInPeriod(
        startDate.subtract(
          Duration(days: endDate.difference(startDate).inDays),
        ),
        startDate,
      );

      kpis.add(
        KPI(
          id: 'total_sales',
          name: 'إجمالي المبيعات',
          description: 'إجمالي قيمة المبيعات في الفترة',
          currentValue: totalSales,
          targetValue: 500000.0, // هدف افتراضي
          previousValue: previousSales,
          type: KPIType.higherIsBetter,
          unit: KPIUnit.currency,
          trend:
              totalSales > previousSales
                  ? KPITrend.up
                  : totalSales < previousSales
                  ? KPITrend.down
                  : KPITrend.stable,
          lastUpdated: DateTime.now(),
          icon: 'sales',
          color: '#4CAF50',
        ),
      );

      // تكلفة الإنتاج
      final productionCost = await _calculateProductionCost(startDate, endDate);

      kpis.add(
        KPI(
          id: 'production_cost',
          name: 'تكلفة الإنتاج',
          description: 'إجمالي تكلفة الإنتاج في الفترة',
          currentValue: productionCost,
          targetValue: 300000.0, // هدف افتراضي
          previousValue: 320000.0, // قيمة افتراضية
          type: KPIType.lowerIsBetter,
          unit: KPIUnit.currency,
          trend: productionCost < 320000 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'cost',
          color: '#FF5722',
        ),
      );

      // هامش الربح
      final profitMargin =
          totalSales > 0
              ? ((totalSales - productionCost) / totalSales) * 100
              : 0;

      kpis.add(
        KPI(
          id: 'profit_margin',
          name: 'هامش الربح',
          description: 'نسبة الربح من إجمالي المبيعات',
          currentValue: profitMargin.toDouble(),
          targetValue: 25.0,
          previousValue: 20.0, // قيمة افتراضية
          type: KPIType.higherIsBetter,
          unit: KPIUnit.percentage,
          trend: profitMargin > 20 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'profit',
          color: '#8BC34A',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في حساب المؤشرات المالية: $e');
    }

    return kpis;
  }

  /// حساب مؤشرات الجودة
  Future<List<KPI>> _calculateQualityKPIs(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final kpis = <KPI>[];

    try {
      // معدل العيوب (افتراضي)
      final defectRate = 2.5; // نسبة افتراضية

      kpis.add(
        KPI(
          id: 'defect_rate',
          name: 'معدل العيوب',
          description: 'نسبة المنتجات المعيبة',
          currentValue: defectRate,
          targetValue: 2.0,
          previousValue: 3.0,
          type: KPIType.lowerIsBetter,
          unit: KPIUnit.percentage,
          trend: defectRate < 3.0 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'quality',
          color: '#E91E63',
        ),
      );

      // معدل الإنجاز من المرة الأولى
      final firstPassYield = 95.0; // نسبة افتراضية

      kpis.add(
        KPI(
          id: 'first_pass_yield',
          name: 'الإنجاز من المرة الأولى',
          description: 'نسبة المنتجات المنجزة بشكل صحيح من المرة الأولى',
          currentValue: firstPassYield,
          targetValue: 98.0,
          previousValue: 93.0,
          type: KPIType.higherIsBetter,
          unit: KPIUnit.percentage,
          trend: firstPassYield > 93.0 ? KPITrend.up : KPITrend.down,
          lastUpdated: DateTime.now(),
          icon: 'first_pass',
          color: '#607D8B',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في حساب مؤشرات الجودة: $e');
    }

    return kpis;
  }

  /// حساب كفاءة الإنتاج
  Future<double> _calculateProductionEfficiency(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final machines = await _machineRepository.getAllMachines();
      if (machines.isEmpty) return 0.0;

      double totalEfficiency = 0.0;
      int count = 0;

      for (var machine in machines) {
        final productions = await _productionRepository
            .getProductionByMachineAndDateRange(
              machine.id!,
              startDate,
              endDate,
            );

        if (productions.isNotEmpty) {
          // حساب كفاءة بسيط بناءً على وقت الدورة
          final avgCycleTime =
              productions.fold(0.0, (sum, p) => sum + p.cycleTime) /
              productions.length;
          final efficiency =
              (30.0 / avgCycleTime) * 100; // افتراض وقت دورة مثالي 30 ثانية
          totalEfficiency += efficiency.clamp(0.0, 100.0);
          count++;
        }
      }

      return count > 0 ? totalEfficiency / count : 0.0;
    } catch (e) {
      debugPrint('خطأ في حساب كفاءة الإنتاج: $e');
      return 0.0;
    }
  }

  /// حساب استغلال الطاقة الإنتاجية
  Future<double> _calculateCapacityUtilization(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final machines = await _machineRepository.getAllMachines();
      if (machines.isEmpty) return 0.0;

      final totalHours = endDate.difference(startDate).inHours.toDouble();
      final availableCapacity = machines.length * totalHours;

      double usedCapacity = 0.0;

      for (var machine in machines) {
        final productions = await _productionRepository
            .getProductionByMachineAndDateRange(
              machine.id!,
              startDate,
              endDate,
            );

        for (var production in productions) {
          final runTime =
              (production.partsProduced * production.cycleTime) /
              3600; // تحويل إلى ساعات
          usedCapacity += runTime * production.shiftsCount;
        }
      }

      return availableCapacity > 0
          ? (usedCapacity / availableCapacity) * 100
          : 0.0;
    } catch (e) {
      debugPrint('خطأ في حساب استغلال الطاقة: $e');
      return 0.0;
    }
  }

  /// حساب تكلفة الإنتاج
  Future<double> _calculateProductionCost(
    DateTime startDate,
    DateTime endDate,
  ) async {
    try {
      final productions = await _productionRepository.getProductionInDateRange(
        startDate,
        endDate,
      );

      double totalCost = 0.0;

      for (var production in productions) {
        totalCost += production.electricityCost + production.operatorCost;
        // إضافة تكلفة المواد الخام (سعر الكيلو × الكمية المستخدمة)
        // يمكن تحسين هذا بالحصول على سعر المادة الخام الفعلي
        totalCost +=
            production.rawMaterialUsed * 10; // سعر افتراضي 10 ريال للكيلو
      }

      return totalCost;
    } catch (e) {
      debugPrint('خطأ في حساب تكلفة الإنتاج: $e');
      return 0.0;
    }
  }

  /// إنشاء ملخص الأداء العام
  Future<PerformanceSummary> generatePerformanceSummary({int days = 30}) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    try {
      // حساب مؤشرات الإنتاج
      final productionEfficiency = await _calculateProductionEfficiency(
        startDate,
        endDate,
      );
      final totalProduction = await _productionRepository
          .getTotalProductionInPeriod(startDate, endDate);
      final capacityUtilization = await _calculateCapacityUtilization(
        startDate,
        endDate,
      );
      final oeeScore =
          (productionEfficiency * capacityUtilization * 95.0) /
          10000; // OEE مبسط

      final production = ProductionMetrics(
        efficiency: productionEfficiency,
        totalProduction: totalProduction,
        capacityUtilization: capacityUtilization,
        oeeScore: oeeScore,
      );

      // حساب مؤشرات الصيانة
      final maintenanceAnalysis =
          await _maintenanceService.analyzeMaintenanceEffectiveness();
      final maintenance = MaintenanceMetrics(
        mtbf: maintenanceAnalysis['mtbf'] as double,
        mttr: maintenanceAnalysis['mttr'] as double,
        availability: maintenanceAnalysis['availability'] as double,
        maintenanceCostRatio: 5.0, // نسبة افتراضية
      );

      // حساب المؤشرات المالية
      final totalSales = await _salesRepository.getTotalSalesInPeriod(
        startDate,
        endDate,
      );
      final productionCost = await _calculateProductionCost(startDate, endDate);
      final profitMargin =
          totalSales > 0
              ? ((totalSales - productionCost) / totalSales) * 100
              : 0;

      final financial = FinancialMetrics(
        totalRevenue: totalSales,
        totalCosts: productionCost,
        profitMargin: profitMargin.toDouble(),
        roi: 15.0, // عائد افتراضي
      );

      // مؤشرات الجودة (افتراضية)
      final quality = QualityMetrics(
        defectRate: 2.5,
        customerSatisfaction: 85.0,
        firstPassYield: 95.0,
        reworkRate: 3.0,
      );

      // حساب النتيجة الإجمالية
      final overallScore =
          ((production.efficiency * 0.3) +
              (maintenance.availability * 0.25) +
              (financial.profitMargin * 0.25) +
              ((100 - quality.defectRate) * 0.2));

      return PerformanceSummary(
        overallScore: overallScore,
        production: production,
        maintenance: maintenance,
        financial: financial,
        quality: quality,
        calculatedAt: DateTime.now(),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء ملخص الأداء: $e');
      // إرجاع قيم افتراضية في حالة الخطأ
      return PerformanceSummary(
        overallScore: 0.0,
        production: ProductionMetrics(
          efficiency: 0,
          totalProduction: 0,
          capacityUtilization: 0,
          oeeScore: 0,
        ),
        maintenance: MaintenanceMetrics(
          mtbf: 0,
          mttr: 0,
          availability: 0,
          maintenanceCostRatio: 0,
        ),
        financial: FinancialMetrics(
          totalRevenue: 0,
          totalCosts: 0,
          profitMargin: 0,
          roi: 0,
        ),
        quality: QualityMetrics(
          defectRate: 0,
          customerSatisfaction: 0,
          firstPassYield: 0,
          reworkRate: 0,
        ),
        calculatedAt: DateTime.now(),
      );
    }
  }

  /// إنشاء بيانات الرسوم البيانية
  Future<List<ChartData>> generateChartData(
    String chartType, {
    int days = 30,
  }) async {
    final endDate = DateTime.now();
    final startDate = endDate.subtract(Duration(days: days));

    switch (chartType) {
      case 'production_trend':
        return await _generateProductionTrendData(startDate, endDate);
      case 'machine_efficiency':
        return await _generateMachineEfficiencyData();
      case 'maintenance_costs':
        return await _generateMaintenanceCostData(startDate, endDate);
      case 'sales_revenue':
        return await _generateSalesRevenueData(startDate, endDate);
      default:
        return [];
    }
  }

  /// إنشاء بيانات اتجاه الإنتاج
  Future<List<ChartData>> _generateProductionTrendData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final data = <ChartData>[];

    try {
      final days = endDate.difference(startDate).inDays;

      for (int i = 0; i < days; i++) {
        final date = startDate.add(Duration(days: i));
        final nextDate = date.add(const Duration(days: 1));

        final dailyProduction = await _productionRepository
            .getTotalProductionInPeriod(date, nextDate);

        data.add(
          ChartData(
            label: '${date.day}/${date.month}',
            value: dailyProduction.toDouble(),
            date: date,
            color: '#2196F3',
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء بيانات اتجاه الإنتاج: $e');
    }

    return data;
  }

  /// إنشاء بيانات كفاءة الماكينات
  Future<List<ChartData>> _generateMachineEfficiencyData() async {
    final data = <ChartData>[];

    try {
      final machines = await _machineRepository.getAllMachines();

      for (var machine in machines) {
        final efficiency = await _calculateMachineEfficiency(machine.id!);

        data.add(
          ChartData(
            label: machine.name,
            value: efficiency,
            color:
                efficiency >= 80
                    ? '#4CAF50'
                    : efficiency >= 60
                    ? '#FF9800'
                    : '#F44336',
            metadata: {'machineId': machine.id},
          ),
        );
      }
    } catch (e) {
      debugPrint('خطأ في إنشاء بيانات كفاءة الماكينات: $e');
    }

    return data;
  }

  /// حساب كفاءة ماكينة واحدة
  Future<double> _calculateMachineEfficiency(String machineId) async {
    try {
      final endDate = DateTime.now();
      final startDate = endDate.subtract(const Duration(days: 30));

      final productions = await _productionRepository
          .getProductionByMachineAndDateRange(machineId, startDate, endDate);

      if (productions.isEmpty) return 0.0;

      final avgCycleTime =
          productions.fold(0.0, (sum, p) => sum + p.cycleTime) /
          productions.length;
      return (30.0 / avgCycleTime) * 100; // افتراض وقت دورة مثالي 30 ثانية
    } catch (e) {
      return 0.0;
    }
  }

  /// إنشاء بيانات تكاليف الصيانة
  Future<List<ChartData>> _generateMaintenanceCostData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final data = <ChartData>[];

    try {
      final maintenanceStats =
          await _maintenanceRepository.getMaintenanceStats();
      final recordStats =
          maintenanceStats['recordStats'] as Map<String, dynamic>;

      final totalCost = (recordStats['totalCost'] as num?)?.toDouble() ?? 0.0;

      data.add(
        ChartData(label: 'تكلفة الصيانة', value: totalCost, color: '#FF5722'),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء بيانات تكاليف الصيانة: $e');
    }

    return data;
  }

  /// إنشاء بيانات إيرادات المبيعات
  Future<List<ChartData>> _generateSalesRevenueData(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final data = <ChartData>[];

    try {
      final totalSales = await _salesRepository.getTotalSalesInPeriod(
        startDate,
        endDate,
      );

      data.add(
        ChartData(
          label: 'إجمالي المبيعات',
          value: totalSales,
          color: '#4CAF50',
        ),
      );
    } catch (e) {
      debugPrint('خطأ في إنشاء بيانات إيرادات المبيعات: $e');
    }

    return data;
  }
}
