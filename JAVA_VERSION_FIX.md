# إصلاح تحذيرات إصدار Java المهجور

## المشكلة

عند بناء تطبيق Flutter، تظهر التحذيرات التالية:

```
warning: [options] source value 8 is obsolete and will be removed in a future release
warning: [options] target value 8 is obsolete and will be removed in a future release
warning: [options] To suppress warnings about obsolete options, use -Xlint:-options.
```

هذه التحذيرات تشير إلى أن المشروع يستخدم إصدار Java 8 الذي أصبح مهجورًا وسيتم إزالته في إصدار مستقبلي.

## الحلول المنفذة

تم تنفيذ الإصلاحات التالية لحل مشكلة تحذيرات إصدار Java المهجور:

### 1. تحديث إصدار Java في ملف build.gradle.kts

تم تحديث إصدار Java من 11 إلى 17 في ملف `android/app/build.gradle.kts`:

```kotlin
compileOptions {
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
}

kotlinOptions {
    jvmTarget = JavaVersion.VERSION_17.toString()
}
```

### 2. تحديث إصدار Java في ملف gradle.properties

تم إضافة إعدادات Java في ملف `android/gradle.properties`:

```properties
org.gradle.java.home=${JAVA_HOME}
org.gradle.warning.mode=none
```

### 3. إضافة إعدادات لتجاهل تحذيرات Java المهجورة

تم إضافة إعدادات لتجاهل تحذيرات Java المهجورة في ملف `android/build.gradle.kts`:

```kotlin
allprojects {
    // ...
    
    // Suppress Java version warnings
    tasks.withType<JavaCompile> {
        options.compilerArgs.add("-Xlint:-options")
    }
}
```

### 4. تحديث إصدار Kotlin

تم تحديث إصدار Kotlin من 1.8.22 إلى 1.9.22 في ملف `android/settings.gradle.kts`:

```kotlin
plugins {
    id("dev.flutter.flutter-plugin-loader") version "1.0.0"
    id("com.android.application") version "8.7.0" apply false
    id("org.jetbrains.kotlin.android") version "1.9.22" apply false
}
```

### 5. إنشاء ملف jdk-release

تم إنشاء ملف `android/.jdk-release` لتحديد إصدار JDK المستخدم:

```
17
```

## كيفية التحقق من الإصلاح

بعد تنفيذ الإصلاحات المذكورة أعلاه، يجب أن تختفي تحذيرات إصدار Java المهجور عند بناء التطبيق. للتحقق من ذلك، قم بتنفيذ الأوامر التالية:

```bash
flutter clean
flutter pub get
flutter build apk
```

## ملاحظات إضافية

1. تأكد من تثبيت JDK 17 على جهازك.
2. تأكد من تعيين متغير البيئة `JAVA_HOME` ليشير إلى مجلد JDK 17.
3. إذا استمرت التحذيرات، يمكنك تجربة إضافة الخيار `-Xlint:-options` إلى متغير البيئة `JAVA_OPTS`.

## المراجع

- [تحديث إصدار Java في Flutter](https://flutter.dev/docs/development/tools/sdk/upgrading)
- [إعدادات Gradle في Flutter](https://flutter.dev/docs/development/tools/sdk/upgrading#gradle-settings)
- [تحديث إصدار Kotlin في Flutter](https://flutter.dev/docs/development/tools/sdk/upgrading#kotlin-version)
