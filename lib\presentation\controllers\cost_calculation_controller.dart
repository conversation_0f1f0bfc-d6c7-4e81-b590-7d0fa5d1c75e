import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/utils/logger.dart';

// ignore_for_file: prefer_const_declarations

class CostCalculationController extends GetxController {
  final MachineRepository _machineRepository = Get.find<MachineRepository>();
  final MoldRepository _moldRepository = Get.find<MoldRepository>();
  final ProductRepository _productRepository = Get.find<ProductRepository>();
  final RawMaterialRepository _rawMaterialRepository =
      Get.find<RawMaterialRepository>();

  // متغيرات تفاعلية
  final RxBool isLoading = true.obs;
  final RxBool isCalculating = false.obs;

  // بيانات الماكينات والإسطمبات
  final RxList<Machine> machines = <Machine>[].obs;
  final RxList<Mold> molds = <Mold>[].obs;

  // متغيرات حساب التكلفة
  final RxDouble electricityCost = 0.0.obs;
  final RxDouble operatorCost = 0.0.obs;
  final RxDouble materialCost = 0.0.obs;
  final RxDouble totalCost = 0.0.obs;
  final RxDouble singlePartCost = 0.0.obs;

  // معلومات الإنتاج
  final RxInt partsProduced = 0.obs;
  final RxDouble materialWeight = 0.0.obs;
  final RxDouble cycleTime = 0.0.obs;
  final RxInt cavityCount = 0.obs;

  // معلومات الماكينة المختارة
  final Rx<Machine?> selectedMachine = Rx<Machine?>(null);
  final Rx<Mold?> selectedMold = Rx<Mold?>(null);

  // معدلات التكلفة
  final RxDouble electricityRate = 1.5.obs; // سعر الكيلووات/ساعة
  final RxDouble operatorHourlyRate = 25.0.obs; // تكلفة المشغل بالساعة
  final RxDouble materialPricePerKg =
      50.0.obs; // سعر الكيلوجرام من المادة الخام

  // معدلات التكلفة للمنتج النهائي
  final RxDouble laborCostPerMinute = 4.0.obs; // تكلفة العمالة بالدقيقة
  final RxDouble overheadPercentage = 15.0.obs; // نسبة التكاليف الإضافية (%)
  final RxDouble profitMarginPercentage = 25.0.obs; // نسبة هامش الربح (%)

  @override
  void onInit() {
    super.onInit();
    fetchData();
  }

  // جلب البيانات الأساسية
  Future<void> fetchData() async {
    isLoading.value = true;
    try {
      await Future.wait([_fetchMachines(), _fetchMolds()]);
    } catch (e) {
      AppLogger.e('Error fetching cost calculation data', e);
    } finally {
      isLoading.value = false;
    }
  }

  // جلب بيانات الماكينات
  Future<void> _fetchMachines() async {
    try {
      final machinesList = await _machineRepository.getAllMachines();
      machines.assignAll(machinesList);
    } catch (e) {
      AppLogger.e('Error fetching machines', e);
    }
  }

  // جلب بيانات الإسطمبات
  Future<void> _fetchMolds() async {
    try {
      final moldsList = await _moldRepository.getAllMolds();
      molds.assignAll(moldsList);
    } catch (e) {
      AppLogger.e('Error fetching molds', e);
    }
  }

  // اختيار ماكينة
  void selectMachine(Machine machine) {
    selectedMachine.value = machine;
    // إعادة ضبط الإسطمبة إذا تم تغيير الماكينة
    selectedMold.value = null;
  }

  // اختيار إسطمبة
  void selectMold(Mold mold) {
    selectedMold.value = mold;
    cavityCount.value = mold.cavityCount;
    materialWeight.value = mold.singlePartWeight * cavityCount.value;
  }

  // تعيين معدل استهلاك الكهرباء
  void setElectricityRate(double rate) {
    electricityRate.value = rate;
  }

  // تعيين معدل أجر المشغل
  void setOperatorRate(double rate) {
    operatorHourlyRate.value = rate;
  }

  // تعيين سعر المادة الخام
  void setMaterialPrice(double price) {
    materialPricePerKg.value = price;
  }

  // تعيين عدد القطع المنتجة
  void setPartsProduced(int count) {
    partsProduced.value = count;
  }

  // تعيين زمن الدورة
  void setCycleTime(double time) {
    cycleTime.value = time;
  }

  // تعيين تكلفة العمالة بالدقيقة
  void setLaborCostPerMinute(double cost) {
    laborCostPerMinute.value = cost;
  }

  // تعيين نسبة التكاليف الإضافية
  void setOverheadPercentage(double percentage) {
    overheadPercentage.value = percentage;
  }

  // تعيين نسبة هامش الربح
  void setProfitMarginPercentage(double percentage) {
    profitMarginPercentage.value = percentage;
  }

  // حساب تكلفة الحقن
  Future<void> calculateInjectionCost() async {
    isCalculating.value = true;

    try {
      if (selectedMachine.value == null || selectedMold.value == null) {
        return;
      }

      // حساب تكلفة الكهرباء
      const double hoursInDay = 8.0; // عدد ساعات العمل في اليوم
      final double machineConsumption = selectedMachine.value!.powerConsumption;
      electricityCost.value =
          machineConsumption * hoursInDay * electricityRate.value;

      // حساب تكلفة المشغل
      operatorCost.value = hoursInDay * operatorHourlyRate.value;

      // حساب تكلفة المادة الخام
      final double totalWeight =
          materialWeight.value *
          partsProduced.value /
          1000; // تحويل جرام إلى كيلوجرام
      materialCost.value = totalWeight * materialPricePerKg.value;

      // حساب التكلفة الإجمالية
      totalCost.value =
          electricityCost.value + operatorCost.value + materialCost.value;

      // حساب تكلفة القطعة الواحدة
      if (partsProduced.value > 0) {
        singlePartCost.value = totalCost.value / partsProduced.value;
      }
    } catch (e) {
      AppLogger.e('Error calculating injection cost', e);
    } finally {
      isCalculating.value = false;
    }
  }

  // حساب تكلفة الإنتاج لمنتج معين
  Future<Map<String, dynamic>> calculateProductionCost(String moldId) async {
    try {
      // الحصول على الإسطمبة
      final mold = await _moldRepository.getMoldById(moldId);
      if (mold == null) {
        return {'error': 'Mold not found'};
      }

      // الحصول على الماكينة المرتبطة بالإسطمبة
      final machineId = mold.currentMachineId;
      if (machineId == null) {
        return {'error': 'No machine associated with this mold'};
      }

      final machine = await _machineRepository.getMachineById(machineId);
      if (machine == null) {
        return {'error': 'Machine not found'};
      }

      // معدلات افتراضية للحساب
      const double hoursInDay = 8.0;
      const double electricityRate = 1.5;
      const double operatorRate = 25.0;
      const double materialRate = 50.0;

      // حساب معدل الإنتاج اليومي
      const double averageCycleTime = 30.0; // بالثواني
      final int cavities = mold.cavityCount;
      final int dailyProduction =
          ((hoursInDay * 3600) / averageCycleTime).floor() * cavities;

      // حساب تكلفة الكهرباء
      final double electricityCost =
          machine.powerConsumption * hoursInDay * electricityRate;

      // حساب تكلفة المشغل
      final double operatorCost = hoursInDay * operatorRate;

      // حساب تكلفة المادة الخام
      final double partWeight = mold.singlePartWeight;
      final double totalWeight =
          partWeight * dailyProduction / 1000; // تحويل جرام إلى كيلوجرام
      final double materialCost = totalWeight * materialRate;

      // حساب التكلفة الإجمالية
      final double totalCost = electricityCost + operatorCost + materialCost;

      // حساب تكلفة القطعة الواحدة
      final double singlePartCost = totalCost / dailyProduction;

      return {
        'dailyProduction': dailyProduction,
        'electricityCost': electricityCost,
        'operatorCost': operatorCost,
        'materialCost': materialCost,
        'totalCost': totalCost,
        'singlePartCost': singlePartCost,
      };
    } catch (e) {
      AppLogger.e('Error calculating production cost', e);
      return {'error': 'Failed to calculate cost'};
    }
  }

  // حساب تكلفة منتج نهائي
  Future<Map<String, dynamic>> calculateFinalProductCost(
    String productCode,
    int quantity,
  ) async {
    try {
      // الحصول على الإسطمبة المرتبطة بالمنتج
      final molds = await _moldRepository.getMoldsByProductCode(productCode);
      if (molds.isEmpty) {
        return {'error': 'No molds found for this product'};
      }

      double totalCost = 0.0;
      final List<Map<String, dynamic>> components = [];

      for (final mold in molds) {
        final costData = await calculateProductionCost(mold.id!);
        if (costData.containsKey('error')) {
          continue;
        }

        final double componentCost = costData['singlePartCost'] * quantity;
        totalCost += componentCost;

        components.add({
          'moldName': mold.name,
          'partWeight': mold.singlePartWeight,
          'cavityCount': mold.cavityCount,
          'singlePartCost': costData['singlePartCost'],
          'totalCost': componentCost,
        });
      }

      // إضافة تكلفة التجميع (افتراضية)
      const double assemblyRatePerPiece = 5.0;
      final double assemblyCost = assemblyRatePerPiece * quantity;
      totalCost += assemblyCost;

      // إضافة تكلفة التغليف (افتراضية)
      const double packagingRatePerPiece = 2.0;
      final double packagingCost = packagingRatePerPiece * quantity;
      totalCost += packagingCost;

      return {
        'productCode': productCode,
        'quantity': quantity,
        'components': components,
        'assemblyCost': assemblyCost,
        'packagingCost': packagingCost,
        'totalCost': totalCost,
        'unitCost': totalCost / quantity,
      };
    } catch (e) {
      AppLogger.e('Error calculating final product cost', e);
      return {'error': 'Failed to calculate final product cost'};
    }
  }

  // حساب تكلفة منتج بشكل تفصيلي
  Future<Map<String, dynamic>> calculateDetailedProductCost(
    String productId,
  ) async {
    try {
      // الحصول على المنتج
      final product = await _productRepository.getProductById(productId);
      if (product == null) {
        return {'error': 'المنتج غير موجود'};
      }

      // تكلفة المواد الخام
      double rawMaterialCost = 0.0;
      List<Map<String, dynamic>> rawMaterialDetails = [];

      // تكلفة الحقن
      double injectionCost = 0.0;
      List<Map<String, dynamic>> injectionDetails = [];

      // تكلفة الإكسسوارات
      double accessoriesCost = 0.0;
      List<Map<String, dynamic>> accessoryDetails = [];

      // حساب تكلفة المكونات
      for (var component in product.components) {
        if (component.type == ComponentType.plasticPart) {
          // مكون بلاستيكي (يتم إنتاجه محلياً)
          final mold = await _moldRepository.getMoldById(component.partId);
          if (mold != null) {
            final costData = await calculateProductionCost(mold.id!);
            if (!costData.containsKey('error')) {
              final componentCost =
                  costData['singlePartCost'] * component.quantity;
              injectionCost += componentCost;

              // استخدام متوسط سعر المواد الخام المتاحة أو قيمة افتراضية
              double materialCostPerKg = 50.0; // قيمة افتراضية

              // محاولة الحصول على متوسط تكلفة المواد الخام
              final rawMaterials =
                  await _rawMaterialRepository.getAllRawMaterials();
              if (rawMaterials.isNotEmpty) {
                double totalCost = 0.0;
                for (var material in rawMaterials) {
                  totalCost += material.costPerKg;
                }
                materialCostPerKg = totalCost / rawMaterials.length;
              }

              final partWeightKg =
                  mold.singlePartWeight / 1000; // تحويل من جرام إلى كيلوجرام
              final materialCost =
                  partWeightKg * materialCostPerKg * component.quantity;
              rawMaterialCost += materialCost;

              rawMaterialDetails.add({
                'name': 'مادة خام للجزء: ${mold.name}',
                'partName': mold.name,
                'weightPerPart': mold.singlePartWeight,
                'quantity': component.quantity,
                'costPerKg': materialCostPerKg,
                'totalCost': materialCost,
              });

              injectionDetails.add({
                'moldName': mold.name,
                'costPerPart': costData['singlePartCost'],
                'quantity': component.quantity,
                'totalCost': componentCost,
              });
            }
          }
        } else if (component.type == ComponentType.accessory) {
          // إكسسوار (غالباً مستورد)
          final itemCost = component.cost * component.quantity;
          accessoriesCost += itemCost;

          accessoryDetails.add({
            'name': component.name,
            'costPerUnit': component.cost,
            'quantity': component.quantity,
            'totalCost': itemCost,
          });
        }
      }

      // تكلفة التجميع
      final assemblyCost = product.assemblyTime * laborCostPerMinute.value;

      // تكلفة التغليف
      final packagingCost = product.packagingCost;

      // إجمالي التكلفة المباشرة
      final directCost =
          rawMaterialCost +
          injectionCost +
          accessoriesCost +
          assemblyCost +
          packagingCost;

      // حساب التكاليف الإضافية (Overhead)
      final overheadCost = directCost * (overheadPercentage.value / 100);

      // إجمالي التكلفة
      final totalCost = directCost + overheadCost;

      // حساب السعر المقترح بناءً على هامش الربح
      final suggestedPrice =
          totalCost / (1 - (profitMarginPercentage.value / 100));

      return {
        'productId': productId,
        'productName': product.name,
        'productCode': product.code,
        'rawMaterialCost': rawMaterialCost,
        'rawMaterialDetails': rawMaterialDetails,
        'injectionCost': injectionCost,
        'injectionDetails': injectionDetails,
        'accessoriesCost': accessoriesCost,
        'accessoryDetails': accessoryDetails,
        'assemblyCost': assemblyCost,
        'packagingCost': packagingCost,
        'directCost': directCost,
        'overheadCost': overheadCost,
        'overheadPercentage': overheadPercentage.value,
        'totalCost': totalCost,
        'profitMarginPercentage': profitMarginPercentage.value,
        'suggestedPrice': suggestedPrice,
      };
    } catch (e) {
      AppLogger.e('Error calculating detailed product cost', e);
      return {'error': 'فشل حساب تكلفة المنتج بالتفصيل: $e'};
    }
  }
}
