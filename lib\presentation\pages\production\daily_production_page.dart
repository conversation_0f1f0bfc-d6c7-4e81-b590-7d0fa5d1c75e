import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/machine.dart';
import 'package:mostafa_final/data/models/mold.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:mostafa_final/data/models/raw_material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'daily_production_controller.dart';

class DailyProductionPage extends StatelessWidget {
  const DailyProductionPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final DailyProductionController controller = Get.put(
      DailyProductionController(),
    );

    return ResponsivePage(
      title: 'تسجيل الإنتاج اليومي',
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDateSelector(controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildProductionForm(controller),
              SizedBox(height: ScreenSize.getPadding(24)),
              _buildProductionHistory(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildDateSelector(DailyProductionController controller) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextUtils.responsiveText(
              'تاريخ الإنتاج',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextUtils.responsiveText(
                      controller.formatDate(controller.selectedDate.value),
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        color: Colors.grey.shade700,
                      ),
                      maxLines: 1,
                    ),
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () => controller.selectDate(Get.context!),
                  icon: Icon(
                    Icons.calendar_today,
                    size: ScreenSize.isSmallScreen ? 16 : 20,
                  ),
                  label: TextUtils.responsiveText(
                    'اختيار التاريخ',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(12),
                      vertical: ScreenSize.getPadding(8),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(height: ScreenSize.getPadding(16)),
            // اختيار عدد الورديات
            Row(
              children: [
                TextUtils.responsiveText(
                  'عدد الورديات:',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                  ),
                  maxLines: 1,
                ),
                SizedBox(width: ScreenSize.getPadding(16)),
                Obx(
                  () => Wrap(
                    spacing: ScreenSize.getPadding(8),
                    children:
                        controller.availableShifts.map((shiftCount) {
                          return ChoiceChip(
                            label: TextUtils.responsiveText(
                              shiftCount.toString(),
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                color: controller.selectedShiftsCount.value == shiftCount
                                    ? Colors.white
                                    : Colors.black,
                              ),
                              maxLines: 1,
                            ),
                            selected: controller.selectedShiftsCount.value == shiftCount,
                            onSelected: (selected) {
                              if (selected) {
                                controller.selectedShiftsCount.value = shiftCount;
                                // إعادة حساب عدد القطع إذا كان هناك وزن مدخل
                                if (controller.rawMaterialUsedController.text.isNotEmpty) {
                                  controller.calculatePartsFromWeight();
                                }
                              }
                            },
                            padding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(8),
                              vertical: ScreenSize.getPadding(4),
                            ),
                          );
                        }).toList(),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductionForm(DailyProductionController controller) {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(ScreenSize.getPadding(16)),
        child: Form(
          key: controller.formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextUtils.responsiveText(
                'بيانات الإنتاج',
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // اختيار الماكينة
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'الماكينة',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                value:
                    controller.selectedMachineId.value.isEmpty
                        ? null
                        : controller.selectedMachineId.value,
                items:
                    controller.machines.map((Machine machine) {
                      return DropdownMenuItem<String>(
                        value: machine.id!,
                        child: TextUtils.responsiveText(
                          '${machine.name} - ${machine.model}',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            color: Colors.black,
                          ),
                          maxLines: 1,
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectMachine(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار الماكينة';
                  }
                  return null;
                },
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                isExpanded: true,
                icon: Icon(
                  Icons.arrow_drop_down,
                  size: ScreenSize.isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // اختيار الإسطمبة
              Obx(
                () => DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    labelText: 'الإسطمبة',
                    border: OutlineInputBorder(),
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(16),
                      vertical: ScreenSize.getPadding(12),
                    ),
                  ),
                  value:
                      controller.selectedMoldId.value.isEmpty
                          ? null
                          : controller.selectedMoldId.value,
                  items:
                      controller.availableMolds.map((Mold mold) {
                        return DropdownMenuItem<String>(
                          value: mold.id!,
                          child: TextUtils.responsiveText(
                            '${mold.name} - ${mold.productCode}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              color: Colors.black,
                            ),
                            maxLines: 1,
                          ),
                        );
                      }).toList(),
                  onChanged:
                      controller.availableMolds.isEmpty
                          ? null
                          : (value) {
                            if (value != null) {
                              controller.selectMold(value);
                            }
                          },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى اختيار الإسطمبة';
                    }
                    return null;
                  },
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  ),
                  isExpanded: true,
                  icon: Icon(
                    Icons.arrow_drop_down,
                    size: ScreenSize.isSmallScreen ? 20 : 24,
                  ),
                ),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // اختيار المادة الخام
              DropdownButtonFormField<String>(
                decoration: InputDecoration(
                  labelText: 'المادة الخام',
                  border: OutlineInputBorder(),
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                value:
                    controller.selectedRawMaterialId.value.isEmpty
                        ? null
                        : controller.selectedRawMaterialId.value,
                items:
                    controller.rawMaterials.map((RawMaterial material) {
                      return DropdownMenuItem<String>(
                        value: material.id!,
                        child: TextUtils.responsiveText(
                          '${material.name} - ${material.color} (${material.availableQuantity.toStringAsFixed(2)} كجم)',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            color: Colors.black,
                          ),
                          maxLines: 1,
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.selectRawMaterial(value);
                  }
                },
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'يرجى اختيار المادة الخام';
                  }
                  return null;
                },
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                isExpanded: true,
                icon: Icon(
                  Icons.arrow_drop_down,
                  size: ScreenSize.isSmallScreen ? 20 : 24,
                ),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),



              // كمية المواد الخام المستخدمة
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: controller.rawMaterialUsedController,
                      decoration: InputDecoration(
                        labelText: 'الوزن الإجمالي للوردية (كجم)',
                        border: OutlineInputBorder(),
                        suffixText: 'كجم',
                        hintText: 'أدخل الوزن الإجمالي للوردية',
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                      ),
                      keyboardType: const TextInputType.numberWithOptions(
                        decimal: true,
                      ),
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}'),
                        ),
                      ],
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'يرجى إدخال الوزن الإجمالي للوردية';
                        }

                        final double? amount = double.tryParse(value);
                        if (amount == null || amount <= 0) {
                          return 'يرجى إدخال قيمة صحيحة أكبر من الصفر';
                        }

                        // التحقق من توفر المواد الخام
                        final String materialId =
                            controller.selectedRawMaterialId.value;
                        if (materialId.isNotEmpty) {
                          final RawMaterial? material = controller
                              .findRawMaterialById(materialId);
                          if (material != null &&
                              amount > material.availableQuantity) {
                            return 'الكمية المتاحة غير كافية (${material.availableQuantity.toStringAsFixed(2)} كجم فقط)';
                          }
                        }

                        return null;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // عدد القطع المنتجة (محسوب تلقائيًا)
              TextFormField(
                controller: controller.partsProducedController,
                decoration: InputDecoration(
                  labelText: 'عدد القطع المنتجة (محسوب تلقائيًا)',
                  border: OutlineInputBorder(),
                  suffixText: 'قطعة',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                readOnly: true,
                enabled: false,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // وقت الدورة (محسوب تلقائيًا)
              TextFormField(
                controller: controller.cycleTimeController,
                decoration: InputDecoration(
                  labelText: 'وقت الدورة (محسوب تلقائيًا)',
                  border: OutlineInputBorder(),
                  suffixText: 'ثانية',
                  contentPadding: EdgeInsets.symmetric(
                    horizontal: ScreenSize.getPadding(16),
                    vertical: ScreenSize.getPadding(12),
                  ),
                ),
                readOnly: true,
                enabled: false,
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),

              // عرض الوزن المحسوب والفاقد
              Obx(
                () => Container(
                  padding: EdgeInsets.all(ScreenSize.getPadding(12)),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.blue.shade200),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.scale,
                            size: ScreenSize.isSmallScreen ? 14 : 16,
                            color: Colors.blue.shade700,
                          ),
                          SizedBox(width: ScreenSize.getPadding(8)),
                          TextUtils.responsiveText(
                            'الوزن النظري للقطع المنتجة:',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                          SizedBox(width: ScreenSize.getPadding(8)),
                          TextUtils.responsiveText(
                            '${controller.calculatedWeight.value.toStringAsFixed(2)} كجم',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.blue.shade700,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                      if (controller.calculatedScrap.value > 0) ...[
                        SizedBox(height: ScreenSize.getPadding(8)),
                        Row(
                          children: [
                            Icon(
                              Icons.recycling,
                              size: ScreenSize.isSmallScreen ? 14 : 16,
                              color: Colors.orange.shade700,
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            TextUtils.responsiveText(
                              'الفاقد التقديري (0.5%):',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              ),
                              maxLines: 1,
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            TextUtils.responsiveText(
                              '${controller.calculatedScrap.value.toStringAsFixed(2)} كجم',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.orange.shade700,
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              ),
                              maxLines: 1,
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              SizedBox(height: ScreenSize.getPadding(24)),

              // زر حفظ الإنتاج
              SizedBox(
                width: double.infinity,
                height: ScreenSize.isSmallScreen ? 40 : 48,
                child: ElevatedButton.icon(
                  onPressed: () => controller.saveProduction(),
                  icon: Icon(
                    Icons.save,
                    size: ScreenSize.isSmallScreen ? 18 : 24,
                  ),
                  label: TextUtils.responsiveText(
                    'حفظ الإنتاج',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                  ),
                  style: ElevatedButton.styleFrom(
                    padding: EdgeInsets.symmetric(
                      vertical: ScreenSize.getPadding(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProductionHistory(DailyProductionController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            TextUtils.responsiveText(
              'سجل الإنتاج اليومي',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
            ),
            Obx(() {
              if (controller.dailyProduction.isNotEmpty) {
                return ElevatedButton.icon(
                  onPressed: () => controller.generateProductionReport(),
                  icon: Icon(
                    Icons.picture_as_pdf,
                    size: ScreenSize.isSmallScreen ? 16 : 20,
                  ),
                  label: TextUtils.responsiveText(
                    'إنشاء تقرير',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: ScreenSize.getPadding(12),
                      vertical: ScreenSize.getPadding(8),
                    ),
                  ),
                );
              }
              return const SizedBox.shrink();
            }),
          ],
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        Obx(() {
          if (controller.isLoadingHistory.value) {
            return Center(
              child: Padding(
                padding: EdgeInsets.all(ScreenSize.getPadding(16)),
                child: CircularProgressIndicator(
                  strokeWidth: ScreenSize.isSmallScreen ? 3 : 4,
                ),
              ),
            );
          }

          if (controller.dailyProduction.isEmpty) {
            return Center(
              child: Padding(
                padding: EdgeInsets.all(ScreenSize.getPadding(16)),
                child: Column(
                  children: [
                    Icon(
                      Icons.engineering_outlined,
                      size: ScreenSize.isSmallScreen ? 36 : 48,
                      color: Colors.grey.shade400,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    TextUtils.responsiveText(
                      'لا يوجد إنتاج مسجل لهذا اليوم',
                      style: TextStyle(
                        color: Colors.grey.shade600,
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      textAlign: TextAlign.center,
                      maxLines: 2,
                    ),
                  ],
                ),
              ),
            );
          }

          return ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller.dailyProduction.length,
            itemBuilder: (context, index) {
              final production = controller.dailyProduction[index];
              return Card(
                margin: EdgeInsets.only(bottom: ScreenSize.getPadding(8)),
                child: Padding(
                  padding: EdgeInsets.all(ScreenSize.getPadding(16)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextUtils.responsiveText(
                            production['machineName'] ?? 'غير معروف',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                          ),
                          TextUtils.responsiveText(
                            controller.formatTimeOnly(
                              DateTime.parse(production['date']),
                            ),
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                        ],
                      ),
                      SizedBox(height: ScreenSize.getPadding(8)),
                      TextUtils.responsiveText(
                        'الإسطمبة: ${production['moldName'] ?? 'غير معروف'}',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(4)),
                      TextUtils.responsiveText(
                        'المادة الخام: ${production['rawMaterialName'] ?? 'غير معروف'} - ${production['rawMaterialColor'] ?? ''}',
                        style: TextStyle(
                          color: Colors.grey.shade700,
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                        maxLines: 1,
                      ),
                      SizedBox(height: ScreenSize.getPadding(8)),
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        child: Row(
                          children: [
                            _buildInfoChip(
                              'القطع: ${production['partsProduced']}',
                              Icons.inventory_2_outlined,
                              Colors.blue,
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            _buildInfoChip(
                              'الخامات: ${(production['rawMaterialUsed'] as double).toStringAsFixed(2)} كجم',
                              Icons.category_outlined,
                              Colors.green,
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            _buildInfoChip(
                              'الدورة: ${production['cycleTime']} ثانية',
                              Icons.timer_outlined,
                              Colors.orange,
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: ScreenSize.getPadding(12)),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          // زر التعديل
                          IconButton(
                            onPressed: () => _showEditProductionDialog(context, controller, production),
                            icon: Icon(
                              Icons.edit,
                              size: ScreenSize.isSmallScreen ? 18 : 22,
                              color: Colors.blue,
                            ),
                            tooltip: 'تعديل',
                            padding: EdgeInsets.all(ScreenSize.getPadding(4)),
                          ),
                          // زر الحذف
                          IconButton(
                            onPressed: () => _showDeleteConfirmationDialog(context, controller, production),
                            icon: Icon(
                              Icons.delete,
                              size: ScreenSize.isSmallScreen ? 18 : 22,
                              color: Colors.red,
                            ),
                            tooltip: 'حذف',
                            padding: EdgeInsets.all(ScreenSize.getPadding(4)),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        }),
      ],
    );
  }

  Widget _buildInfoChip(String label, IconData icon, Color color) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(8),
        vertical: ScreenSize.getPadding(4),
      ),
      decoration: BoxDecoration(
        color: Color.fromRGBO(
          color.r.toInt(),
          color.g.toInt(),
          color.b.toInt(),
          0.1,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Color.fromRGBO(
            color.r.toInt(),
            color.g.toInt(),
            color.b.toInt(),
            0.5,
          ),
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: ScreenSize.isSmallScreen ? 14 : 16, color: color),
          SizedBox(width: ScreenSize.getPadding(4)),
          TextUtils.responsiveText(
            label,
            style: TextStyle(
              color: color,
              fontWeight: FontWeight.w500,
              fontSize: ScreenSize.isSmallScreen ? 10 : 12,
            ),
            maxLines: 1,
          ),
        ],
      ),
    );
  }

  // عرض مربع حوار تعديل الإنتاج
  void _showEditProductionDialog(BuildContext context, DailyProductionController controller, Map<String, dynamic> production) {
    // تهيئة وحدات التحكم بالنص مع القيم الحالية
    final TextEditingController partsController = TextEditingController(
      text: production['partsProduced'].toString(),
    );
    final TextEditingController rawMaterialController = TextEditingController(
      text: production['rawMaterialUsed'].toString(),
    );
    final TextEditingController cycleTimeController = TextEditingController(
      text: production['cycleTime'].toString(),
    );

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تعديل الإنتاج'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextUtils.responsiveText(
                'الماكينة: ${production['machineName']}',
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(8)),
              TextUtils.responsiveText(
                'الإسطمبة: ${production['moldName']}',
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(8)),
              TextUtils.responsiveText(
                'المادة الخام: ${production['rawMaterialName']} - ${production['rawMaterialColor'] ?? ''}',
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextFormField(
                controller: rawMaterialController,
                decoration: const InputDecoration(
                  labelText: 'كمية المواد الخام (كجم)',
                  border: OutlineInputBorder(),
                  suffixText: 'كجم',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextFormField(
                controller: partsController,
                decoration: const InputDecoration(
                  labelText: 'عدد القطع المنتجة',
                  border: OutlineInputBorder(),
                ),
                keyboardType: TextInputType.number,
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              TextFormField(
                controller: cycleTimeController,
                decoration: const InputDecoration(
                  labelText: 'وقت الدورة (ثانية)',
                  border: OutlineInputBorder(),
                  suffixText: 'ثانية',
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              // التحقق من صحة المدخلات
              final int? partsProduced = int.tryParse(partsController.text);
              final double? rawMaterialUsed = double.tryParse(rawMaterialController.text);
              final double? cycleTime = double.tryParse(cycleTimeController.text);

              if (partsProduced == null || rawMaterialUsed == null || cycleTime == null) {
                Get.snackbar(
                  'خطأ',
                  'يرجى إدخال قيم صحيحة',
                  backgroundColor: Colors.red.shade100,
                  colorText: Colors.red.shade800,
                  snackPosition: SnackPosition.BOTTOM,
                );
                return;
              }

              // إنشاء كائن الإنتاج المحدث
              final updatedProduction = Production(
                id: production['id'],
                date: DateTime.parse(production['date']),
                machineId: production['machineId'],
                moldId: production['moldId'],
                rawMaterialId: production['rawMaterialId'],
                rawMaterialUsed: rawMaterialUsed,
                partsProduced: partsProduced,
                cycleTime: cycleTime,
                electricityCost: production['electricityCost'],
                operatorCost: production['operatorCost'],
                shiftsCount: production['shiftsCount'],
              );

              // تحديث المخزون والمواد الخام
              final double oldRawMaterialUsed = production['rawMaterialUsed'];
              final int oldPartsProduced = production['partsProduced'];

              // حساب الفرق في المواد الخام والقطع المنتجة
              final double rawMaterialDiff = rawMaterialUsed - oldRawMaterialUsed;
              final int partsDiff = partsProduced - oldPartsProduced;

              // تحديث الإنتاج في قاعدة البيانات
              await controller.updateProduction(
                updatedProduction,
                rawMaterialDiff,
                partsDiff,
              );

              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: const Text('حفظ التغييرات'),
          ),
        ],
      ),
    );
  }

  // عرض مربع حوار تأكيد الحذف
  void _showDeleteConfirmationDialog(BuildContext context, DailyProductionController controller, Map<String, dynamic> production) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text(
          'هل أنت متأكد من حذف سجل الإنتاج هذا؟ سيتم تحديث المخزون والمواد الخام وفقاً لذلك.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () async {
              await controller.deleteProduction(production['id']);
              if (context.mounted) {
                Navigator.pop(context);
              }
            },
            child: const Text('حذف', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }
}
