import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Worker;
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/assembly.dart';
import 'package:mostafa_final/data/models/worker.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:mostafa_final/data/repositories/assembly_repository.dart';
import 'package:mostafa_final/data/repositories/worker_repository.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';

class AssemblyController extends GetxController {
  // المستودعات
  final AssemblyRepository _assemblyRepository = Get.find<AssemblyRepository>();
  final WorkerRepository _workerRepository = Get.find<WorkerRepository>();
  final ProductRepository _productRepository = Get.find<ProductRepository>();
  final InventoryRepository _inventoryRepository = Get.find<InventoryRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  // قوائم البيانات
  final RxList<Assembly> assemblies = <Assembly>[].obs;
  final RxList<Worker> workers = <Worker>[].obs;
  final RxList<Product> products = <Product>[].obs;

  // البيانات المختارة
  final Rx<DateTime> selectedDate = DateTime.now().obs;
  final Rx<Worker?> selectedWorker = Rx<Worker?>(null);
  final Rx<Product?> selectedProduct = Rx<Product?>(null);
  final RxInt quantity = 1.obs;

  // المكونات المستخدمة
  final RxList<AssemblyComponentUsage> componentsUsed =
      <AssemblyComponentUsage>[].obs;

  // تكاليف التجميع
  final RxDouble componentsCost = 0.0.obs;
  final RxDouble laborCost = 0.0.obs;
  final RxDouble packagingCost = 0.0.obs;
  final RxDouble totalCost = 0.0.obs;

  // معلومات إحصائية عن التجميع
  final RxInt totalAssembledUnits = 0.obs;
  final RxInt totalAssemblyOperations = 0.obs;
  final RxDouble totalAssemblyCost = 0.0.obs;
  final RxDouble averageUnitCost = 0.0.obs;

  // إحصائيات إضافية للتقارير
  final RxList<Map<String, dynamic>> weeklyAssemblyData =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> workerPerformance =
      <Map<String, dynamic>>[].obs;
  final RxList<Map<String, dynamic>> productEfficiency =
      <Map<String, dynamic>>[].obs;
  final Rx<DateTime> reportStartDate =
      DateTime.now().subtract(const Duration(days: 30)).obs;
  final Rx<DateTime> reportEndDate = DateTime.now().obs;

  @override
  void onInit() {
    super.onInit();
    fetchInitialData();
  }

  // جلب البيانات الأولية
  Future<void> fetchInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([
        fetchWorkers(),
        fetchProducts(),
        fetchAssemblies(),
        fetchAssemblyStatistics(),
      ]);
    } finally {
      isLoading.value = false;
    }
  }

  // جلب قائمة العمال
  Future<void> fetchWorkers() async {
    try {
      final assemblyWorkers = await _workerRepository.getWorkersByDepartment(
        WorkerDepartment.assembly,
      );
      workers.assignAll(assemblyWorkers);

      // تعيين العامل الافتراضي إذا كان هناك عمال في القائمة
      if (workers.isNotEmpty && selectedWorker.value == null) {
        selectedWorker.value = workers.first;
      }
    } catch (e) {
      debugPrint('Error fetching workers: $e');
    }
  }

  // جلب قائمة المنتجات
  Future<void> fetchProducts() async {
    try {
      final allProducts = await _productRepository.getAllProducts();
      products.assignAll(allProducts);

      if (products.isNotEmpty && selectedProduct.value == null) {
        selectProduct(products.first);
      }
    } catch (e) {
      debugPrint('Error fetching products: $e');
    }
  }

  // جلب قائمة عمليات التجميع
  Future<void> fetchAssemblies() async {
    try {
      final allAssemblies = await _assemblyRepository.getAllAssemblies();
      assemblies.assignAll(allAssemblies);
    } catch (e) {
      debugPrint('Error fetching assemblies: $e');
    }
  }

  // جلب قائمة عمليات التجميع بتاريخ معين
  Future<void> fetchAssembliesByDate(DateTime date) async {
    isLoading.value = true;
    try {
      final dateAssemblies = await _assemblyRepository.getAssembliesByDate(
        date,
      );
      assemblies.assignAll(dateAssemblies);
    } catch (e) {
      debugPrint('Error fetching assemblies by date: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // جلب قائمة عمليات التجميع حسب نطاق تاريخي
  Future<void> fetchAssembliesByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    isLoading.value = true;
    try {
      final rangeAssemblies = await _assemblyRepository
          .getAssembliesByDateRange(startDate, endDate);
      assemblies.assignAll(rangeAssemblies);

      // تحديث بيانات التقارير
      reportStartDate.value = startDate;
      reportEndDate.value = endDate;

      // بعد جلب البيانات، نحسب الإحصائيات
      await calculateWeeklyAssemblyData();
      await calculateWorkerPerformance();
      await calculateProductEfficiency();
    } catch (e) {
      debugPrint('Error fetching assemblies by date range: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // جلب إحصاءات التجميع
  Future<void> fetchAssemblyStatistics() async {
    try {
      final stats = await _assemblyRepository.getAssemblyStatistics();
      totalAssembledUnits.value = stats['totalQuantity'] ?? 0;
      totalAssemblyOperations.value = stats['totalAssemblies'] ?? 0;
      totalAssemblyCost.value = stats['totalCost'] ?? 0;
      averageUnitCost.value = stats['averageCost'] ?? 0;
    } catch (e) {
      debugPrint('Error fetching assembly statistics: $e');
    }
  }

  // اختيار منتج للتجميع
  Future<void> selectProduct(Product product) async {
    selectedProduct.value = product;

    // إعادة تعيين المكونات والكميات
    componentsUsed.clear();
    quantity.value = 1;

    // تحميل مكونات المنتج من قاعدة البيانات
    await loadProductComponents(product.id!);

    // حساب التكاليف المتوقعة
    await calculateCosts();
  }

  // تحميل مكونات المنتج
  Future<void> loadProductComponents(String productId) async {
    try {
      // جلب مكونات المنتج من قاعدة البيانات
      final productDetails = await _productRepository.getProductById(productId);

      if (productDetails != null) {
        // تحويل مكونات المنتج إلى مكونات تجميع
        final components =
            productDetails.components.map((component) {
              // التحقق من توفر المكون في المخزون
              return AssemblyComponentUsage(
                componentId: component.id!,
                name: component.name,
                partId: component.partId,
                componentType: component.type.index,
                quantity: component.quantity,
                cost: component.cost,
              );
            }).toList();

        componentsUsed.assignAll(components);
      }
    } catch (e) {
      debugPrint('Error loading product components: $e');
    }
  }

  // التحقق من توفر المكونات في المخزون
  Future<bool> checkComponentsAvailability() async {
    try {
      for (final component in componentsUsed) {
        // جلب معلومات المكون من المخزون
        final String itemType =
            component.componentType == 0 ? 'plastic_part' : 'accessory';
        final inventoryItem = await _inventoryRepository
            .getInventoryItemByItemIdAndType(component.partId, itemType);

        if (inventoryItem == null) {
          Get.snackbar(
            'خطأ',
            'المكون ${component.name} غير موجود في المخزون',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        }

        // التحقق من كمية المكون في المخزون
        if (inventoryItem.currentQuantity <
            component.quantity * quantity.value) {
          Get.snackbar(
            'خطأ',
            'كمية ${component.name} غير كافية في المخزون. المتوفر: ${inventoryItem.currentQuantity}, المطلوب: ${component.quantity * quantity.value}',
            backgroundColor: Colors.red,
            colorText: Colors.white,
            snackPosition: SnackPosition.BOTTOM,
          );
          return false;
        }
      }

      return true;
    } catch (e) {
      debugPrint('Error checking components availability: $e');
      return false;
    }
  }

  // حساب تكاليف التجميع
  Future<void> calculateCosts() async {
    if (selectedProduct.value == null || selectedWorker.value == null) {
      return;
    }

    try {
      // حساب تكلفة العمالة للدقيقة الواحدة
      final double laborCostPerMinute = selectedWorker.value!.hourlyRate / 60.0;

      // حساب التكاليف
      final costs = await _assemblyRepository.calculateAssemblyCost(
        selectedProduct.value!.id!,
        quantity.value,
        laborCostPerMinute,
        componentsUsed,
      );

      // تحديث قيم التكاليف
      componentsCost.value = costs['componentsCost'] ?? 0;
      laborCost.value = costs['laborCost'] ?? 0;
      packagingCost.value = costs['packagingCost'] ?? 0;
      totalCost.value = costs['totalCost'] ?? 0;
    } catch (e) {
      debugPrint('Error calculating costs: $e');
    }
  }

  // تغيير الكمية
  void changeQuantity(int value) {
    if (value < 1) return;
    quantity.value = value;
    calculateCosts();
  }

  // حفظ عملية تجميع جديدة
  Future<bool> saveAssembly() async {
    if (selectedProduct.value == null || selectedWorker.value == null) {
      Get.snackbar(
        'خطأ',
        'يرجى اختيار المنتج والعامل',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }

    // التحقق من توفر المكونات
    final componentsAvailable = await checkComponentsAvailability();
    if (!componentsAvailable) {
      return false;
    }

    isSaving.value = true;
    try {
      // حساب تكلفة العمالة للدقيقة الواحدة
      final double laborCostPerMinute = selectedWorker.value!.hourlyRate / 60.0;

      // إنشاء كائن التجميع
      final assembly = Assembly(
        productId: selectedProduct.value!.id!,
        date: selectedDate.value,
        workerId: selectedWorker.value!.id!,
        quantity: quantity.value,
        laborCostPerMinute: laborCostPerMinute,
        totalAssemblyCost: laborCost.value,
        totalPackagingCost: packagingCost.value,
        status: AssemblyStatus.completed,
        componentsUsed: componentsUsed,
      );

      // حفظ عملية التجميع
      final assemblyId = await _assemblyRepository.createAssembly(assembly);

      if (assemblyId.isNotEmpty) {
        Get.snackbar(
          'نجاح',
          'تم حفظ عملية التجميع بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // إعادة تحميل البيانات
        await fetchAssemblies();
        await fetchAssemblyStatistics();

        // إعادة تعيين النموذج
        resetForm();

        return true;
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في حفظ عملية التجميع',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error saving assembly: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حفظ عملية التجميع: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isSaving.value = false;
    }
  }

  // تحديث حالة عملية تجميع
  Future<bool> updateAssemblyStatus(String assemblyId, String status) async {
    try {
      final result = await _assemblyRepository.updateAssemblyStatus(
        assemblyId,
        status,
      );

      if (result > 0) {
        // إعادة تحميل عمليات التجميع
        await fetchAssemblies();

        Get.snackbar(
          'نجاح',
          'تم تحديث حالة التجميع بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة التجميع',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error updating assembly status: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث حالة التجميع: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // حذف عملية تجميع
  Future<bool> deleteAssembly(String assemblyId) async {
    try {
      final result = await _assemblyRepository.deleteAssembly(assemblyId);

      if (result > 0) {
        // إعادة تحميل البيانات
        await fetchAssemblies();
        await fetchAssemblyStatistics();

        Get.snackbar(
          'نجاح',
          'تم حذف عملية التجميع بنجاح',
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        return true;
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في حذف عملية التجميع',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
        return false;
      }
    } catch (e) {
      debugPrint('Error deleting assembly: $e');
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء حذف عملية التجميع: $e',
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    }
  }

  // إعادة تعيين نموذج التجميع
  void resetForm() {
    quantity.value = 1;
    selectedDate.value = DateTime.now();

    // لا نعيد تعيين العامل والمنتج، فقط نعيد حساب التكاليف
    if (selectedProduct.value != null) {
      calculateCosts();
    }
  }

  // تنسيق التاريخ للعرض
  String formatDate(DateTime date) {
    return DateFormat('yyyy-MM-dd').format(date);
  }

  // حساب بيانات التجميع الأسبوعية للرسم البياني
  Future<void> calculateWeeklyAssemblyData() async {
    try {
      // هنا سنقوم بتجميع البيانات أسبوعيًا من عمليات التجميع
      final Map<String, int> weeklyData = {};

      // تمثيل كمثال، لاحقًا سيتم استبداله بحساب فعلي من قاعدة البيانات
      final now = DateTime.now();
      for (int i = 6; i >= 0; i--) {
        final date = now.subtract(Duration(days: i));
        final day = DateFormat('EEE').format(date);
        weeklyData[day] = (i * 10 + 20) % 100; // قيمة عشوائية للتمثيل
      }

      // تحويل البيانات إلى صيغة مناسبة للرسم البياني
      final List<Map<String, dynamic>> chartData = [];
      weeklyData.forEach((day, quantity) {
        chartData.add({'day': day, 'quantity': quantity});
      });

      weeklyAssemblyData.assignAll(chartData);
    } catch (e) {
      debugPrint('Error calculating weekly assembly data: $e');
    }
  }

  // حساب أداء العمال
  Future<void> calculateWorkerPerformance() async {
    try {
      final List<Map<String, dynamic>> performance = [];

      // لاحقًا سيتم الحساب الفعلي بناءً على بيانات قاعدة البيانات
      for (final worker in workers) {
        // هنا نقوم بحساب الأداء بناءً على عدد القطع المجمعة وجودة الإنتاج ووقت الإنجاز
        final int assembledQuantity = (worker.id!.hashCode % 100) + 50;
        final double qualityRate = 0.85 + (worker.id!.hashCode % 15) / 100;
        final int completionTime = 120 - (worker.id!.hashCode % 30);

        // حساب نسبة الأداء الإجمالية
        final double performanceRate =
            (assembledQuantity / 100) * 0.4 +
            qualityRate * 0.4 +
            (1 - (completionTime / 120)) * 0.2;

        performance.add({
          'workerId': worker.id,
          'workerName': worker.name,
          'workerCode': worker.code,
          'assembledQuantity': assembledQuantity,
          'qualityRate': qualityRate,
          'completionTime': completionTime,
          'performanceRate': performanceRate,
        });
      }

      // ترتيب العمال حسب الأداء (تنازليًا)
      performance.sort(
        (a, b) => (b['performanceRate'] as double).compareTo(
          a['performanceRate'] as double,
        ),
      );

      workerPerformance.assignAll(performance);
    } catch (e) {
      debugPrint('Error calculating worker performance: $e');
    }
  }

  // حساب كفاءة المنتجات
  Future<void> calculateProductEfficiency() async {
    try {
      final List<Map<String, dynamic>> efficiency = [];

      // لاحقًا سيتم الحساب الفعلي بناءً على بيانات قاعدة البيانات
      for (final product in products) {
        // هنا نقوم بحساب كفاءة المنتج بناءً على التكلفة ووقت التجميع ومعدل العيوب
        final double assemblyTime = 10 + (product.id!.hashCode % 20);
        final double defectRate = 0.01 + (product.id!.hashCode % 5) / 100;
        final double costEfficiency = 0.7 + (product.id!.hashCode % 30) / 100;

        // حساب نسبة الكفاءة الإجمالية
        final double efficiencyRate =
            (1 - (assemblyTime / 30)) * 0.3 +
            (1 - defectRate) * 0.4 +
            costEfficiency * 0.3;

        efficiency.add({
          'productId': product.id,
          'productName': product.name,
          'productCode': product.code,
          'assemblyTime': assemblyTime,
          'defectRate': defectRate,
          'costEfficiency': costEfficiency,
          'efficiencyRate': efficiencyRate,
        });
      }

      // ترتيب المنتجات حسب الكفاءة (تنازليًا)
      efficiency.sort(
        (a, b) => (b['efficiencyRate'] as double).compareTo(
          a['efficiencyRate'] as double,
        ),
      );

      productEfficiency.assignAll(efficiency);
    } catch (e) {
      debugPrint('Error calculating product efficiency: $e');
    }
  }
}
