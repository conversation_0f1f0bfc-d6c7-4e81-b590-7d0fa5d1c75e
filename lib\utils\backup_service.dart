import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:archive/archive.dart';
import 'package:archive/archive_io.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// خدمة النسخ الاحتياطي لقاعدة البيانات
class BackupService {
  final Logger _logger = Logger();
  static const String _lastBackupKey = 'last_backup_time';
  static const String _backupScheduleKey = 'backup_schedule';
  static const String _backupFolderKey = 'backup_folder_path';

  /// الحصول على مسار ملف قاعدة البيانات
  Future<String> _getDatabasePath() async {
    final documentsDirectory = await getApplicationDocumentsDirectory();
    return join(documentsDirectory.path, "factory_management.db");
  }

  /// إنشاء نسخة احتياطية من قاعدة البيانات
  Future<String?> createBackup({String? customName}) async {
    try {
      // التحقق من صلاحيات الوصول للتخزين
      if (!await _checkStoragePermission()) {
        _logger.e('لم يتم منح صلاحيات الوصول للتخزين');
        return null;
      }

      // الحصول على مسار ملف قاعدة البيانات
      final dbPath = await _getDatabasePath();
      final dbFile = File(dbPath);

      // التحقق من وجود ملف قاعدة البيانات
      if (!await dbFile.exists()) {
        _logger.e('ملف قاعدة البيانات غير موجود: $dbPath');
        return null;
      }

      // إنشاء اسم للنسخة الاحتياطية
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final backupName = customName ?? 'backup_$timestamp';

      // إنشاء مسار مجلد النسخ الاحتياطي
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        _logger.e('فشل في إنشاء مجلد النسخ الاحتياطي');
        return null;
      }

      // إنشاء مسار ملف النسخة الاحتياطية
      final backupPath = join(backupDir.path, '$backupName.zip');

      // قراءة محتوى ملف قاعدة البيانات
      final dbBytes = await dbFile.readAsBytes();

      // إنشاء أرشيف للضغط
      final archive = Archive();

      // إضافة ملف قاعدة البيانات إلى الأرشيف
      final archiveFile = ArchiveFile(
        basename(dbPath),
        dbBytes.length,
        dbBytes,
      );
      archive.addFile(archiveFile);

      // ضغط الأرشيف وحفظه
      final zipEncoder = ZipEncoder();
      final zipData = zipEncoder.encode(archive);
      if (zipData == null) {
        _logger.e('فشل في ضغط ملف قاعدة البيانات');
        return null;
      }

      // حفظ ملف النسخة الاحتياطية
      final backupFile = File(backupPath);
      await backupFile.writeAsBytes(zipData);

      // تحديث وقت آخر نسخة احتياطية
      await _updateLastBackupTime();

      _logger.i('تم إنشاء نسخة احتياطية بنجاح: $backupPath');
      return backupPath;
    } catch (e) {
      _logger.e('حدث خطأ أثناء إنشاء النسخة الاحتياطية: $e');
      return null;
    }
  }

  /// استعادة قاعدة البيانات من نسخة احتياطية
  Future<bool> restoreBackup(String backupPath) async {
    try {
      // التحقق من وجود ملف النسخة الاحتياطية
      final backupFile = File(backupPath);
      if (!await backupFile.exists()) {
        _logger.e('ملف النسخة الاحتياطية غير موجود: $backupPath');
        return false;
      }

      // قراءة محتوى ملف النسخة الاحتياطية
      final zipBytes = await backupFile.readAsBytes();

      // فك ضغط الأرشيف
      final archive = ZipDecoder().decodeBytes(zipBytes);

      // الحصول على مسار ملف قاعدة البيانات
      final dbPath = await _getDatabasePath();

      // استخراج ملف قاعدة البيانات من الأرشيف
      for (final file in archive) {
        if (file.isFile) {
          final data = file.content as List<int>;
          final dbFile = File(dbPath);

          // حفظ ملف قاعدة البيانات
          await dbFile.writeAsBytes(data);
          _logger.i('تم استعادة قاعدة البيانات بنجاح');
          return true;
        }
      }

      _logger.e('لم يتم العثور على ملف قاعدة البيانات في النسخة الاحتياطية');
      return false;
    } catch (e) {
      _logger.e('حدث خطأ أثناء استعادة النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// الحصول على قائمة النسخ الاحتياطية المتاحة
  Future<List<Map<String, dynamic>>> getAvailableBackups() async {
    try {
      final backupDir = await _getBackupDirectory();
      if (backupDir == null) {
        return [];
      }

      final List<FileSystemEntity> files = await backupDir.list().toList();
      final List<Map<String, dynamic>> backups = [];

      for (final file in files) {
        if (file is File && file.path.endsWith('.zip')) {
          final stat = await file.stat();
          backups.add({
            'path': file.path,
            'name': basename(file.path),
            'size': stat.size,
            'modified': stat.modified,
          });
        }
      }

      // ترتيب النسخ الاحتياطية حسب تاريخ التعديل (الأحدث أولاً)
      backups.sort(
        (a, b) =>
            (b['modified'] as DateTime).compareTo(a['modified'] as DateTime),
      );

      return backups;
    } catch (e) {
      _logger.e('حدث خطأ أثناء جلب قائمة النسخ الاحتياطية: $e');
      return [];
    }
  }

  /// حذف نسخة احتياطية
  Future<bool> deleteBackup(String backupPath) async {
    try {
      final backupFile = File(backupPath);
      if (await backupFile.exists()) {
        await backupFile.delete();
        _logger.i('تم حذف النسخة الاحتياطية بنجاح: $backupPath');
        return true;
      }
      return false;
    } catch (e) {
      _logger.e('حدث خطأ أثناء حذف النسخة الاحتياطية: $e');
      return false;
    }
  }

  /// تعيين جدول النسخ الاحتياطي التلقائي
  Future<bool> setBackupSchedule(String schedule) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backupScheduleKey, schedule);
      return true;
    } catch (e) {
      _logger.e('حدث خطأ أثناء تعيين جدول النسخ الاحتياطي: $e');
      return false;
    }
  }

  /// الحصول على جدول النسخ الاحتياطي التلقائي
  Future<String?> getBackupSchedule() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_backupScheduleKey);
    } catch (e) {
      _logger.e('حدث خطأ أثناء جلب جدول النسخ الاحتياطي: $e');
      return null;
    }
  }

  /// تعيين مسار مجلد النسخ الاحتياطي
  Future<bool> setBackupDirectory(String path) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_backupFolderKey, path);
      return true;
    } catch (e) {
      _logger.e('حدث خطأ أثناء تعيين مسار مجلد النسخ الاحتياطي: $e');
      return false;
    }
  }

  /// الحصول على مجلد النسخ الاحتياطي
  Future<Directory?> _getBackupDirectory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final customPath = prefs.getString(_backupFolderKey);

      if (customPath != null && customPath.isNotEmpty) {
        final dir = Directory(customPath);
        if (!await dir.exists()) {
          await dir.create(recursive: true);
        }
        return dir;
      }

      // استخدام المجلد الافتراضي إذا لم يتم تعيين مسار مخصص
      final externalDir = await getExternalStorageDirectory();
      if (externalDir != null) {
        final backupDir = Directory(join(externalDir.path, 'backups'));
        if (!await backupDir.exists()) {
          await backupDir.create(recursive: true);
        }
        return backupDir;
      }

      // استخدام مجلد المستندات إذا لم يتم العثور على مجلد التخزين الخارجي
      final documentsDir = await getApplicationDocumentsDirectory();
      final backupDir = Directory(join(documentsDir.path, 'backups'));
      if (!await backupDir.exists()) {
        await backupDir.create(recursive: true);
      }
      return backupDir;
    } catch (e) {
      _logger.e('حدث خطأ أثناء الحصول على مجلد النسخ الاحتياطي: $e');
      return null;
    }
  }

  /// التحقق من صلاحيات الوصول للتخزين
  Future<bool> _checkStoragePermission() async {
    if (Platform.isAndroid) {
      final status = await Permission.storage.status;
      if (status.isDenied) {
        final result = await Permission.storage.request();
        return result.isGranted;
      }
      return status.isGranted;
    }
    return true; // على iOS، لا نحتاج إلى صلاحيات خاصة للوصول إلى مجلد المستندات
  }

  /// تحديث وقت آخر نسخة احتياطية
  Future<void> _updateLastBackupTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setInt(_lastBackupKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      _logger.e('حدث خطأ أثناء تحديث وقت آخر نسخة احتياطية: $e');
    }
  }

  /// الحصول على وقت آخر نسخة احتياطية
  Future<DateTime?> getLastBackupTime() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final timestamp = prefs.getInt(_lastBackupKey);
      if (timestamp != null) {
        return DateTime.fromMillisecondsSinceEpoch(timestamp);
      }
      return null;
    } catch (e) {
      _logger.e('حدث خطأ أثناء جلب وقت آخر نسخة احتياطية: $e');
      return null;
    }
  }
}
