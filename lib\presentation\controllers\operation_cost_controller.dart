import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/operation_cost.dart';
import 'package:mostafa_final/data/repositories/operation_cost_repository.dart';

class OperationCostController extends GetxController {
  final OperationCostRepository _repository = Get.find<OperationCostRepository>();
  
  // حالة التحميل
  final RxBool isLoading = false.obs;
  
  // قائمة تكاليف التشغيل
  final RxList<OperationCost> operationCosts = <OperationCost>[].obs;
  
  // تكاليف التشغيل حسب الفئة
  final RxList<OperationCost> electricityCosts = <OperationCost>[].obs;
  final RxList<OperationCost> laborCosts = <OperationCost>[].obs;
  final RxList<OperationCost> maintenanceCosts = <OperationCost>[].obs;
  final RxList<OperationCost> overheadCosts = <OperationCost>[].obs;
  
  // الفئة المحددة حالياً
  final RxString selectedCategory = CostCategory.electricity.obs;
  
  @override
  void onInit() {
    super.onInit();
    loadOperationCosts();
  }
  
  // تحميل تكاليف التشغيل
  Future<void> loadOperationCosts() async {
    isLoading.value = true;
    try {
      // إنشاء جدول تكاليف التشغيل إذا لم يكن موجوداً
      await _repository.createTable();
      
      // تحميل جميع تكاليف التشغيل
      final costs = await _repository.getAllOperationCosts();
      operationCosts.assignAll(costs);
      
      // تصنيف التكاليف حسب الفئة
      _categorizeOperationCosts();
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل تكاليف التشغيل: $e');
    } finally {
      isLoading.value = false;
    }
  }
  
  // تصنيف تكاليف التشغيل حسب الفئة
  void _categorizeOperationCosts() {
    electricityCosts.assignAll(
      operationCosts.where((cost) => cost.category == CostCategory.electricity),
    );
    
    laborCosts.assignAll(
      operationCosts.where((cost) => cost.category == CostCategory.labor),
    );
    
    maintenanceCosts.assignAll(
      operationCosts.where((cost) => cost.category == CostCategory.maintenance),
    );
    
    overheadCosts.assignAll(
      operationCosts.where((cost) => cost.category == CostCategory.overhead),
    );
  }
  
  // تغيير الفئة المحددة
  void changeCategory(String category) {
    selectedCategory.value = category;
  }
  
  // إضافة تكلفة تشغيل جديدة
  Future<void> addOperationCost(
    String name,
    String category,
    double value,
    String unit,
    bool isDefault,
  ) async {
    try {
      final cost = OperationCost(
        name: name,
        category: category,
        value: value,
        unit: unit,
        isDefault: isDefault,
        lastUpdate: DateTime.now(),
      );
      
      await _repository.createOperationCost(cost);
      await loadOperationCosts();
      
      Get.back();
      _showSuccess('تمت إضافة تكلفة التشغيل بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إضافة تكلفة التشغيل: $e');
    }
  }
  
  // تحديث تكلفة تشغيل
  Future<void> updateOperationCost(
    OperationCost cost,
    double newValue,
  ) async {
    try {
      final updatedCost = cost.copyWith(
        value: newValue,
        lastUpdate: DateTime.now(),
      );
      
      await _repository.updateOperationCost(updatedCost);
      await loadOperationCosts();
      
      _showSuccess('تم تحديث تكلفة التشغيل بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث تكلفة التشغيل: $e');
    }
  }
  
  // حذف تكلفة تشغيل
  Future<void> deleteOperationCost(String id) async {
    try {
      await _repository.deleteOperationCost(id);
      await loadOperationCosts();
      
      _showSuccess('تم حذف تكلفة التشغيل بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء حذف تكلفة التشغيل: $e');
    }
  }
  
  // عرض نافذة إضافة تكلفة تشغيل جديدة
  void showAddOperationCostDialog() {
    final nameController = TextEditingController();
    final valueController = TextEditingController();
    final categoryController = TextEditingController(text: selectedCategory.value);
    final unitController = TextEditingController(text: _getDefaultUnit(selectedCategory.value));
    final isDefaultController = false.obs;
    
    Get.dialog(
      AlertDialog(
        title: const Text('إضافة تكلفة تشغيل جديدة'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'الاسم',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: valueController,
                decoration: const InputDecoration(
                  labelText: 'القيمة',
                  border: OutlineInputBorder(),
                ),
                keyboardType: const TextInputType.numberWithOptions(decimal: true),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الفئة',
                  border: OutlineInputBorder(),
                ),
                value: categoryController.text,
                items: [
                  DropdownMenuItem(
                    value: CostCategory.electricity,
                    child: Text(CostCategory.electricity),
                  ),
                  DropdownMenuItem(
                    value: CostCategory.labor,
                    child: Text(CostCategory.labor),
                  ),
                  DropdownMenuItem(
                    value: CostCategory.maintenance,
                    child: Text(CostCategory.maintenance),
                  ),
                  DropdownMenuItem(
                    value: CostCategory.overhead,
                    child: Text(CostCategory.overhead),
                  ),
                ],
                onChanged: (value) {
                  if (value != null) {
                    categoryController.text = value;
                    unitController.text = _getDefaultUnit(value);
                  }
                },
              ),
              const SizedBox(height: 16),
              TextField(
                controller: unitController,
                decoration: const InputDecoration(
                  labelText: 'وحدة القياس',
                  border: OutlineInputBorder(),
                ),
              ),
              const SizedBox(height: 16),
              Obx(
                () => CheckboxListTile(
                  title: const Text('قيمة افتراضية'),
                  value: isDefaultController.value,
                  onChanged: (value) {
                    isDefaultController.value = value ?? false;
                  },
                ),
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final name = nameController.text.trim();
              final valueText = valueController.text.trim();
              final category = categoryController.text.trim();
              final unit = unitController.text.trim();
              final isDefault = isDefaultController.value;
              
              if (name.isEmpty || valueText.isEmpty || category.isEmpty || unit.isEmpty) {
                _showError('الرجاء إدخال جميع البيانات المطلوبة');
                return;
              }
              
              final value = double.tryParse(valueText);
              if (value == null) {
                _showError('الرجاء إدخال قيمة صحيحة');
                return;
              }
              
              addOperationCost(name, category, value, unit, isDefault);
            },
            child: const Text('إضافة'),
          ),
        ],
      ),
    );
  }
  
  // عرض نافذة تحديث تكلفة تشغيل
  void showUpdateOperationCostDialog(OperationCost cost) {
    final valueController = TextEditingController(text: cost.value.toString());
    
    Get.dialog(
      AlertDialog(
        title: Text('تحديث ${cost.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الفئة: ${cost.category}'),
            Text('وحدة القياس: ${cost.unit}'),
            const SizedBox(height: 16),
            TextField(
              controller: valueController,
              decoration: const InputDecoration(
                labelText: 'القيمة الجديدة',
                border: OutlineInputBorder(),
              ),
              keyboardType: const TextInputType.numberWithOptions(decimal: true),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              final valueText = valueController.text.trim();
              
              if (valueText.isEmpty) {
                _showError('الرجاء إدخال قيمة صحيحة');
                return;
              }
              
              final value = double.tryParse(valueText);
              if (value == null) {
                _showError('الرجاء إدخال قيمة صحيحة');
                return;
              }
              
              updateOperationCost(cost, value);
              Get.back();
            },
            child: const Text('تحديث'),
          ),
        ],
      ),
    );
  }
  
  // الحصول على وحدة القياس الافتراضية للفئة
  String _getDefaultUnit(String category) {
    switch (category) {
      case CostCategory.electricity:
        return CostUnit.perKw;
      case CostCategory.labor:
        return CostUnit.perHour;
      case CostCategory.maintenance:
      case CostCategory.overhead:
        return CostUnit.percentage;
      default:
        return '';
    }
  }
  
  // عرض رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
  
  // عرض رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}
