import 'package:flutter/foundation.dart';
import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/operation_cost.dart';
import 'package:uuid/uuid.dart';

class OperationCostRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();
  static const String tableName = 'operation_costs';

  // إنشاء جدول تكاليف التشغيل
  Future<void> createTable() async {
    final db = await _databaseHelper.database;
    
    // التحقق من وجود الجدول
    final tables = await db.rawQuery(
      "SELECT name FROM sqlite_master WHERE type='table' AND name='$tableName'",
    );
    
    if (tables.isEmpty) {
      await db.execute('''
        CREATE TABLE $tableName (
          id TEXT PRIMARY KEY,
          name TEXT NOT NULL,
          category TEXT NOT NULL,
          value REAL NOT NULL,
          unit TEXT NOT NULL,
          isDefault INTEGER NOT NULL,
          lastUpdate TEXT NOT NULL
        )
      ''');
      
      // إضافة القيم الافتراضية
      await _addDefaultCosts();
    }
  }

  // إضافة القيم الافتراضية
  Future<void> _addDefaultCosts() async {
    final now = DateTime.now();
    
    // تكلفة الكهرباء
    await createOperationCost(OperationCost(
      name: 'سعر الكيلوواط',
      category: CostCategory.electricity,
      value: 1.5,
      unit: CostUnit.perKw,
      isDefault: true,
      lastUpdate: now,
    ));
    
    // تكلفة العمالة
    await createOperationCost(OperationCost(
      name: 'أجر المشغل',
      category: CostCategory.labor,
      value: 25.0,
      unit: CostUnit.perHour,
      isDefault: true,
      lastUpdate: now,
    ));
    
    // تكلفة الصيانة للماكينات الكبيرة
    await createOperationCost(OperationCost(
      name: 'معدل صيانة الماكينات الكبيرة',
      category: CostCategory.maintenance,
      value: 0.2,
      unit: CostUnit.percentage,
      isDefault: true,
      lastUpdate: now,
    ));
    
    // تكلفة الصيانة للماكينات الصغيرة
    await createOperationCost(OperationCost(
      name: 'معدل صيانة الماكينات الصغيرة',
      category: CostCategory.maintenance,
      value: 0.15,
      unit: CostUnit.percentage,
      isDefault: true,
      lastUpdate: now,
    ));
    
    // تكلفة النفقات الإضافية للماكينات الكبيرة
    await createOperationCost(OperationCost(
      name: 'معدل النفقات الإضافية للماكينات الكبيرة',
      category: CostCategory.overhead,
      value: 0.3,
      unit: CostUnit.percentage,
      isDefault: true,
      lastUpdate: now,
    ));
    
    // تكلفة النفقات الإضافية للماكينات الصغيرة
    await createOperationCost(OperationCost(
      name: 'معدل النفقات الإضافية للماكينات الصغيرة',
      category: CostCategory.overhead,
      value: 0.25,
      unit: CostUnit.percentage,
      isDefault: true,
      lastUpdate: now,
    ));
  }

  // إنشاء تكلفة تشغيل جديدة
  Future<String> createOperationCost(OperationCost cost) async {
    final costWithId = OperationCost(
      id: _uuid.v4(),
      name: cost.name,
      category: cost.category,
      value: cost.value,
      unit: cost.unit,
      isDefault: cost.isDefault,
      lastUpdate: cost.lastUpdate,
    );

    return await _databaseHelper.insert(
      tableName,
      costWithId.toMap(),
    );
  }

  // الحصول على جميع تكاليف التشغيل
  Future<List<OperationCost>> getAllOperationCosts() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      tableName,
    );

    return maps.map((map) => OperationCost.fromMap(map)).toList();
  }

  // الحصول على تكاليف التشغيل حسب الفئة
  Future<List<OperationCost>> getOperationCostsByCategory(String category) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableName WHERE category = ?',
      [category],
    );

    return maps.map((map) => OperationCost.fromMap(map)).toList();
  }

  // الحصول على تكلفة تشغيل بمعرف معين
  Future<OperationCost?> getOperationCostById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      tableName,
      id,
    );

    if (map == null) {
      return null;
    }

    return OperationCost.fromMap(map);
  }

  // الحصول على تكلفة تشغيل افتراضية حسب الفئة
  Future<OperationCost?> getDefaultOperationCost(String category, String name) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM $tableName WHERE category = ? AND name = ? AND isDefault = 1',
      [category, name],
    );

    if (maps.isEmpty) {
      return null;
    }

    return OperationCost.fromMap(maps.first);
  }

  // تحديث تكلفة تشغيل
  Future<int> updateOperationCost(OperationCost cost) async {
    return await _databaseHelper.update(
      tableName,
      cost.toMap(),
    );
  }

  // حذف تكلفة تشغيل
  Future<int> deleteOperationCost(String id) async {
    return await _databaseHelper.delete(tableName, id);
  }
}
