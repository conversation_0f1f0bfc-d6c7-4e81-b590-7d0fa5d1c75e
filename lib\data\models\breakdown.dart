/// نموذج بيانات الأعطال
class Breakdown {
  final String? id;
  final String machineId;
  final DateTime startTime; // وقت بداية العطل
  final DateTime? endTime; // وقت انتهاء العطل (null إذا كان العطل مستمر)
  final BreakdownType type; // نوع العطل
  final BreakdownCause cause; // سبب العطل
  final String description; // وصف تفصيلي للعطل
  final BreakdownSeverity severity; // خطورة العطل
  final String? reportedBy; // من قام بالإبلاغ عن العطل
  final String? fixedBy; // من قام بإصلاح العطل
  final String? fixDescription; // وصف الإصلاح
  final double? repairCost; // تكلفة الإصلاح
  final double? downtimeHours; // ساعات التوقف
  final List<String>? spareParts; // قطع الغيار المستخدمة
  final DateTime createdAt;
  final DateTime? updatedAt;

  Breakdown({
    this.id,
    required this.machineId,
    required this.startTime,
    this.endTime,
    required this.type,
    required this.cause,
    required this.description,
    required this.severity,
    this.reportedBy,
    this.fixedBy,
    this.fixDescription,
    this.repairCost,
    this.downtimeHours,
    this.spareParts,
    required this.createdAt,
    this.updatedAt,
  });

  // هل العطل مستمر؟
  bool get isOngoing => endTime == null;

  // مدة العطل بالساعات
  double get durationHours {
    if (endTime == null) {
      return DateTime.now().difference(startTime).inMinutes / 60.0;
    }
    return endTime!.difference(startTime).inMinutes / 60.0;
  }

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      if (id != null) 'id': id,
      'machineId': machineId,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'type': type.index,
      'cause': cause.index,
      'description': description,
      'severity': severity.index,
      'reportedBy': reportedBy,
      'fixedBy': fixedBy,
      'fixDescription': fixDescription,
      'repairCost': repairCost,
      'downtimeHours': downtimeHours,
      'spareParts': spareParts?.join(','),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory Breakdown.fromMap(Map<String, dynamic> map) {
    return Breakdown(
      id: map['id'],
      machineId: map['machineId'],
      startTime: DateTime.parse(map['startTime']),
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime']) : null,
      type: BreakdownType.values[map['type']],
      cause: BreakdownCause.values[map['cause']],
      description: map['description'],
      severity: BreakdownSeverity.values[map['severity']],
      reportedBy: map['reportedBy'],
      fixedBy: map['fixedBy'],
      fixDescription: map['fixDescription'],
      repairCost: map['repairCost'],
      downtimeHours: map['downtimeHours'],
      spareParts: map['spareParts']?.split(','),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: map['updatedAt'] != null ? DateTime.parse(map['updatedAt']) : null,
    );
  }

  // إنشاء نسخة معدلة
  Breakdown copyWith({
    String? id,
    String? machineId,
    DateTime? startTime,
    DateTime? endTime,
    BreakdownType? type,
    BreakdownCause? cause,
    String? description,
    BreakdownSeverity? severity,
    String? reportedBy,
    String? fixedBy,
    String? fixDescription,
    double? repairCost,
    double? downtimeHours,
    List<String>? spareParts,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Breakdown(
      id: id ?? this.id,
      machineId: machineId ?? this.machineId,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      type: type ?? this.type,
      cause: cause ?? this.cause,
      description: description ?? this.description,
      severity: severity ?? this.severity,
      reportedBy: reportedBy ?? this.reportedBy,
      fixedBy: fixedBy ?? this.fixedBy,
      fixDescription: fixDescription ?? this.fixDescription,
      repairCost: repairCost ?? this.repairCost,
      downtimeHours: downtimeHours ?? this.downtimeHours,
      spareParts: spareParts ?? this.spareParts,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

/// أنواع الأعطال
enum BreakdownType {
  mechanical,    // عطل ميكانيكي
  electrical,    // عطل كهربائي
  hydraulic,     // عطل هيدروليكي
  software,      // عطل برمجي
  tooling,       // عطل في الأدوات
  material,      // مشكلة في المواد
  operator,      // خطأ مشغل
  environmental, // عوامل بيئية
}

/// أسباب الأعطال
enum BreakdownCause {
  wearAndTear,      // تآكل طبيعي
  lackOfMaintenance, // عدم الصيانة
  overuse,          // الاستخدام المفرط
  materialDefect,   // عيب في المواد
  operatorError,    // خطأ المشغل
  powerFailure,     // انقطاع الكهرباء
  moldChange,       // تغيير الإسطمبة
  materialShortage, // نقص المواد الخام
  qualityIssue,     // مشكلة في الجودة
  unknown,          // سبب غير معروف
}

/// خطورة العطل
enum BreakdownSeverity {
  low,      // منخفض - لا يؤثر على الإنتاج
  medium,   // متوسط - يؤثر جزئياً على الإنتاج
  high,     // عالي - يوقف الإنتاج
  critical, // حرج - يؤثر على السلامة
}

/// إضافات مساعدة للتعامل مع الأنواع
extension BreakdownTypeExtension on BreakdownType {
  String get displayName {
    switch (this) {
      case BreakdownType.mechanical:
        return 'عطل ميكانيكي';
      case BreakdownType.electrical:
        return 'عطل كهربائي';
      case BreakdownType.hydraulic:
        return 'عطل هيدروليكي';
      case BreakdownType.software:
        return 'عطل برمجي';
      case BreakdownType.tooling:
        return 'عطل في الأدوات';
      case BreakdownType.material:
        return 'مشكلة في المواد';
      case BreakdownType.operator:
        return 'خطأ مشغل';
      case BreakdownType.environmental:
        return 'عوامل بيئية';
    }
  }
}

extension BreakdownCauseExtension on BreakdownCause {
  String get displayName {
    switch (this) {
      case BreakdownCause.wearAndTear:
        return 'تآكل طبيعي';
      case BreakdownCause.lackOfMaintenance:
        return 'عدم الصيانة';
      case BreakdownCause.overuse:
        return 'الاستخدام المفرط';
      case BreakdownCause.materialDefect:
        return 'عيب في المواد';
      case BreakdownCause.operatorError:
        return 'خطأ المشغل';
      case BreakdownCause.powerFailure:
        return 'انقطاع الكهرباء';
      case BreakdownCause.moldChange:
        return 'تغيير الإسطمبة';
      case BreakdownCause.materialShortage:
        return 'نقص المواد الخام';
      case BreakdownCause.qualityIssue:
        return 'مشكلة في الجودة';
      case BreakdownCause.unknown:
        return 'سبب غير معروف';
    }
  }
}

extension BreakdownSeverityExtension on BreakdownSeverity {
  String get displayName {
    switch (this) {
      case BreakdownSeverity.low:
        return 'منخفض';
      case BreakdownSeverity.medium:
        return 'متوسط';
      case BreakdownSeverity.high:
        return 'عالي';
      case BreakdownSeverity.critical:
        return 'حرج';
    }
  }
}
