import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/order.dart';
import 'package:uuid/uuid.dart';

class OrderRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إنشاء طلبية جديدة
  Future<String> createOrder(Order order) async {
    final orderWithId = Order(
      id: _uuid.v4(),
      date: order.date,
      items: order.items,
      transportCost: order.transportCost,
      shippingCost: order.shippingCost,
      clearanceCost: order.clearanceCost,
      customsDuty: order.customsDuty,
      exchangeRate: order.exchangeRate,
      currency: order.currency,
    );

    // حفظ الطلبية
    final orderId = await _databaseHelper.insert(
      DatabaseHelper.tableOrders,
      orderWithId.toMap(),
    );

    // حفظ عناصر الطلبية
    for (var item in orderWithId.items) {
      final itemWithIds = OrderItem(
        id: _uuid.v4(),
        orderId: orderWithId.id,
        accessoryId: item.accessoryId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        weight: item.weight,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableOrderItems,
        itemWithIds.toMap(),
      );
    }

    return orderId;
  }

  // الحصول على جميع الطلبيات
  Future<List<Order>> getAllOrders() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      DatabaseHelper.tableOrders,
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getOrderItems(map['id']);
        return Order.fromMap(map, items);
      }).toList(),
    );
  }

  // الحصول على طلبية بمعرف معين
  Future<Order?> getOrderById(String id) async {
    final Map<String, dynamic>? map = await _databaseHelper.queryRow(
      DatabaseHelper.tableOrders,
      id,
    );

    if (map == null) {
      return null;
    }

    final items = await _getOrderItems(id);
    return Order.fromMap(map, items);
  }

  // تحديث طلبية
  Future<void> updateOrder(Order order) async {
    // تحديث بيانات الطلبية
    await _databaseHelper.update(DatabaseHelper.tableOrders, order.toMap());

    // حذف العناصر القديمة
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableOrderItems} WHERE orderId = ?',
      [order.id],
    );

    // إضافة العناصر الجديدة
    for (var item in order.items) {
      final itemWithIds = OrderItem(
        id: _uuid.v4(),
        orderId: order.id,
        accessoryId: item.accessoryId,
        name: item.name,
        price: item.price,
        quantity: item.quantity,
        weight: item.weight,
      );

      await _databaseHelper.insert(
        DatabaseHelper.tableOrderItems,
        itemWithIds.toMap(),
      );
    }
  }

  // حذف طلبية
  Future<void> deleteOrder(String id) async {
    // حذف عناصر الطلبية أولاً
    await _databaseHelper.rawQuery(
      'DELETE FROM ${DatabaseHelper.tableOrderItems} WHERE orderId = ?',
      [id],
    );

    // ثم حذف الطلبية نفسها
    await _databaseHelper.delete(DatabaseHelper.tableOrders, id);
  }

  // الحصول على عناصر طلبية
  Future<List<OrderItem>> _getOrderItems(String orderId) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableOrderItems} WHERE orderId = ?',
      [orderId],
    );

    return maps.map((map) => OrderItem.fromMap(map)).toList();
  }

  // الحصول على الطلبيات بناءً على نطاق تاريخ
  Future<List<Order>> getOrdersByDateRange(
    DateTime startDate,
    DateTime endDate,
  ) async {
    final String startStr = startDate.toIso8601String().split('T')[0];
    final String endStr = endDate.toIso8601String().split('T')[0];

    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      'SELECT * FROM ${DatabaseHelper.tableOrders} WHERE date(date) BETWEEN ? AND ? ORDER BY date DESC',
      [startStr, endStr],
    );

    return Future.wait(
      maps.map((map) async {
        final items = await _getOrderItems(map['id']);
        return Order.fromMap(map, items);
      }).toList(),
    );
  }

  // حساب تكلفة العنصر بالجنيه المصري
  Future<double> calculateItemEgpCost(String orderId, String itemId) async {
    final order = await getOrderById(orderId);
    if (order == null) {
      return 0;
    }

    final item = order.items.firstWhere(
      (item) => item.id == itemId,
      orElse:
          () => OrderItem(
            accessoryId: '',
            name: '',
            price: 0,
            quantity: 0,
            weight: 0,
          ),
    );

    if (item.id == null) {
      return 0;
    }

    return order.calculateItemEgpCost(item);
  }
}
