import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/themes/enhanced_app_theme.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/responsive_info_card.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/responsive_search_bar.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'dashboard_controller.dart';

class DashboardPage extends StatelessWidget {
  const DashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    Get.put(DashboardController());
    final searchController = TextEditingController();

    return ResponsivePage(
      title: 'نظام إدارة المصنع',
      drawer: const CustomDrawer(),
      body: GetBuilder<DashboardController>(
        builder: (controller) {
          if (controller.isLoading.value) {
            return const Center(
              child: CircularProgressIndicator(
                color: EnhancedAppTheme.primaryColor,
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: controller.refreshData,
            color: EnhancedAppTheme.primaryColor,
            child: SingleChildScrollView(
              padding: EdgeInsets.all(ScreenSize.getPadding(16)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildWelcomeHeader(),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  ResponsiveSearchBar(
                    controller: searchController,
                    hintText: 'البحث في النظام...',
                    onChanged: (query) {
                      // يمكن تنفيذ البحث هنا
                    },
                    actions: [
                      IconButton(
                        icon: Icon(
                          Icons.notifications_none,
                          color: EnhancedAppTheme.textSecondary,
                          size: ScreenSize.isSmallScreen ? 20 : 24,
                        ),
                        padding: EdgeInsets.all(ScreenSize.getPadding(8)),
                        constraints: BoxConstraints(
                          minWidth: ScreenSize.isSmallScreen ? 32 : 40,
                          minHeight: ScreenSize.isSmallScreen ? 32 : 40,
                        ),
                        onPressed: () {
                          // عرض الإشعارات
                        },
                      ),
                    ],
                  ),
                  SizedBox(height: ScreenSize.getPadding(24)),
                  _buildSummaryCards(controller),
                  SizedBox(height: ScreenSize.getPadding(24)),
                  _buildProductionSection(controller),
                  SizedBox(height: ScreenSize.getPadding(24)),
                  _buildInventorySection(controller),
                  SizedBox(height: ScreenSize.getPadding(24)),
                  _buildQuickActions(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildWelcomeHeader() {
    final now = DateTime.now();
    String greeting;

    if (now.hour < 12) {
      greeting = 'صباح الخير';
    } else if (now.hour < 18) {
      greeting = 'مساء الخير';
    } else {
      greeting = 'مساء الخير';
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          greeting,
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 20 : 24,
            fontWeight: FontWeight.bold,
            color: EnhancedAppTheme.textPrimary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(4)),
        TextUtils.responsiveText(
          'مرحباً بك في نظام إدارة المصنع',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            color: EnhancedAppTheme.textSecondary,
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  Widget _buildSummaryCards(DashboardController controller) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'نظرة عامة',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.bold,
            color: EnhancedAppTheme.textPrimary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        GridView.count(
          crossAxisCount: ScreenSize.isSmallScreen ? 1 : 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisSpacing: ScreenSize.getPadding(16),
          mainAxisSpacing: ScreenSize.getPadding(16),
          childAspectRatio: ScreenSize.isSmallScreen ? 2.0 : 1.5,
          children: [
            ResponsiveInfoCard(
              title: 'الإنتاج اليومي',
              value: '${controller.todayProduction} قطعة',
              icon: Icons.precision_manufacturing,
              iconColor: EnhancedAppTheme.primaryColor,
              onTap: () => Get.toNamed('/daily-production'),
            ),
            ResponsiveInfoCard(
              title: 'المخزون',
              value: '${controller.totalInventory} منتج',
              icon: Icons.inventory,
              iconColor: EnhancedAppTheme.accentColor,
              onTap: () => Get.toNamed('/inventory'),
            ),
            ResponsiveInfoCard(
              title: 'الطلبيات',
              value: '${controller.pendingOrders} طلبية',
              icon: Icons.shopping_bag,
              iconColor: Colors.purple,
              onTap: () => Get.toNamed('/orders'),
            ),
            ResponsiveInfoCard(
              title: 'المبيعات',
              value: '${controller.monthlySales} ج.م',
              icon: Icons.point_of_sale,
              iconColor: Colors.green,
              onTap: () => Get.toNamed('/sales'),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildProductionSection(DashboardController controller) {
    return ResponsiveStatsCard(
      title: 'حالة الإنتاج',
      headerColor: EnhancedAppTheme.primaryColor,
      onTap: () => Get.toNamed('/daily-production'),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.white,
        size: ScreenSize.isSmallScreen ? 14 : 16,
      ),
      stats: [
        ResponsiveStatItem(
          label: 'الماكينات العاملة',
          value: '${controller.activeMachines} / ${controller.totalMachines}',
          icon: Icons.precision_manufacturing,
          color: EnhancedAppTheme.primaryColor,
        ),
        ResponsiveStatItem(
          label: 'الإنتاج المتوقع اليوم',
          value: '${controller.expectedProduction} قطعة',
          icon: Icons.trending_up,
          color: EnhancedAppTheme.successColor,
        ),
        ResponsiveStatItem(
          label: 'الإسطمبات المستخدمة',
          value: '${controller.activeMolds} / ${controller.totalMolds}',
          icon: Icons.settings,
          color: EnhancedAppTheme.accentColor,
        ),
      ],
    );
  }

  Widget _buildInventorySection(DashboardController controller) {
    return ResponsiveStatsCard(
      title: 'حالة المخزون',
      headerColor: EnhancedAppTheme.accentColor,
      onTap: () => Get.toNamed('/inventory'),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: Colors.white,
        size: ScreenSize.isSmallScreen ? 14 : 16,
      ),
      stats: [
        ResponsiveStatItem(
          label: 'المواد الخام',
          value: '${controller.rawMaterialsCount} صنف',
          icon: Icons.category,
          color: EnhancedAppTheme.primaryColor,
        ),
        ResponsiveStatItem(
          label: 'المنتجات الجاهزة',
          value: '${controller.finishedProductsCount} منتج',
          icon: Icons.inventory_2,
          color: EnhancedAppTheme.successColor,
        ),
        ResponsiveStatItem(
          label: 'المواد التي تحتاج إعادة طلب',
          value: '${controller.lowStockCount} صنف',
          icon: Icons.warning_amber,
          color: EnhancedAppTheme.warningColor,
        ),
      ],
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextUtils.responsiveText(
          'إجراءات سريعة',
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
            fontWeight: FontWeight.bold,
            color: EnhancedAppTheme.textPrimary,
          ),
          maxLines: 1,
        ),
        SizedBox(height: ScreenSize.getPadding(16)),
        Wrap(
          spacing: ScreenSize.getPadding(16),
          runSpacing: ScreenSize.getPadding(16),
          children: [
            _buildActionButton(
              label: 'تسجيل إنتاج',
              icon: Icons.add_circle_outline,
              onTap: () => Get.toNamed('/daily-production'),
              color: EnhancedAppTheme.primaryColor,
            ),
            _buildActionButton(
              label: 'تغيير إسطمبة',
              icon: Icons.swap_horiz,
              onTap: () => Get.toNamed('/machines'),
              color: EnhancedAppTheme.accentColor,
            ),
            _buildActionButton(
              label: 'استلام خامات',
              icon: Icons.inventory,
              onTap: () => Get.toNamed('/raw-materials'),
              color: Colors.teal,
            ),
            _buildActionButton(
              label: 'تسجيل بيع',
              icon: Icons.point_of_sale,
              onTap: () => Get.toNamed('/sales'),
              color: Colors.green,
            ),
            _buildActionButton(
              label: 'تقرير التكاليف',
              icon: Icons.attach_money,
              onTap: () => Get.toNamed('/cost-report'),
              color: Colors.purple,
            ),
            _buildActionButton(
              label: 'تقرير المبيعات',
              icon: Icons.bar_chart,
              onTap: () => Get.toNamed('/sales-report'),
              color: Colors.indigo,
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
      child: Container(
        width: ScreenSize.isSmallScreen ? 90 : 110,
        padding: EdgeInsets.symmetric(
          vertical: ScreenSize.getPadding(12),
          horizontal: ScreenSize.getPadding(8),
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(EnhancedAppTheme.borderRadius),
          boxShadow: EnhancedAppTheme.cardShadow,
          border: Border.all(color: color.withAlpha(77)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.all(ScreenSize.getPadding(8)),
              decoration: BoxDecoration(
                color: color.withAlpha(25),
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                color: color,
                size: ScreenSize.isSmallScreen ? 20 : 24,
              ),
            ),
            SizedBox(height: ScreenSize.getPadding(8)),
            TextUtils.responsiveText(
              label,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 10 : 12,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              maxLines: 1,
            ),
          ],
        ),
      ),
    );
  }
}
