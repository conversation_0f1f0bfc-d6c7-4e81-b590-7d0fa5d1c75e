import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/machine_capacity.dart';
import 'package:mostafa_final/data/models/smart_schedule.dart';
import 'package:mostafa_final/data/models/production.dart';
import 'package:uuid/uuid.dart';

class SmartPlanningRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  static const String tableMachineCapacity = 'machine_capacity';
  static const String tablePendingOrders = 'pending_orders';
  static const String tableSmartSchedules = 'smart_schedules';
  static const String tableScheduledTasks = 'scheduled_tasks';
  static const String tableScheduleAlerts = 'schedule_alerts';

  // إنشاء الجداول المطلوبة
  Future<void> createTables() async {
    final db = await _databaseHelper.database;

    // جدول الطاقة الإنتاجية للماكينات
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableMachineCapacity (
        id TEXT PRIMARY KEY,
        machineId TEXT NOT NULL,
        moldId TEXT NOT NULL,
        theoreticalCycleTime REAL NOT NULL,
        actualCycleTime REAL NOT NULL,
        efficiency REAL NOT NULL,
        partsPerCycle INTEGER NOT NULL,
        setupTime REAL NOT NULL,
        calculatedAt TEXT NOT NULL,
        sampleSize INTEGER NOT NULL,
        FOREIGN KEY (machineId) REFERENCES machines (id),
        FOREIGN KEY (moldId) REFERENCES molds (id)
      )
    ''');

    // جدول الطلبيات المعلقة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tablePendingOrders (
        id TEXT PRIMARY KEY,
        productCode TEXT NOT NULL,
        productName TEXT NOT NULL,
        requiredQuantity INTEGER NOT NULL,
        completedQuantity INTEGER DEFAULT 0,
        dueDate TEXT NOT NULL,
        priority INTEGER NOT NULL,
        customerName TEXT,
        createdAt TEXT NOT NULL
      )
    ''');

    // جدول الجدولة الذكية
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableSmartSchedules (
        id TEXT PRIMARY KEY,
        scheduleDate TEXT NOT NULL,
        totalCapacityUtilization REAL NOT NULL,
        createdAt TEXT NOT NULL,
        status INTEGER NOT NULL
      )
    ''');

    // جدول المهام المجدولة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableScheduledTasks (
        id TEXT PRIMARY KEY,
        scheduleId TEXT NOT NULL,
        machineId TEXT NOT NULL,
        moldId TEXT NOT NULL,
        productCode TEXT NOT NULL,
        plannedQuantity INTEGER NOT NULL,
        estimatedRunTime REAL NOT NULL,
        setupTime REAL NOT NULL,
        plannedStartTime TEXT NOT NULL,
        plannedEndTime TEXT NOT NULL,
        priority INTEGER NOT NULL,
        status INTEGER NOT NULL,
        notes TEXT,
        FOREIGN KEY (scheduleId) REFERENCES $tableSmartSchedules (id),
        FOREIGN KEY (machineId) REFERENCES machines (id),
        FOREIGN KEY (moldId) REFERENCES molds (id)
      )
    ''');

    // جدول تنبيهات الجدولة
    await db.execute('''
      CREATE TABLE IF NOT EXISTS $tableScheduleAlerts (
        id TEXT PRIMARY KEY,
        scheduleId TEXT NOT NULL,
        type INTEGER NOT NULL,
        message TEXT NOT NULL,
        severity INTEGER NOT NULL,
        createdAt TEXT NOT NULL,
        isRead INTEGER DEFAULT 0,
        metadata TEXT,
        FOREIGN KEY (scheduleId) REFERENCES $tableSmartSchedules (id)
      )
    ''');

    // إنشاء الفهارس
    await _createIndexes(db);
  }

  // إنشاء الفهارس لتحسين الأداء
  Future<void> _createIndexes(db) async {
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_machine_capacity_machine ON $tableMachineCapacity (machineId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_machine_capacity_mold ON $tableMachineCapacity (moldId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_pending_orders_due_date ON $tablePendingOrders (dueDate)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_pending_orders_priority ON $tablePendingOrders (priority)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_schedule ON $tableScheduledTasks (scheduleId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_machine ON $tableScheduledTasks (machineId)',
    );
    await db.execute(
      'CREATE INDEX IF NOT EXISTS idx_schedule_alerts_schedule ON $tableScheduleAlerts (scheduleId)',
    );
  }

  // === طرق إدارة الطاقة الإنتاجية ===

  // حساب الطاقة الإنتاجية للماكينة بناءً على البيانات التاريخية
  Future<MachineCapacity?> calculateMachineCapacity(
    String machineId,
    String moldId,
  ) async {
    final db = await _databaseHelper.database;

    // الحصول على البيانات التاريخية للإنتاج (آخر 30 يوم)
    final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));

    final List<Map<String, dynamic>> productionData = await db.rawQuery(
      '''
      SELECT cycleTime, partsProduced, shiftsCount
      FROM production
      WHERE machineId = ? AND moldId = ? AND date >= ?
      ORDER BY date DESC
    ''',
      [machineId, moldId, thirtyDaysAgo.toIso8601String()],
    );

    if (productionData.isEmpty) {
      return null; // لا توجد بيانات كافية
    }

    // حساب المتوسطات
    double totalCycleTime = 0;
    int sampleCount = productionData.length;

    for (var record in productionData) {
      totalCycleTime += record['cycleTime'];
    }

    double avgCycleTime = totalCycleTime / sampleCount;

    // الحصول على بيانات الإسطمبة
    final moldData = await db.rawQuery(
      '''
      SELECT cavityCount FROM molds WHERE id = ?
    ''',
      [moldId],
    );

    if (moldData.isEmpty) {
      return null;
    }

    int partsPerCycle = (moldData.first['cavityCount'] as num).toInt();

    // حساب الكفاءة (مقارنة بالوقت النظري)
    double theoreticalCycleTime =
        avgCycleTime * 0.85; // افتراض أن النظري أسرع بـ 15%
    double efficiency = (theoreticalCycleTime / avgCycleTime) * 100;

    // وقت الإعداد الافتراضي (30 دقيقة)
    double setupTime = 30.0;

    final capacity = MachineCapacity(
      id: _uuid.v4(),
      machineId: machineId,
      moldId: moldId,
      theoreticalCycleTime: theoreticalCycleTime,
      actualCycleTime: avgCycleTime,
      efficiency: efficiency,
      partsPerCycle: partsPerCycle,
      setupTime: setupTime,
      calculatedAt: DateTime.now(),
      sampleSize: sampleCount,
    );

    // حفظ النتيجة في قاعدة البيانات
    await _databaseHelper.insert(tableMachineCapacity, capacity.toMap());

    return capacity;
  }

  // الحصول على الطاقة الإنتاجية المحفوظة
  Future<MachineCapacity?> getMachineCapacity(
    String machineId,
    String moldId,
  ) async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      '''
      SELECT * FROM $tableMachineCapacity
      WHERE machineId = ? AND moldId = ?
      ORDER BY calculatedAt DESC
      LIMIT 1
    ''',
      [machineId, moldId],
    );

    if (maps.isEmpty) {
      return null;
    }

    return MachineCapacity.fromMap(maps.first);
  }

  // الحصول على جميع الطاقات الإنتاجية
  Future<List<MachineCapacity>> getAllMachineCapacities() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.queryAllRows(
      tableMachineCapacity,
    );
    return maps.map((map) => MachineCapacity.fromMap(map)).toList();
  }

  // === طرق إدارة الطلبيات المعلقة ===

  // إضافة طلبية معلقة
  Future<String> createPendingOrder(PendingOrder order) async {
    final orderWithId = PendingOrder(
      id: _uuid.v4(),
      productCode: order.productCode,
      productName: order.productName,
      requiredQuantity: order.requiredQuantity,
      completedQuantity: order.completedQuantity,
      dueDate: order.dueDate,
      priority: order.priority,
      customerName: order.customerName,
      createdAt: order.createdAt,
    );

    return await _databaseHelper.insert(
      tablePendingOrders,
      orderWithId.toMap(),
    );
  }

  // الحصول على الطلبيات المعلقة مرتبة حسب الأولوية والتاريخ
  Future<List<PendingOrder>> getPendingOrders() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery('''
      SELECT * FROM $tablePendingOrders
      WHERE completedQuantity < requiredQuantity
      ORDER BY priority ASC, dueDate ASC
    ''');

    return maps.map((map) => PendingOrder.fromMap(map)).toList();
  }

  // تحديث كمية الإنجاز للطلبية
  Future<int> updateOrderProgress(String orderId, int completedQuantity) async {
    final db = await _databaseHelper.database;
    return await db.update(
      tablePendingOrders,
      {'completedQuantity': completedQuantity},
      where: 'id = ?',
      whereArgs: [orderId],
    );
  }

  // === طرق إدارة الجدولة الذكية ===

  // إنشاء جدولة ذكية جديدة
  Future<String> createSmartSchedule(SmartSchedule schedule) async {
    final scheduleWithId = SmartSchedule(
      id: _uuid.v4(),
      scheduleDate: schedule.scheduleDate,
      tasks: schedule.tasks,
      totalCapacityUtilization: schedule.totalCapacityUtilization,
      createdAt: schedule.createdAt,
      status: schedule.status,
    );

    final scheduleId = await _databaseHelper.insert(
      tableSmartSchedules,
      scheduleWithId.toMap(),
    );

    // حفظ المهام
    for (var task in schedule.tasks) {
      final taskWithId = ScheduledTask(
        id: _uuid.v4(),
        scheduleId: scheduleId,
        machineId: task.machineId,
        moldId: task.moldId,
        productCode: task.productCode,
        plannedQuantity: task.plannedQuantity,
        estimatedRunTime: task.estimatedRunTime,
        setupTime: task.setupTime,
        plannedStartTime: task.plannedStartTime,
        plannedEndTime: task.plannedEndTime,
        priority: task.priority,
        status: task.status,
        notes: task.notes,
      );

      await _databaseHelper.insert(tableScheduledTasks, taskWithId.toMap());
    }

    return scheduleId;
  }

  // الحصول على الجدولة مع المهام
  Future<SmartSchedule?> getSmartScheduleWithTasks(String scheduleId) async {
    final List<Map<String, dynamic>> scheduleMaps = await _databaseHelper
        .rawQuery(
          '''
      SELECT * FROM $tableSmartSchedules WHERE id = ?
    ''',
          [scheduleId],
        );

    if (scheduleMaps.isEmpty) {
      return null;
    }

    final List<Map<String, dynamic>> taskMaps = await _databaseHelper.rawQuery(
      '''
      SELECT * FROM $tableScheduledTasks WHERE scheduleId = ? ORDER BY plannedStartTime
    ''',
      [scheduleId],
    );

    final tasks = taskMaps.map((map) => ScheduledTask.fromMap(map)).toList();

    final schedule = SmartSchedule.fromMap(scheduleMaps.first);

    return SmartSchedule(
      id: schedule.id,
      scheduleDate: schedule.scheduleDate,
      tasks: tasks,
      totalCapacityUtilization: schedule.totalCapacityUtilization,
      createdAt: schedule.createdAt,
      status: schedule.status,
    );
  }

  // الحصول على الجدولة النشطة
  Future<SmartSchedule?> getActiveSchedule() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery(
      '''
      SELECT * FROM $tableSmartSchedules
      WHERE status = ?
      ORDER BY scheduleDate DESC
      LIMIT 1
    ''',
      [ScheduleStatus.active.index],
    );

    if (maps.isEmpty) {
      return null;
    }

    return getSmartScheduleWithTasks(maps.first['id']);
  }

  // === طرق إدارة التنبيهات ===

  // إضافة تنبيه
  Future<String> createAlert(ScheduleAlert alert) async {
    final alertWithId = ScheduleAlert(
      id: _uuid.v4(),
      scheduleId: alert.scheduleId,
      type: alert.type,
      message: alert.message,
      severity: alert.severity,
      createdAt: alert.createdAt,
      isRead: alert.isRead,
      metadata: alert.metadata,
    );

    return await _databaseHelper.insert(
      tableScheduleAlerts,
      alertWithId.toMap(),
    );
  }

  // الحصول على التنبيهات غير المقروءة
  Future<List<ScheduleAlert>> getUnreadAlerts() async {
    final List<Map<String, dynamic>> maps = await _databaseHelper.rawQuery('''
      SELECT * FROM $tableScheduleAlerts
      WHERE isRead = 0
      ORDER BY severity DESC, createdAt DESC
    ''');

    return maps.map((map) => ScheduleAlert.fromMap(map)).toList();
  }

  // تحديد التنبيه كمقروء
  Future<int> markAlertAsRead(String alertId) async {
    final db = await _databaseHelper.database;
    return await db.update(
      tableScheduleAlerts,
      {'isRead': 1},
      where: 'id = ?',
      whereArgs: [alertId],
    );
  }
}
