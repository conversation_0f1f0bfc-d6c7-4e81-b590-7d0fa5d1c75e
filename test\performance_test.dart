import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/di/dependency_injection.dart';
import 'package:mostafa_final/presentation/controllers/inventory_controller.dart';
import 'package:mostafa_final/presentation/controllers/sales_controller.dart';
import 'package:mostafa_final/presentation/pages/dashboard/dashboard_page.dart';
import 'package:mostafa_final/presentation/pages/inventory/inventory_page.dart';
import 'package:mostafa_final/presentation/pages/sales/sales_page.dart';
import 'package:mostafa_final/utils/performance_monitor.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة حقن التبعيات
  setUpAll(() {
    DependencyInjection.init();
  });

  group('اختبارات أداء قاعدة البيانات', () {
    final performanceMonitor = PerformanceMonitor();
    final inventoryRepository = InventoryRepository();
    final productionRepository = ProductionRepository();
    final salesRepository = SalesRepository();

    test('اختبار أداء استعلامات المخزون', () async {
      // قياس أداء استعلام المخزون
      final startDate = DateTime.now().subtract(const Duration(days: 30));
      final endDate = DateTime.now();

      final result = await performanceMonitor.measureDatabaseQuery(
        () => inventoryRepository.getRawMaterialConsumptionReport(
          startDate,
          endDate,
        ),
        'raw_material_consumption_report',
      );

      expect(result, isA<List<Map<String, dynamic>>>());
    });

    test('اختبار أداء استعلامات الإنتاج', () async {
      // قياس أداء استعلام الإنتاج
      final date = DateTime.now();

      final result = await performanceMonitor.measureDatabaseQuery(
        () => productionRepository.getProductionByDate(date),
        'production_by_date',
      );

      expect(result, isA<List<Map<String, dynamic>>>());
    });

    test('اختبار أداء استعلامات المبيعات', () async {
      // قياس أداء استعلام المبيعات
      final result = await performanceMonitor.measureDatabaseQuery(
        () => salesRepository.getTopSellingProducts(10),
        'top_selling_products',
      );

      expect(result, isA<List<Map<String, dynamic>>>());
    });

    test('اختبار أداء قاعدة البيانات تحت ضغط عالٍ', () async {
      // اختبار أداء قاعدة البيانات تحت ضغط عالٍ
      final results = await performanceMonitor.testDatabasePerformance();

      expect(results, isA<Map<String, dynamic>>());
      expect(results.containsKey('simple_query'), isTrue);
      expect(results.containsKey('complex_query'), isTrue);
      expect(results.containsKey('bulk_insert'), isTrue);

      // طباعة النتائج
      debugPrint('نتائج اختبار أداء قاعدة البيانات:');
      debugPrint('استعلام بسيط: ${results['simple_query']} ms');
      debugPrint('استعلام معقد: ${results['complex_query']} ms');
      debugPrint('إدراج متعدد: ${results['bulk_insert']} ms');
    });
  });

  group('اختبارات أداء واجهة المستخدم', () {
    final performanceMonitor = PerformanceMonitor();

    testWidgets('اختبار أداء الصفحة الرئيسية', (WidgetTester tester) async {
      // تهيئة مراقبة الأداء
      performanceMonitor.startMonitoring();

      // بناء التطبيق
      await tester.pumpWidget(
        GetMaterialApp(
          home: const DashboardPage(),
          initialBinding: BindingsBuilder(() {
            DependencyInjection.init();
          }),
        ),
      );

      // قياس زمن الاستجابة
      final responseTime = await performanceMonitor.measureUIResponseTime(
        () async {
          await tester.pump();
          await tester.pumpAndSettle();
        },
        'dashboard_page',
      );

      // التحقق من زمن الاستجابة
      expect(responseTime, lessThan(1000.0)); // أقل من 1 ثانية

      // إيقاف مراقبة الأداء
      performanceMonitor.stopMonitoring();
    });

    testWidgets('اختبار أداء صفحة المخزون', (WidgetTester tester) async {
      // تهيئة مراقبة الأداء
      performanceMonitor.startMonitoring();

      // بناء التطبيق
      await tester.pumpWidget(
        GetMaterialApp(
          home: const InventoryPage(),
          initialBinding: BindingsBuilder(() {
            DependencyInjection.init();
            Get.put(InventoryController());
          }),
        ),
      );

      // قياس زمن الاستجابة
      final responseTime = await performanceMonitor.measureUIResponseTime(
        () async {
          await tester.pump();
          await tester.pumpAndSettle();
        },
        'inventory_page',
      );

      // التحقق من زمن الاستجابة
      expect(responseTime, lessThan(1000.0)); // أقل من 1 ثانية

      // إيقاف مراقبة الأداء
      performanceMonitor.stopMonitoring();
    });

    testWidgets('اختبار أداء صفحة المبيعات', (WidgetTester tester) async {
      // تهيئة مراقبة الأداء
      performanceMonitor.startMonitoring();

      // بناء التطبيق
      await tester.pumpWidget(
        GetMaterialApp(
          home: const SalesPage(),
          initialBinding: BindingsBuilder(() {
            DependencyInjection.init();
            Get.put(SalesController());
          }),
        ),
      );

      // قياس زمن الاستجابة
      final responseTime = await performanceMonitor.measureUIResponseTime(
        () async {
          await tester.pump();
          await tester.pumpAndSettle();
        },
        'sales_page',
      );

      // التحقق من زمن الاستجابة
      expect(responseTime, lessThan(1000.0)); // أقل من 1 ثانية

      // إيقاف مراقبة الأداء
      performanceMonitor.stopMonitoring();
    });
  });

  group('اختبارات استهلاك الذاكرة', () {
    final performanceMonitor = PerformanceMonitor();

    test('قياس استهلاك الذاكرة', () async {
      // قياس استهلاك الذاكرة
      final memoryUsage = await performanceMonitor.measureMemoryUsage();

      // طباعة استهلاك الذاكرة
      debugPrint('استهلاك الذاكرة: $memoryUsage KB');

      // لا يمكن التحقق من قيمة محددة، لكن يمكن التحقق من أنها أكبر من الصفر
      expect(memoryUsage, isA<int>());
    });
  });

  group('إنشاء تقرير الأداء', () {
    final performanceMonitor = PerformanceMonitor();

    test('إنشاء تقرير أداء مفصل', () async {
      // إنشاء تقرير أداء
      final reportPath = await performanceMonitor.generatePerformanceReport();

      // طباعة مسار التقرير
      debugPrint('تم إنشاء تقرير الأداء في: $reportPath');

      // التحقق من أن التقرير تم إنشاؤه
      expect(reportPath.isNotEmpty, isTrue);
    });
  });
}
