import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/predictive_analytics.dart';
import 'package:mostafa_final/presentation/controllers/predictive_analytics_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

/// صفحة التحليل التنبؤي والذكاء الاصطناعي
class PredictiveAnalyticsPage extends StatelessWidget {
  const PredictiveAnalyticsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(PredictiveAnalyticsController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('التحليل التنبؤي والذكاء الاصطناعي'),
        centerTitle: true,
        actions: [
          // زر تشغيل التحليل الشامل
          Obx(
            () => IconButton(
              icon:
                  controller.isAnalysisRunning.value
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.analytics),
              onPressed:
                  controller.isAnalysisRunning.value
                      ? null
                      : controller.runComprehensiveAnalysis,
              tooltip: 'تشغيل التحليل الشامل',
            ),
          ),
          // زر الإعدادات
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(controller),
            tooltip: 'إعدادات التحليل',
          ),
          // زر التصدير
          IconButton(
            icon: const Icon(Icons.file_download),
            onPressed: controller.exportPredictiveAnalysisReport,
            tooltip: 'تصدير التقرير',
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(
            message: 'جاري تحميل التحليل التنبؤي...',
          );
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildQuickStatsSection(controller),
              const SizedBox(height: 20),
              _buildSmartRecommendationsSection(controller),
              const SizedBox(height: 20),
              _buildTabSection(controller),
            ],
          ),
        );
      }),
    );
  }

  /// قسم الإحصائيات السريعة
  Widget _buildQuickStatsSection(PredictiveAnalyticsController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نظرة عامة على التحليل التنبؤي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final stats = controller.quickStats;
              return GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 4,
                childAspectRatio: 1.2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  _buildStatCard(
                    'إجمالي التنبؤات',
                    '${stats['totalPredictions']}',
                    Icons.analytics,
                    Colors.blue,
                  ),
                  _buildStatCard(
                    'مخاطر عالية',
                    '${stats['highRiskPredictions']}',
                    Icons.warning,
                    Colors.red,
                  ),
                  _buildStatCard(
                    'تنبيهات حرجة',
                    '${stats['criticalNotifications']}',
                    Icons.error,
                    Colors.orange,
                  ),
                  _buildStatCard(
                    'متوسط الاحتمالية',
                    '${stats['avgFailureProbability'].toStringAsFixed(1)}%',
                    Icons.percent,
                    Colors.green,
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  /// بطاقة إحصائية
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  /// قسم التوصيات الذكية
  Widget _buildSmartRecommendationsSection(
    PredictiveAnalyticsController controller,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التوصيات الذكية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final recommendations = controller.smartRecommendations;
              return Column(
                children:
                    recommendations
                        .map(
                          (recommendation) => Container(
                            width: double.infinity,
                            margin: const EdgeInsets.only(bottom: 8),
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: Colors.blue.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.blue.withValues(alpha: 0.3),
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.lightbulb,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    recommendation,
                                    style: const TextStyle(fontSize: 14),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        )
                        .toList(),
              );
            }),
          ],
        ),
      ),
    );
  }

  /// قسم التبويبات
  Widget _buildTabSection(PredictiveAnalyticsController controller) {
    return DefaultTabController(
      length: 4,
      child: Column(
        children: [
          const TabBar(
            tabs: [
              Tab(text: 'التنبؤ بالأعطال', icon: Icon(Icons.build)),
              Tab(text: 'تحليل الاتجاهات', icon: Icon(Icons.trending_up)),
              Tab(text: 'التوقعات المستقبلية', icon: Icon(Icons.timeline)),
              Tab(
                text: 'التنبيهات التنبؤية',
                icon: Icon(Icons.notifications_active),
              ),
            ],
          ),
          SizedBox(
            height: 600,
            child: TabBarView(
              children: [
                _buildFailurePredictionsTab(controller),
                _buildTrendAnalysisTab(controller),
                _buildForecastsTab(controller),
                _buildPredictiveNotificationsTab(controller),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب التنبؤ بالأعطال
  Widget _buildFailurePredictionsTab(PredictiveAnalyticsController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // فلاتر
          Row(
            children: [
              Expanded(
                child: Obx(
                  () => CheckboxListTile(
                    title: const Text('مخاطر عالية فقط'),
                    value: controller.showOnlyHighRisk.value,
                    onChanged: (_) => controller.toggleHighRiskFilter(),
                  ),
                ),
              ),
              ElevatedButton(
                onPressed: controller.clearAllFilters,
                child: const Text('مسح الفلاتر'),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // قائمة التنبؤات
          Expanded(
            child: Obx(() {
              final predictions = controller.filteredFailurePredictions;
              if (predictions.isEmpty) {
                return const Center(child: Text('لا توجد تنبؤات بالأعطال'));
              }

              return ListView.builder(
                itemCount: predictions.length,
                itemBuilder: (context, index) {
                  final prediction = predictions[index];
                  return _buildFailurePredictionCard(prediction, controller);
                },
              );
            }),
          ),
        ],
      ),
    );
  }

  /// بطاقة تنبؤ العطل
  Widget _buildFailurePredictionCard(
    FailurePrediction prediction,
    PredictiveAnalyticsController controller,
  ) {
    final riskColor = _getRiskColor(prediction.failureProbability);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: riskColor,
          child: Text(
            '${prediction.failureProbability.toStringAsFixed(0)}%',
            style: const TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
        title: Text(prediction.machineName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'نوع العطل المتوقع: ${prediction.predictedFailureType.displayName}',
            ),
            Text('خلال ${prediction.daysUntilFailure} أيام'),
            Text(
              'مستوى الثقة: ${prediction.confidenceLevel.toStringAsFixed(1)}%',
            ),
          ],
        ),
        trailing: Icon(
          _getRiskIcon(prediction.failureProbability),
          color: riskColor,
        ),
        onTap: () => controller.selectFailurePrediction(prediction),
      ),
    );
  }

  /// تبويب تحليل الاتجاهات
  Widget _buildTrendAnalysisTab(PredictiveAnalyticsController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() {
        final trends = controller.trendAnalyses;
        if (trends.isEmpty) {
          return const Center(child: Text('لا توجد تحليلات اتجاهات'));
        }

        return ListView.builder(
          itemCount: trends.length,
          itemBuilder: (context, index) {
            final trend = trends[index];
            return _buildTrendAnalysisCard(trend, controller);
          },
        );
      }),
    );
  }

  /// بطاقة تحليل الاتجاه
  Widget _buildTrendAnalysisCard(
    TrendAnalysis trend,
    PredictiveAnalyticsController controller,
  ) {
    final directionColor = _getTrendDirectionColor(trend.direction);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getTrendDirectionIcon(trend.direction),
          color: directionColor,
          size: 32,
        ),
        title: Text(trend.metricName),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('الاتجاه: ${trend.direction.displayName}'),
            Text('قوة الاتجاه: ${trend.trendStrength.toStringAsFixed(1)}%'),
            Text('معدل التغيير: ${trend.changeRate.toStringAsFixed(2)}'),
          ],
        ),
        trailing: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: directionColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            '${trend.trendStrength.toStringAsFixed(0)}%',
            style: TextStyle(
              color: directionColor,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        onTap: () => controller.selectTrendAnalysis(trend),
      ),
    );
  }

  /// تبويب التوقعات المستقبلية
  Widget _buildForecastsTab(PredictiveAnalyticsController controller) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() {
        final forecasts = controller.futureForecasts;
        if (forecasts.isEmpty) {
          return const Center(child: Text('لا توجد توقعات مستقبلية'));
        }

        return ListView.builder(
          itemCount: forecasts.length,
          itemBuilder: (context, index) {
            final forecast = forecasts[index];
            return _buildForecastCard(forecast, controller);
          },
        );
      }),
    );
  }

  /// بطاقة التوقع المستقبلي
  Widget _buildForecastCard(
    FutureForecast forecast,
    PredictiveAnalyticsController controller,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ExpansionTile(
        leading: Icon(_getForecastTypeIcon(forecast.type), color: Colors.blue),
        title: Text(forecast.title),
        subtitle: Text('دقة التوقع: ${forecast.accuracy.toStringAsFixed(1)}%'),
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(forecast.description),
                const SizedBox(height: 12),
                const Text(
                  'التوقعات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...forecast.predictions.entries.map(
                  (entry) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(entry.key),
                        Text(
                          entry.value.toStringAsFixed(1),
                          style: const TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                const Text(
                  'التوصيات:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                ...forecast.recommendations.map(
                  (rec) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        const Icon(Icons.arrow_right, size: 16),
                        Expanded(child: Text(rec)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// تبويب التنبيهات التنبؤية
  Widget _buildPredictiveNotificationsTab(
    PredictiveAnalyticsController controller,
  ) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Obx(() {
        final notifications = controller.filteredPredictiveNotifications;
        if (notifications.isEmpty) {
          return const Center(child: Text('لا توجد تنبيهات تنبؤية'));
        }

        return ListView.builder(
          itemCount: notifications.length,
          itemBuilder: (context, index) {
            final notification = notifications[index];
            return _buildPredictiveNotificationCard(notification, controller);
          },
        );
      }),
    );
  }

  /// بطاقة التنبيه التنبؤي
  Widget _buildPredictiveNotificationCard(
    PredictiveNotification notification,
    PredictiveAnalyticsController controller,
  ) {
    final severityColor = _getSeverityColor(notification.severity);

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          _getSeverityIcon(notification.severity),
          color: severityColor,
        ),
        title: Text(
          notification.title,
          style: TextStyle(
            fontWeight:
                notification.isRead ? FontWeight.normal : FontWeight.bold,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(notification.message),
            Text('النوع: ${notification.type.displayName}'),
            Text('الإجراء المطلوب: ${notification.actionRequired}'),
          ],
        ),
        trailing: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (!notification.isRead)
              Container(
                width: 8,
                height: 8,
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
              ),
            if (notification.isActionTaken)
              const Icon(Icons.check_circle, color: Colors.green, size: 16),
          ],
        ),
        onTap: () {
          if (!notification.isRead) {
            controller.markNotificationAsRead(notification.id);
          }
        },
      ),
    );
  }

  /// نافذة الإعدادات
  void _showSettingsDialog(PredictiveAnalyticsController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إعدادات التحليل التنبؤي'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Obx(
              () => ListTile(
                title: const Text('أيام التوقع'),
                subtitle: Slider(
                  value: controller.forecastDays.value.toDouble(),
                  min: 7,
                  max: 90,
                  divisions: 11,
                  label: '${controller.forecastDays.value} أيام',
                  onChanged:
                      (value) => controller.forecastDays.value = value.round(),
                ),
              ),
            ),
            Obx(
              () => ListTile(
                title: const Text('نافذة التحليل'),
                subtitle: Slider(
                  value: controller.analysisWindow.value.toDouble(),
                  min: 7,
                  max: 180,
                  divisions: 24,
                  label: '${controller.analysisWindow.value} أيام',
                  onChanged:
                      (value) =>
                          controller.analysisWindow.value = value.round(),
                ),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(onPressed: () => Get.back(), child: const Text('إلغاء')),
          ElevatedButton(
            onPressed: () {
              controller.updateAnalysisSettings(
                newForecastDays: controller.forecastDays.value,
                newAnalysisWindow: controller.analysisWindow.value,
              );
              Get.back();
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  // طرق مساعدة للألوان والأيقونات
  Color _getRiskColor(double probability) {
    if (probability > 80) return Colors.red;
    if (probability > 60) return Colors.orange;
    if (probability > 40) return Colors.yellow;
    return Colors.green;
  }

  IconData _getRiskIcon(double probability) {
    if (probability > 80) return Icons.dangerous;
    if (probability > 60) return Icons.warning;
    if (probability > 40) return Icons.info;
    return Icons.check_circle;
  }

  Color _getTrendDirectionColor(TrendDirection direction) {
    switch (direction) {
      case TrendDirection.increasing:
        return Colors.green;
      case TrendDirection.decreasing:
        return Colors.red;
      case TrendDirection.stable:
        return Colors.blue;
      case TrendDirection.volatile:
        return Colors.orange;
    }
  }

  IconData _getTrendDirectionIcon(TrendDirection direction) {
    switch (direction) {
      case TrendDirection.increasing:
        return Icons.trending_up;
      case TrendDirection.decreasing:
        return Icons.trending_down;
      case TrendDirection.stable:
        return Icons.trending_flat;
      case TrendDirection.volatile:
        return Icons.show_chart;
    }
  }

  IconData _getForecastTypeIcon(ForecastType type) {
    switch (type) {
      case ForecastType.production:
        return Icons.factory;
      case ForecastType.maintenance:
        return Icons.build;
      case ForecastType.financial:
        return Icons.attach_money;
      case ForecastType.quality:
        return Icons.verified;
      case ForecastType.efficiency:
        return Icons.speed;
      case ForecastType.demand:
        return Icons.trending_up;
    }
  }

  Color _getSeverityColor(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return Colors.red;
      case NotificationSeverity.warning:
        return Colors.orange;
      case NotificationSeverity.info:
        return Colors.blue;
      case NotificationSeverity.success:
        return Colors.green;
    }
  }

  IconData _getSeverityIcon(NotificationSeverity severity) {
    switch (severity) {
      case NotificationSeverity.critical:
        return Icons.error;
      case NotificationSeverity.warning:
        return Icons.warning;
      case NotificationSeverity.info:
        return Icons.info;
      case NotificationSeverity.success:
        return Icons.check_circle;
    }
  }
}
