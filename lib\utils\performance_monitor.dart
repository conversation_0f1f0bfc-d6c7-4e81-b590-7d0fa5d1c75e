import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:sqflite/sqflite.dart';
import 'package:mostafa_final/data/datasources/database_helper.dart';

/// أداة مراقبة أداء التطبيق
class PerformanceMonitor {
  static final PerformanceMonitor _instance = PerformanceMonitor._internal();
  factory PerformanceMonitor() => _instance;
  PerformanceMonitor._internal();

  final Logger _logger = Logger();
  final Stopwatch _stopwatch = Stopwatch();
  final Map<String, List<double>> _responseTimes = {};
  final Map<String, List<double>> _databaseQueryTimes = {};
  final Map<String, int> _frameDrops = {};

  bool _isMonitoring = false;
  StreamSubscription<FrameTiming>? _frameTimingSubscription;

  /// بدء مراقبة الأداء
  void startMonitoring() {
    if (_isMonitoring) return;

    _isMonitoring = true;
    _logger.i('بدء مراقبة الأداء');

    // مراقبة إطارات الرسم
    // تم تعطيل هذا الكود مؤقتًا لأنه يسبب مشاكل في الاختبارات
    // _frameTimingSubscription = window.onReportTimings.listen(_onFrameTimingReport);
  }

  /// إيقاف مراقبة الأداء
  void stopMonitoring() {
    if (!_isMonitoring) return;

    _isMonitoring = false;
    _frameTimingSubscription?.cancel();
    _logger.i('تم إيقاف مراقبة الأداء');
  }

  // تم حذف دالة _onFrameTimingReport لأنها غير مستخدمة

  // تم حذف دالة _getCurrentRouteName لأنها غير مستخدمة

  /// قياس زمن استجابة واجهة المستخدم
  Future<double> measureUIResponseTime(
    Future<void> Function() action,
    String screenName,
  ) async {
    _stopwatch.reset();
    _stopwatch.start();

    await action();

    _stopwatch.stop();
    final responseTime = _stopwatch.elapsedMilliseconds.toDouble();

    // تخزين زمن الاستجابة
    _responseTimes[screenName] = _responseTimes[screenName] ?? [];
    _responseTimes[screenName]!.add(responseTime);

    _logger.i('زمن استجابة $screenName: $responseTime ms');
    return responseTime;
  }

  /// قياس أداء استعلام قاعدة البيانات
  Future<T> measureDatabaseQuery<T>(
    Future<T> Function() query,
    String queryName,
  ) async {
    _stopwatch.reset();
    _stopwatch.start();

    final result = await query();

    _stopwatch.stop();
    final queryTime = _stopwatch.elapsedMilliseconds.toDouble();

    // تخزين زمن الاستعلام
    _databaseQueryTimes[queryName] = _databaseQueryTimes[queryName] ?? [];
    _databaseQueryTimes[queryName]!.add(queryTime);

    _logger.i('زمن استعلام $queryName: $queryTime ms');
    return result;
  }

  /// قياس استهلاك الذاكرة
  Future<int> measureMemoryUsage() async {
    if (kIsWeb) {
      _logger.w('قياس استهلاك الذاكرة غير متاح في بيئة الويب');
      return 0;
    }

    // استخدام DevTools للحصول على معلومات الذاكرة
    // هذه طريقة تقريبية، يمكن استخدام أدوات أكثر دقة
    final memoryInfo = await _getMemoryInfo();

    _logger.i('استهلاك الذاكرة: $memoryInfo KB');
    return memoryInfo;
  }

  /// الحصول على معلومات الذاكرة
  Future<int> _getMemoryInfo() async {
    try {
      // هذه طريقة تقريبية، تختلف حسب النظام
      if (Platform.isAndroid || Platform.isIOS) {
        // استخدام DevTools للحصول على معلومات الذاكرة
        return 0; // يجب استبدالها بطريقة فعلية لقياس الذاكرة
      }
      return 0;
    } catch (e) {
      _logger.e('خطأ في قياس استهلاك الذاكرة: $e');
      return 0;
    }
  }

  /// اختبار أداء قاعدة البيانات تحت ضغط عالٍ
  Future<Map<String, dynamic>> testDatabasePerformance() async {
    final results = <String, dynamic>{};
    final db = await DatabaseHelper.instance.database;

    // اختبار استعلام بسيط
    results['simple_query'] = await _testSimpleQuery(db);

    // اختبار استعلام معقد
    results['complex_query'] = await _testComplexQuery(db);

    // اختبار إدراج متعدد
    results['bulk_insert'] = await _testBulkInsert(db);

    return results;
  }

  /// اختبار استعلام بسيط
  Future<double> _testSimpleQuery(Database db) async {
    _stopwatch.reset();
    _stopwatch.start();

    // تنفيذ استعلام بسيط 100 مرة
    for (int i = 0; i < 100; i++) {
      await db.query(DatabaseHelper.tableRawMaterials, limit: 10);
    }

    _stopwatch.stop();
    return _stopwatch.elapsedMilliseconds / 100.0; // متوسط الزمن
  }

  /// اختبار استعلام معقد
  Future<double> _testComplexQuery(Database db) async {
    _stopwatch.reset();
    _stopwatch.start();

    // تنفيذ استعلام معقد 20 مرة
    for (int i = 0; i < 20; i++) {
      await db.rawQuery('''
        SELECT p.*, m.name as machineName, mold.name as moldName
        FROM ${DatabaseHelper.tableProduction} p
        JOIN ${DatabaseHelper.tableMachines} m ON p.machineId = m.id
        JOIN ${DatabaseHelper.tableMolds} mold ON p.moldId = mold.id
        LIMIT 50
      ''');
    }

    _stopwatch.stop();
    return _stopwatch.elapsedMilliseconds / 20.0; // متوسط الزمن
  }

  /// اختبار إدراج متعدد
  Future<double> _testBulkInsert(Database db) async {
    _stopwatch.reset();
    _stopwatch.start();

    // إنشاء بيانات اختبار
    final testData = List.generate(
      100,
      (index) => {
        'id': 'test_$index',
        'name': 'Test Item $index',
        'value': index,
        'timestamp': DateTime.now().toIso8601String(),
      },
    );

    // إنشاء جدول اختبار مؤقت
    await db.execute('''
      CREATE TABLE IF NOT EXISTS performance_test (
        id TEXT PRIMARY KEY,
        name TEXT,
        value INTEGER,
        timestamp TEXT
      )
    ''');

    // إدراج البيانات
    await db.transaction((txn) async {
      for (final item in testData) {
        await txn.insert('performance_test', item);
      }
    });

    // حذف جدول الاختبار
    await db.execute('DROP TABLE IF EXISTS performance_test');

    _stopwatch.stop();
    return _stopwatch.elapsedMilliseconds.toDouble();
  }

  /// إنشاء تقرير أداء مفصل
  Future<String> generatePerformanceReport() async {
    final report = StringBuffer();
    final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
    final now = DateTime.now();

    report.writeln('# تقرير أداء التطبيق');
    report.writeln('تاريخ التقرير: ${dateFormat.format(now)}');
    report.writeln('\n## زمن استجابة واجهة المستخدم');

    _responseTimes.forEach((screen, times) {
      if (times.isNotEmpty) {
        final avg = times.reduce((a, b) => a + b) / times.length;
        final max = times.reduce((a, b) => a > b ? a : b);
        final min = times.reduce((a, b) => a < b ? a : b);

        report.writeln('### $screen');
        report.writeln('- متوسط زمن الاستجابة: ${avg.toStringAsFixed(2)} ms');
        report.writeln('- أقصى زمن استجابة: ${max.toStringAsFixed(2)} ms');
        report.writeln('- أدنى زمن استجابة: ${min.toStringAsFixed(2)} ms');
      }
    });

    report.writeln('\n## أداء قاعدة البيانات');

    _databaseQueryTimes.forEach((query, times) {
      if (times.isNotEmpty) {
        final avg = times.reduce((a, b) => a + b) / times.length;
        final max = times.reduce((a, b) => a > b ? a : b);
        final min = times.reduce((a, b) => a < b ? a : b);

        report.writeln('### $query');
        report.writeln('- متوسط زمن الاستعلام: ${avg.toStringAsFixed(2)} ms');
        report.writeln('- أقصى زمن استعلام: ${max.toStringAsFixed(2)} ms');
        report.writeln('- أدنى زمن استعلام: ${min.toStringAsFixed(2)} ms');
      }
    });

    report.writeln('\n## الإطارات المتأخرة');

    _frameDrops.forEach((screen, count) {
      report.writeln('- $screen: $count إطار متأخر');
    });

    // حفظ التقرير في ملف
    final reportPath = await _saveReportToFile(report.toString());

    return reportPath;
  }

  /// حفظ التقرير في ملف
  Future<String> _saveReportToFile(String reportContent) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final dateStr = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final filePath = '${directory.path}/performance_report_$dateStr.md';

      final file = File(filePath);
      await file.writeAsString(reportContent);

      _logger.i('تم حفظ تقرير الأداء في: $filePath');
      return filePath;
    } catch (e) {
      _logger.e('خطأ في حفظ تقرير الأداء: $e');
      return '';
    }
  }

  /// إعادة تعيين بيانات المراقبة
  void resetMonitoringData() {
    _responseTimes.clear();
    _databaseQueryTimes.clear();
    _frameDrops.clear();
    _logger.i('تم إعادة تعيين بيانات المراقبة');
  }
}
