import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/order.dart';
import 'package:mostafa_final/presentation/controllers/order_controller.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'package:mostafa_final/utils/format_utils.dart';

class OrdersPage extends StatelessWidget {
  const OrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final controller = Get.put(OrderController());

    return ResponsivePage(
      title: 'إدارة الطلبيات والاستيراد',
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => controller.fetchOrders(),
        ),
        IconButton(
          icon: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => _showCreateOrderDialog(context, controller),
        ),
      ],
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CustomLoadingIndicator(
              size: ScreenSize.isSmallScreen ? 40 : 50,
            ),
          );
        }

        if (controller.orders.isEmpty) {
          return Center(
            child: TextUtils.responsiveText(
              'لا توجد طلبيات مسجلة بعد',
              style: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                color: Colors.grey.shade600,
              ),
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
          );
        }

        return Padding(
          padding: EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextUtils.responsiveText(
                'قائمة الطلبيات',
                style: TextStyle(
                  fontSize: ScreenSize.isSmallScreen ? 18 : 20,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
              ),
              SizedBox(height: ScreenSize.getPadding(16)),
              Expanded(
                child: ListView.builder(
                  itemCount: controller.orders.length,
                  itemBuilder: (context, index) {
                    final order = controller.orders[index];
                    return Card(
                      margin: EdgeInsets.only(
                        bottom: ScreenSize.getPadding(16),
                      ),
                      child: Padding(
                        padding: EdgeInsets.all(ScreenSize.getPadding(12)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: TextUtils.responsiveText(
                                    'طلبية بتاريخ ${DateFormat('yyyy-MM-dd').format(order.date)}',
                                    style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize:
                                          ScreenSize.isSmallScreen ? 14 : 16,
                                    ),
                                    maxLines: 1,
                                  ),
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(
                                        Icons.edit,
                                        color: Colors.blue,
                                        size:
                                            ScreenSize.isSmallScreen ? 20 : 24,
                                      ),
                                      constraints: BoxConstraints(
                                        minWidth: ScreenSize.getPadding(36),
                                        minHeight: ScreenSize.getPadding(36),
                                      ),
                                      padding: EdgeInsets.all(
                                        ScreenSize.getPadding(4),
                                      ),
                                      onPressed: () {
                                        controller.loadOrderForEdit(order);
                                        _showCreateOrderDialog(
                                          context,
                                          controller,
                                          isEdit: true,
                                        );
                                      },
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                        size:
                                            ScreenSize.isSmallScreen ? 20 : 24,
                                      ),
                                      constraints: BoxConstraints(
                                        minWidth: ScreenSize.getPadding(36),
                                        minHeight: ScreenSize.getPadding(36),
                                      ),
                                      padding: EdgeInsets.all(
                                        ScreenSize.getPadding(4),
                                      ),
                                      onPressed:
                                          () => _showDeleteConfirmation(
                                            context,
                                            controller,
                                            order,
                                          ),
                                    ),
                                    IconButton(
                                      icon: Icon(
                                        Icons.visibility,
                                        color: Colors.green,
                                        size:
                                            ScreenSize.isSmallScreen ? 20 : 24,
                                      ),
                                      constraints: BoxConstraints(
                                        minWidth: ScreenSize.getPadding(36),
                                        minHeight: ScreenSize.getPadding(36),
                                      ),
                                      padding: EdgeInsets.all(
                                        ScreenSize.getPadding(4),
                                      ),
                                      onPressed: () {
                                        controller.selectOrder(order);
                                        _showOrderDetailsDialog(
                                          context,
                                          controller,
                                        );
                                      },
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            SizedBox(height: ScreenSize.getPadding(8)),
                            TextUtils.responsiveText(
                              'العملة: ${order.currency} | سعر الصرف: ${order.exchangeRate}',
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                color: Colors.grey.shade700,
                              ),
                              maxLines: 1,
                            ),
                            SizedBox(height: ScreenSize.getPadding(4)),
                            TextUtils.responsiveText(
                              'عدد العناصر: ${order.items.length} | التكلفة الإجمالية: ${FormatUtils.formatCurrency(order.totalOrderCost * order.exchangeRate)} ج.م',
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                color: Colors.grey.shade700,
                              ),
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  // عرض تفاصيل الطلبية
  void _showOrderDetailsDialog(
    BuildContext context,
    OrderController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final order = controller.selectedOrder.value!;

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.8,
              padding: EdgeInsets.all(ScreenSize.getPadding(16)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      TextUtils.responsiveText(
                        'تفاصيل الطلبية',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 18 : 20,
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 1,
                      ),
                      IconButton(
                        icon: Icon(
                          Icons.close,
                          size: ScreenSize.isSmallScreen ? 20 : 24,
                        ),
                        onPressed: () => Navigator.of(context).pop(),
                        constraints: BoxConstraints(
                          minWidth: ScreenSize.getPadding(36),
                          minHeight: ScreenSize.getPadding(36),
                        ),
                        padding: EdgeInsets.all(ScreenSize.getPadding(4)),
                      ),
                    ],
                  ),
                  Divider(height: ScreenSize.getPadding(1)),
                  SizedBox(height: ScreenSize.getPadding(8)),
                  _buildOrderDetailRow(
                    'تاريخ الطلبية:',
                    DateFormat('yyyy-MM-dd').format(order.date),
                  ),
                  _buildOrderDetailRow('العملة:', order.currency),
                  _buildOrderDetailRow(
                    'سعر الصرف:',
                    order.exchangeRate.toString(),
                  ),
                  _buildOrderDetailRow(
                    'مصاريف النقل:',
                    '${FormatUtils.formatCurrency(order.transportCost)} ${order.currency}',
                  ),
                  _buildOrderDetailRow(
                    'مصاريف الشحن:',
                    '${FormatUtils.formatCurrency(order.shippingCost)} ${order.currency}',
                  ),
                  _buildOrderDetailRow(
                    'مصاريف التخليص:',
                    '${FormatUtils.formatCurrency(order.clearanceCost)} ${order.currency}',
                  ),
                  _buildOrderDetailRow(
                    'الجمارك:',
                    '${FormatUtils.formatCurrency(order.customsDuty)} ${order.currency}',
                  ),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  TextUtils.responsiveText(
                    'عناصر الطلبية:',
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                  ),
                  SizedBox(height: ScreenSize.getPadding(8)),
                  SizedBox(
                    height: ScreenSize.isSmallScreen ? 150 : 200,
                    child: ListView.builder(
                      itemCount: order.items.length,
                      itemBuilder: (context, index) {
                        final item = order.items[index];
                        return Card(
                          margin: EdgeInsets.only(
                            bottom: ScreenSize.getPadding(8),
                          ),
                          child: Padding(
                            padding: EdgeInsets.all(ScreenSize.getPadding(12)),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                TextUtils.responsiveText(
                                  item.name,
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 14 : 16,
                                  ),
                                  maxLines: 1,
                                ),
                                SizedBox(height: ScreenSize.getPadding(4)),
                                TextUtils.responsiveText(
                                  'الكمية: ${item.quantity}',
                                  style: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                    color: Colors.grey.shade700,
                                  ),
                                  maxLines: 1,
                                ),
                                TextUtils.responsiveText(
                                  'السعر: ${FormatUtils.formatCurrency(item.price)} ${order.currency}',
                                  style: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                    color: Colors.grey.shade700,
                                  ),
                                  maxLines: 1,
                                ),
                                TextUtils.responsiveText(
                                  'الوزن: ${item.weight} كجم',
                                  style: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                    color: Colors.grey.shade700,
                                  ),
                                  maxLines: 1,
                                ),
                                FutureBuilder<double>(
                                  future: controller.calculateItemEgpCost(
                                    order.id!,
                                    item.id!,
                                  ),
                                  builder: (context, snapshot) {
                                    if (snapshot.connectionState ==
                                        ConnectionState.waiting) {
                                      return TextUtils.responsiveText(
                                        'جاري حساب التكلفة...',
                                        style: TextStyle(
                                          fontSize:
                                              ScreenSize.isSmallScreen
                                                  ? 12
                                                  : 14,
                                          color: Colors.grey.shade700,
                                        ),
                                        maxLines: 1,
                                      );
                                    }
                                    if (snapshot.hasError) {
                                      return TextUtils.responsiveText(
                                        'حدث خطأ في حساب التكلفة',
                                        style: TextStyle(
                                          fontSize:
                                              ScreenSize.isSmallScreen
                                                  ? 12
                                                  : 14,
                                          color: Colors.red,
                                        ),
                                        maxLines: 1,
                                      );
                                    }
                                    return TextUtils.responsiveText(
                                      'التكلفة بالجنيه المصري: ${FormatUtils.formatCurrency(snapshot.data ?? 0)} ج.م',
                                      style: TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize:
                                            ScreenSize.isSmallScreen ? 12 : 14,
                                      ),
                                      maxLines: 1,
                                    );
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                  SizedBox(height: ScreenSize.getPadding(16)),
                  _buildTotalCostRow(
                    'إجمالي التكلفة:',
                    '${FormatUtils.formatCurrency(order.totalOrderCost)} ${order.currency}',
                  ),
                  SizedBox(height: ScreenSize.getPadding(4)),
                  _buildTotalCostRow(
                    'إجمالي التكلفة بالجنيه المصري:',
                    '${FormatUtils.formatCurrency(order.totalOrderCost * order.exchangeRate)} ج.م',
                  ),
                ],
              ),
            ),
          ),
    );
  }

  // بناء صف تفاصيل الطلبية
  Widget _buildOrderDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(4)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TextUtils.responsiveText(
            label,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
            ),
            maxLines: 1,
          ),
          SizedBox(width: ScreenSize.getPadding(4)),
          Expanded(
            child: TextUtils.responsiveText(
              value,
              style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
              maxLines: 1,
            ),
          ),
        ],
      ),
    );
  }

  // بناء صف إجمالي التكلفة
  Widget _buildTotalCostRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        TextUtils.responsiveText(
          label,
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
        ),
        TextUtils.responsiveText(
          value,
          style: TextStyle(
            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
            fontWeight: FontWeight.bold,
          ),
          maxLines: 1,
        ),
      ],
    );
  }

  // عرض مربع حوار إنشاء طلبية جديدة
  void _showCreateOrderDialog(
    BuildContext context,
    OrderController controller, {
    bool isEdit = false,
  }) {
    if (!isEdit) {
      controller.resetForm();
    }

    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: Container(
              width: MediaQuery.of(context).size.width * 0.9,
              height: MediaQuery.of(context).size.height * 0.9,
              padding: const EdgeInsets.all(16),
              child: Obx(() {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          isEdit ? 'تعديل الطلبية' : 'إنشاء طلبية جديدة',
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                    const Divider(),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // تاريخ الطلبية
                            const Text('تاريخ الطلبية:'),
                            InkWell(
                              onTap: () async {
                                final selectedDate = await showDatePicker(
                                  context: context,
                                  initialDate: controller.orderDate.value,
                                  firstDate: DateTime(2020),
                                  lastDate: DateTime(2030),
                                );
                                if (selectedDate != null) {
                                  controller.orderDate.value = selectedDate;
                                }
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 8,
                                  horizontal: 12,
                                ),
                                decoration: BoxDecoration(
                                  border: Border.all(color: Colors.grey),
                                  borderRadius: BorderRadius.circular(4),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      DateFormat(
                                        'yyyy-MM-dd',
                                      ).format(controller.orderDate.value),
                                    ),
                                    const Icon(Icons.calendar_today),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // العملة وسعر الصرف
                            Row(
                              children: [
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('العملة:'),
                                      DropdownButtonFormField<String>(
                                        value: controller.currency.value,
                                        items:
                                            controller.availableCurrencies
                                                .map(
                                                  (currency) =>
                                                      DropdownMenuItem(
                                                        value: currency,
                                                        child: Text(currency),
                                                      ),
                                                )
                                                .toList(),
                                        onChanged: (value) {
                                          if (value != null) {
                                            controller.currency.value = value;
                                          }
                                        },
                                        decoration: const InputDecoration(
                                          border: OutlineInputBorder(),
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text('سعر الصرف:'),
                                      TextField(
                                        keyboardType:
                                            const TextInputType.numberWithOptions(
                                              decimal: true,
                                            ),
                                        decoration: const InputDecoration(
                                          border: OutlineInputBorder(),
                                          hintText: 'أدخل سعر الصرف',
                                          contentPadding: EdgeInsets.symmetric(
                                            horizontal: 12,
                                            vertical: 8,
                                          ),
                                        ),
                                        controller: TextEditingController(
                                          text:
                                              controller.exchangeRate.value
                                                  .toString(),
                                        )..addListener(() {
                                          final value = double.tryParse(
                                            TextEditingController().text,
                                          );
                                          if (value != null) {
                                            controller.exchangeRate.value =
                                                value;
                                          }
                                        }),
                                        onChanged: (value) {
                                          final parsedValue = double.tryParse(
                                            value,
                                          );
                                          if (parsedValue != null) {
                                            controller.exchangeRate.value =
                                                parsedValue;
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // مصاريف الطلبية
                            const Text('مصاريف الطلبية:'),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    decoration: const InputDecoration(
                                      labelText: 'مصاريف النقل',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    controller: TextEditingController(
                                      text:
                                          controller.transportCost.value
                                              .toString(),
                                    ),
                                    onChanged: (value) {
                                      final parsedValue = double.tryParse(
                                        value,
                                      );
                                      if (parsedValue != null) {
                                        controller.transportCost.value =
                                            parsedValue;
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    decoration: const InputDecoration(
                                      labelText: 'مصاريف الشحن',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    controller: TextEditingController(
                                      text:
                                          controller.shippingCost.value
                                              .toString(),
                                    ),
                                    onChanged: (value) {
                                      final parsedValue = double.tryParse(
                                        value,
                                      );
                                      if (parsedValue != null) {
                                        controller.shippingCost.value =
                                            parsedValue;
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    decoration: const InputDecoration(
                                      labelText: 'مصاريف التخليص',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    controller: TextEditingController(
                                      text:
                                          controller.clearanceCost.value
                                              .toString(),
                                    ),
                                    onChanged: (value) {
                                      final parsedValue = double.tryParse(
                                        value,
                                      );
                                      if (parsedValue != null) {
                                        controller.clearanceCost.value =
                                            parsedValue;
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextField(
                                    decoration: const InputDecoration(
                                      labelText: 'الجمارك',
                                      border: OutlineInputBorder(),
                                    ),
                                    keyboardType:
                                        const TextInputType.numberWithOptions(
                                          decimal: true,
                                        ),
                                    controller: TextEditingController(
                                      text:
                                          controller.customsDuty.value
                                              .toString(),
                                    ),
                                    onChanged: (value) {
                                      final parsedValue = double.tryParse(
                                        value,
                                      );
                                      if (parsedValue != null) {
                                        controller.customsDuty.value =
                                            parsedValue;
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 24),

                            // إضافة عناصر الطلبية
                            const Text(
                              'عناصر الطلبية:',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),

                            // نموذج إضافة عنصر جديد
                            Card(
                              child: Padding(
                                padding: const EdgeInsets.all(16.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text('إضافة عنصر جديد:'),
                                    const SizedBox(height: 8),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'معرف الإكسسوار',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged:
                                          (value) =>
                                              controller
                                                  .newItemAccessoryId
                                                  .value = value,
                                    ),
                                    const SizedBox(height: 8),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'اسم العنصر',
                                        border: OutlineInputBorder(),
                                      ),
                                      onChanged:
                                          (value) =>
                                              controller.newItemName.value =
                                                  value,
                                    ),
                                    const SizedBox(height: 8),
                                    Row(
                                      children: [
                                        Expanded(
                                          child: TextField(
                                            decoration: const InputDecoration(
                                              labelText: 'السعر',
                                              border: OutlineInputBorder(),
                                            ),
                                            keyboardType:
                                                const TextInputType.numberWithOptions(
                                                  decimal: true,
                                                ),
                                            onChanged: (value) {
                                              final parsedValue =
                                                  double.tryParse(value);
                                              if (parsedValue != null) {
                                                controller.newItemPrice.value =
                                                    parsedValue;
                                              }
                                            },
                                          ),
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: TextField(
                                            decoration: const InputDecoration(
                                              labelText: 'الكمية',
                                              border: OutlineInputBorder(),
                                            ),
                                            keyboardType: TextInputType.number,
                                            onChanged: (value) {
                                              final parsedValue = int.tryParse(
                                                value,
                                              );
                                              if (parsedValue != null) {
                                                controller
                                                    .newItemQuantity
                                                    .value = parsedValue;
                                              }
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 8),
                                    TextField(
                                      decoration: const InputDecoration(
                                        labelText: 'الوزن (كجم)',
                                        border: OutlineInputBorder(),
                                      ),
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                            decimal: true,
                                          ),
                                      onChanged: (value) {
                                        final parsedValue = double.tryParse(
                                          value,
                                        );
                                        if (parsedValue != null) {
                                          controller.newItemWeight.value =
                                              parsedValue;
                                        }
                                      },
                                    ),
                                    const SizedBox(height: 16),
                                    ElevatedButton.icon(
                                      onPressed: () {
                                        controller.addItemToOrder();
                                      },
                                      icon: const Icon(Icons.add),
                                      label: const Text('إضافة العنصر'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green,
                                        foregroundColor: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            const SizedBox(height: 16),

                            // قائمة العناصر المضافة
                            if (controller.orderItems.isNotEmpty) ...[
                              const Text('العناصر المضافة:'),
                              const SizedBox(height: 8),
                              ListView.builder(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: controller.orderItems.length,
                                itemBuilder: (context, index) {
                                  final item = controller.orderItems[index];
                                  return Card(
                                    child: ListTile(
                                      title: Text(item.name),
                                      subtitle: Text(
                                        'الكمية: ${item.quantity} | السعر: ${FormatUtils.formatCurrency(item.price)} ${controller.currency.value} | الوزن: ${item.weight} كجم',
                                      ),
                                      trailing: IconButton(
                                        icon: const Icon(
                                          Icons.delete,
                                          color: Colors.red,
                                        ),
                                        onPressed:
                                            () => controller
                                                .removeItemFromOrder(index),
                                      ),
                                    ),
                                  );
                                },
                              ),
                              const SizedBox(height: 16),
                              // إجمالي التكلفة
                              Card(
                                child: Padding(
                                  padding: const EdgeInsets.all(16.0),
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text('إجمالي تكلفة العناصر:'),
                                          Text(
                                            '${FormatUtils.formatCurrency(controller.orderItems.fold(0, (sum, item) => sum + (item.price * item.quantity)))} ${controller.currency.value}',
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            'إجمالي التكلفة بالعملة الأجنبية:',
                                          ),
                                          Text(
                                            '${FormatUtils.formatCurrency(controller.calculateTotalForeignCost())} ${controller.currency.value}',
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 8),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            'إجمالي التكلفة بالجنيه المصري:',
                                            style: TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          Text(
                                            '${FormatUtils.formatCurrency(controller.calculateTotalEgpCost())} ج.م',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: const Text('إلغاء'),
                        ),
                        const SizedBox(width: 16),
                        ElevatedButton(
                          onPressed:
                              controller.isSaving.value
                                  ? null
                                  : () async {
                                    if (isEdit) {
                                      await controller.updateOrder();
                                    } else {
                                      await controller.saveOrder();
                                    }
                                    if (context.mounted) {
                                      Navigator.of(context).pop();
                                    }
                                  },
                          child:
                              controller.isSaving.value
                                  ? const CircularProgressIndicator()
                                  : Text(
                                    isEdit ? 'تحديث الطلبية' : 'حفظ الطلبية',
                                  ),
                        ),
                      ],
                    ),
                  ],
                );
              }),
            ),
          ),
    );
  }

  // تأكيد حذف الطلبية
  void _showDeleteConfirmation(
    BuildContext context,
    OrderController controller,
    Order order,
  ) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('تأكيد الحذف'),
            content: const Text(
              'هل أنت متأكد من رغبتك في حذف هذه الطلبية؟ لا يمكن التراجع عن هذا الإجراء.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  controller.deleteOrder(order.id!);
                  Navigator.of(context).pop();
                },
                child: const Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}
