import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/controllers/injection_cost_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class InjectionCostPage extends StatelessWidget {
  const InjectionCostPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(InjectionCostController());

    return Scaffold(
      appBar: AppBar(title: const Text('حساب تكلفة الحقن'), centerTitle: true),
      drawer: const CustomDrawer(),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: 'جاري تحميل البيانات...');
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(),
              const SizedBox(height: 20),
              _buildSelectionSection(controller),
              const SizedBox(height: 20),
              _buildCycleInfoSection(controller),
              const SizedBox(height: 20),
              _buildCostInputsSection(controller),
              const SizedBox(height: 30),
              Center(
                child: ElevatedButton.icon(
                  onPressed: controller.calculateCost,
                  icon: const Icon(Icons.calculate),
                  label: const Text('حساب التكلفة'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 30,
                      vertical: 15,
                    ),
                    textStyle: const TextStyle(fontSize: 18),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calculate, color: Colors.blue.shade700, size: 24),
              const SizedBox(width: 10),
              const Text(
                'حساب تكلفة الحقن',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
          const SizedBox(height: 8),
          const Text(
            'هذه الأداة تساعدك على حساب تكلفة حقن القطع البلاستيكية، ومعرفة تفاصيل التكاليف لكل قطعة.',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectionSection(InjectionCostController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اختيار الماكينة والإسطمبة والمادة الخام',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            // اختيار الماكينة
            Obx(
              () => DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الماكينة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.precision_manufacturing),
                ),
                value:
                    controller.selectedMachineId.isEmpty
                        ? null
                        : controller.selectedMachineId.value,
                items:
                    controller.machines.map((machine) {
                      return DropdownMenuItem<String>(
                        value: machine.id,
                        child: Text(
                          '${machine.name} - ${machine.model} (${machine.powerConsumption} كيلوواط)',
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    // استدعاء الدالة بشكل غير متزامن
                    controller.onMachineSelected(value);
                  }
                },
                isExpanded: true,
              ),
            ),
            const SizedBox(height: 16),
            // اختيار الإسطمبة
            Obx(
              () => DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'الإسطمبة',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.settings),
                ),
                value:
                    controller.selectedMoldId.isEmpty
                        ? null
                        : controller.selectedMoldId.value,
                items:
                    controller.molds.map((mold) {
                      return DropdownMenuItem<String>(
                        value: mold.id,
                        child: Text(
                          '${mold.name} - (${mold.cavityCount} تجويف)',
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.onMoldSelected(value);
                  }
                },
                isExpanded: true,
              ),
            ),
            const SizedBox(height: 16),
            // اختيار المادة الخام
            Obx(
              () => DropdownButtonFormField<String>(
                decoration: const InputDecoration(
                  labelText: 'المادة الخام',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category),
                ),
                value:
                    controller.selectedMaterialId.isEmpty
                        ? null
                        : controller.selectedMaterialId.value,
                items:
                    controller.materials.map((material) {
                      return DropdownMenuItem<String>(
                        value: material.id,
                        child: Text(
                          '${material.name} - ${material.color} (${material.costPerKg} جنيه/كجم)',
                        ),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    controller.onMaterialSelected(value);
                  }
                },
                isExpanded: true,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCycleInfoSection(InjectionCostController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات دورة الإنتاج',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'وقت الدورة (ثانية)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.timer),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+\.?\d{0,2}$'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        controller.cycleTimeInSeconds.value =
                            double.tryParse(value) ?? 0;
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'عدد التجاويف',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.grid_view),
                      ),
                      initialValue: controller.cavityCount.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.cavityCount.value =
                              int.tryParse(value) ?? 1;
                        }
                      },
                      enabled: false, // تعطيل الحقل لأنه يتم تحديثه تلقائياً
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'وزن القطعة (جرام)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.scale),
                      ),
                      initialValue: controller.partWeightInGrams.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.partWeightInGrams.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                      enabled: false, // تعطيل الحقل لأنه يتم تحديثه تلقائياً
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'نسبة الهالك (%)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.recycling),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+\.?\d{0,2}$'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        controller.scrapRate.value =
                            double.tryParse(value) ?? 0;
                      }
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            TextFormField(
              decoration: const InputDecoration(
                labelText: 'حجم الدفعة (عدد القطع)',
                border: OutlineInputBorder(),
                prefixIcon: Icon(Icons.inventory),
              ),
              initialValue: controller.batchSize.toString(),
              keyboardType: TextInputType.number,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              onChanged: (value) {
                if (value.isNotEmpty) {
                  controller.batchSize.value = int.tryParse(value) ?? 1000;
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCostInputsSection(InjectionCostController controller) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات التكلفة',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تكلفة الكهرباء (جنيه/ساعة)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.electric_bolt),
                      ),
                      initialValue:
                          controller.electricityCostPerHour.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.electricityCostPerHour.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تكلفة المشغل (جنيه/ساعة)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.person),
                      ),
                      initialValue: controller.operatorCostPerHour.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.operatorCostPerHour.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تكلفة النفقات الإضافية (جنيه/ساعة)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.business),
                      ),
                      initialValue: controller.overheadCostPerHour.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.overheadCostPerHour.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تكلفة الصيانة (جنيه/ساعة)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.build),
                      ),
                      initialValue:
                          controller.maintenanceCostPerHour.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.maintenanceCostPerHour.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => TextFormField(
                      decoration: const InputDecoration(
                        labelText: 'تكلفة المادة الخام (جنيه/كجم)',
                        border: OutlineInputBorder(),
                        prefixIcon: Icon(Icons.category),
                      ),
                      initialValue: controller.materialCostPerKg.toString(),
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(r'^\d+\.?\d{0,2}$'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value.isNotEmpty) {
                          controller.materialCostPerKg.value =
                              double.tryParse(value) ?? 0;
                        }
                      },
                      enabled: false, // تعطيل الحقل لأنه يتم تحديثه تلقائياً
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: const InputDecoration(
                      labelText: 'وقت الإعداد (دقيقة)',
                      border: OutlineInputBorder(),
                      prefixIcon: Icon(Icons.engineering),
                    ),
                    initialValue: controller.setupTimeInMinutes.toString(),
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'^\d+\.?\d{0,2}$'),
                      ),
                    ],
                    onChanged: (value) {
                      if (value.isNotEmpty) {
                        controller.setupTimeInMinutes.value =
                            double.tryParse(value) ?? 0;
                      }
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
