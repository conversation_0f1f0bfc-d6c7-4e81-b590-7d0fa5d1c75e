import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Worker;
import 'package:mostafa_final/presentation/controllers/assembly_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/utils/format_utils.dart';

class AssemblyReportPage extends StatelessWidget {
  const AssemblyReportPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(AssemblyController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('تقارير التجميع'),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () => controller.fetchInitialData(),
          ),
          IconButton(
            icon: const Icon(Icons.date_range),
            onPressed: () => _showDateRangeDialog(context, controller),
          ),
        ],
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CustomLoadingIndicator());
        }

        return SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // ملخص الإحصائيات
              _buildStatisticsCard(controller),

              // رسم بياني للتجميع الأسبوعي
              _buildWeeklyAssemblyChart(controller),

              // أداء العمال
              _buildWorkersPerformanceSection(controller),

              // كفاءة المنتجات
              _buildProductEfficiencySection(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildStatisticsCard(AssemblyController controller) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملخص التجميع',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'إجمالي القطع المجمعة',
                  controller.totalAssembledUnits.toString(),
                  Icons.inventory,
                ),
                _buildStatItem(
                  'عمليات التجميع',
                  controller.totalAssemblyOperations.toString(),
                  Icons.engineering,
                ),
                _buildStatItem(
                  'متوسط التكلفة',
                  FormatUtils.formatCurrency(controller.averageUnitCost.value),
                  Icons.attach_money,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'مجمل التكلفة',
                  FormatUtils.formatCurrency(
                    controller.totalAssemblyCost.value,
                  ),
                  Icons.money,
                ),
                _buildStatItem(
                  'معدل الإنتاج اليومي',
                  (controller.totalAssembledUnits.value / 30).toStringAsFixed(
                    1,
                  ),
                  Icons.trending_up,
                ),
                _buildStatItem('كفاءة التجميع', '85%', Icons.speed),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, size: 30, color: Colors.blue),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildWeeklyAssemblyChart(AssemblyController controller) {
    // لا يمكن جعل Card بأكملها const لأن controller هو متغير
    // ignore: prefer_const_constructors
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      // ignore: prefer_const_constructors
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: const Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'معدل التجميع الأسبوعي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 16),
            SizedBox(
              height: 200,
              child: Placeholder(
                color: Color.fromRGBO(33, 150, 243, 0.5),
                child: Center(
                  child: Text(
                    'سيتم إضافة رسم بياني هنا',
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
              // سيتم استبدال Placeholder بـ BarChart من مكتبة fl_chart
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkersPerformanceSection(AssemblyController controller) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'أداء العمال',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  controller.workers.isNotEmpty
                      ? (controller.workers.length > 5
                          ? 5
                          : controller.workers.length)
                      : 0,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final worker = controller.workers[index];
                // هنا سنقوم بحساب أداء كل عامل بناءً على بيانات فعلية لاحقًا
                final performance = (85 + index * 3) % 100;

                return ListTile(
                  leading: const CircleAvatar(child: Icon(Icons.person)),
                  title: Text(worker.name),
                  subtitle: Text('كود: ${worker.code}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        '$performance%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color:
                              performance > 90
                                  ? Colors.green
                                  : performance > 70
                                  ? Colors.amber
                                  : Colors.red,
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        width: 60,
                        child: LinearProgressIndicator(
                          value: performance / 100,
                          backgroundColor: Colors.grey.shade200,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            performance > 90
                                ? Colors.green
                                : performance > 70
                                ? Colors.amber
                                : Colors.red,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            if (controller.workers.length > 5)
              Center(
                child: TextButton(
                  onPressed: () {
                    // تنقل إلى صفحة تفاصيل أداء العمال
                  },
                  child: const Text('عرض المزيد...'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildProductEfficiencySection(AssemblyController controller) {
    return Card(
      margin: const EdgeInsets.all(16.0),
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'كفاءة التجميع حسب المنتج',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount:
                  controller.products.isNotEmpty
                      ? (controller.products.length > 5
                          ? 5
                          : controller.products.length)
                      : 0,
              separatorBuilder: (context, index) => const Divider(),
              itemBuilder: (context, index) {
                final product = controller.products[index];
                // هنا سنقوم بحساب كفاءة كل منتج بناءً على بيانات فعلية لاحقًا
                final efficiency = (90 - index * 5) % 100;

                return ListTile(
                  leading: const CircleAvatar(child: Icon(Icons.category)),
                  title: Text(product.name),
                  subtitle: Text('كود: ${product.code}'),
                  trailing: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '$efficiency%',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color:
                                  efficiency > 85
                                      ? Colors.green
                                      : efficiency > 70
                                      ? Colors.amber
                                      : Colors.red,
                            ),
                          ),
                          Text(
                            'وقت التجميع: ${product.assemblyTime} دقيقة',
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
            const SizedBox(height: 8),
            if (controller.products.length > 5)
              Center(
                child: TextButton(
                  onPressed: () {
                    // تنقل إلى صفحة تفاصيل كفاءة المنتجات
                  },
                  child: const Text('عرض المزيد...'),
                ),
              ),
          ],
        ),
      ),
    );
  }

  void _showDateRangeDialog(
    BuildContext context,
    AssemblyController controller,
  ) async {
    final dateRange = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      initialDateRange: DateTimeRange(
        start: DateTime.now().subtract(const Duration(days: 7)),
        end: DateTime.now(),
      ),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: const ColorScheme.light(
              primary: Colors.blue,
              onPrimary: Colors.white,
              onSurface: Colors.black,
            ),
            textButtonTheme: TextButtonThemeData(
              style: TextButton.styleFrom(foregroundColor: Colors.blue),
            ),
          ),
          child: child!,
        );
      },
    );

    if (dateRange != null) {
      // هنا سيتم تحديث البيانات حسب النطاق الزمني المختار
      // controller.fetchAssembliesByDateRange(dateRange.start, dateRange.end);
    }
  }
}
