import 'package:mostafa_final/data/datasources/database_helper.dart';
import 'package:mostafa_final/data/models/mold_change_history.dart';
import 'package:uuid/uuid.dart';

class MoldChangeHistoryRepository {
  final DatabaseHelper _databaseHelper = DatabaseHelper.instance;
  final _uuid = const Uuid();

  // إضافة سجل جديد في تاريخ تغيير الإسطمبات
  Future<String> addMoldChangeRecord(MoldChangeHistory history) async {
    final historyWithId = MoldChangeHistory(
      id: _uuid.v4(),
      machineId: history.machineId,
      moldId: history.moldId,
      changeDate: history.changeDate,
      previousMoldId: history.previousMoldId,
      notes: history.notes,
    );

    return await _databaseHelper.insert(
      DatabaseHelper.tableMoldChangeHistory,
      historyWithId.toMap(),
    );
  }

  // الحصول على جميع سجلات تغيير الإسطمبات لماكينة معينة
  Future<List<Map<String, dynamic>>> getMoldChangeHistoryForMachine(
    String machineId,
  ) async {
    String query = '''
      SELECT 
        mch.*,
        machine.name as machineName,
        mold.name as moldName,
        prevMold.name as previousMoldName
      FROM ${DatabaseHelper.tableMoldChangeHistory} mch
      JOIN ${DatabaseHelper.tableMachines} machine ON mch.machineId = machine.id
      JOIN ${DatabaseHelper.tableMolds} mold ON mch.moldId = mold.id
      LEFT JOIN ${DatabaseHelper.tableMolds} prevMold ON mch.previousMoldId = prevMold.id
      WHERE mch.machineId = ?
      ORDER BY mch.changeDate DESC
    ''';

    return await _databaseHelper.rawQuery(query, [machineId]);
  }

  // الحصول على جميع سجلات استخدام إسطمبة معينة
  Future<List<Map<String, dynamic>>> getMoldUsageHistory(String moldId) async {
    String query = '''
      SELECT 
        mch.*,
        machine.name as machineName,
        mold.name as moldName
      FROM ${DatabaseHelper.tableMoldChangeHistory} mch
      JOIN ${DatabaseHelper.tableMachines} machine ON mch.machineId = machine.id
      JOIN ${DatabaseHelper.tableMolds} mold ON mch.moldId = mold.id
      WHERE mch.moldId = ?
      ORDER BY mch.changeDate DESC
    ''';

    return await _databaseHelper.rawQuery(query, [moldId]);
  }

  // الحصول على آخر 10 سجلات لتغيير الإسطمبات
  Future<List<Map<String, dynamic>>> getLatestMoldChanges() async {
    String query = '''
      SELECT 
        mch.*,
        machine.name as machineName,
        mold.name as moldName,
        prevMold.name as previousMoldName
      FROM ${DatabaseHelper.tableMoldChangeHistory} mch
      JOIN ${DatabaseHelper.tableMachines} machine ON mch.machineId = machine.id
      JOIN ${DatabaseHelper.tableMolds} mold ON mch.moldId = mold.id
      LEFT JOIN ${DatabaseHelper.tableMolds} prevMold ON mch.previousMoldId = prevMold.id
      ORDER BY mch.changeDate DESC
      LIMIT 10
    ''';

    return await _databaseHelper.rawQuery(query);
  }
}
