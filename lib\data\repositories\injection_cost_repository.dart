import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:mostafa_final/data/models/injection_cost_result.dart';

/// مستودع لإدارة نتائج حسابات تكلفة الحقن باستخدام SQLite
class InjectionCostRepository {
  static const String tableName = 'injection_cost_results';
  static Database? _database;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    final databasesPath = await getDatabasesPath();
    final path = join(databasesPath, 'injection_costs.db');

    return await openDatabase(
      path,
      version: 1,
      onCreate: (db, version) async {
        await db.execute('''
          CREATE TABLE $tableName (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            machineId TEXT NOT NULL,
            machineName TEXT NOT NULL,
            moldId TEXT NOT NULL,
            moldName TEXT NOT NULL,
            materialId TEXT NOT NULL,
            materialName TEXT NOT NULL,
            cycleTimeInSeconds REAL NOT NULL,
            cavityCount INTEGER NOT NULL,
            partWeightInGrams REAL NOT NULL,
            materialCostPerKg REAL NOT NULL,
            scrapRate REAL NOT NULL,
            setupTimeInMinutes REAL NOT NULL,
            batchSize INTEGER NOT NULL,
            electricityCostPerHour REAL NOT NULL,
            operatorCostPerHour REAL NOT NULL,
            overheadCostPerHour REAL NOT NULL,
            maintenanceCostPerHour REAL NOT NULL,
            totalMaterialCost REAL NOT NULL,
            totalElectricityCost REAL NOT NULL,
            totalOperatorCost REAL NOT NULL,
            totalOverheadCost REAL NOT NULL,
            totalMaintenanceCost REAL NOT NULL,
            totalSetupCost REAL NOT NULL,
            costPerPart REAL NOT NULL,
            productionTimeInHours REAL NOT NULL,
            partsPerHour INTEGER NOT NULL,
            calculationDate TEXT NOT NULL,
            notes TEXT
          )
        ''');
      },
    );
  }

  /// حفظ نتيجة حساب جديدة
  Future<int> saveInjectionCostResult(InjectionCostResult result) async {
    try {
      final db = await database;
      return await db.insert(tableName, result.toMap());
    } catch (e) {
      throw Exception('فشل في حفظ نتيجة حساب التكلفة: $e');
    }
  }

  /// الحصول على جميع نتائج الحسابات
  Future<List<InjectionCostResult>> getAllInjectionCostResults() async {
    try {
      final db = await database;
      final results = await db.query(
        tableName,
        orderBy: 'calculationDate DESC',
      );

      return results.map((map) => InjectionCostResult.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في استرجاع نتائج حسابات التكلفة: $e');
    }
  }

  /// الحصول على نتائج الحسابات لماكينة محددة
  Future<List<InjectionCostResult>> getInjectionCostResultsByMachine(
    String machineId,
  ) async {
    try {
      final db = await database;
      final results = await db.query(
        tableName,
        where: 'machineId = ?',
        whereArgs: [machineId],
        orderBy: 'calculationDate DESC',
      );

      return results.map((map) => InjectionCostResult.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في استرجاع نتائج حسابات التكلفة للماكينة: $e');
    }
  }

  /// الحصول على نتائج الحسابات لإسطمبة محددة
  Future<List<InjectionCostResult>> getInjectionCostResultsByMold(
    String moldId,
  ) async {
    try {
      final db = await database;
      final results = await db.query(
        tableName,
        where: 'moldId = ?',
        whereArgs: [moldId],
        orderBy: 'calculationDate DESC',
      );

      return results.map((map) => InjectionCostResult.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في استرجاع نتائج حسابات التكلفة للإسطمبة: $e');
    }
  }

  /// الحصول على نتائج الحسابات لمادة خام محددة
  Future<List<InjectionCostResult>> getInjectionCostResultsByMaterial(
    String materialId,
  ) async {
    try {
      final db = await database;
      final results = await db.query(
        tableName,
        where: 'materialId = ?',
        whereArgs: [materialId],
        orderBy: 'calculationDate DESC',
      );

      return results.map((map) => InjectionCostResult.fromMap(map)).toList();
    } catch (e) {
      throw Exception('فشل في استرجاع نتائج حسابات التكلفة للمادة الخام: $e');
    }
  }

  /// الحصول على نتيجة حساب محددة بواسطة المعرف
  Future<InjectionCostResult?> getInjectionCostResultById(int id) async {
    try {
      final db = await database;
      final results = await db.query(
        tableName,
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (results.isEmpty) return null;
      return InjectionCostResult.fromMap(results.first);
    } catch (e) {
      throw Exception('فشل في استرجاع نتيجة حساب التكلفة: $e');
    }
  }

  /// حذف نتيجة حساب
  Future<int> deleteInjectionCostResult(int id) async {
    try {
      final db = await database;
      return await db.delete(tableName, where: 'id = ?', whereArgs: [id]);
    } catch (e) {
      throw Exception('فشل في حذف نتيجة حساب التكلفة: $e');
    }
  }
}
