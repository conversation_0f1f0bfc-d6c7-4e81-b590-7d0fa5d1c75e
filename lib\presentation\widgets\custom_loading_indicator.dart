import 'package:flutter/material.dart';

class CustomLoadingIndicator extends StatelessWidget {
  final double size;
  final Color color;
  final Widget? label;

  const CustomLoadingIndicator({
    super.key,
    this.size = 60.0,
    this.color = Colors.blue,
    this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: size,
          height: size,
          child: CircularProgressIndicator(
            strokeWidth: 4.0,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ),
        if (label != null)
          Padding(padding: const EdgeInsets.only(top: 16.0), child: label),
      ],
    );
  }
}
