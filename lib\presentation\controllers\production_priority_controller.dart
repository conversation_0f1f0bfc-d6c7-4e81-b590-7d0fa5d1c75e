import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/data/models/order.dart';
import 'package:mostafa_final/data/models/production_plan.dart';
import 'package:mostafa_final/data/models/product.dart';
import 'package:mostafa_final/data/repositories/order_repository.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/production_plan_repository.dart';

class ProductionPriorityController extends GetxController {
  final OrderRepository _orderRepository = Get.find<OrderRepository>();
  final ProductRepository _productRepository = Get.find<ProductRepository>();
  final ProductionPlanRepository _productionPlanRepository = Get.find<ProductionPlanRepository>();

  // حالة التحميل
  final RxBool isLoading = false.obs;
  final RxBool isSaving = false.obs;

  // بيانات المنتجات والطلبيات
  final RxList<Product> products = <Product>[].obs;
  final RxList<Order> orders = <Order>[].obs;

  // بيانات خطة الإنتاج الحالية
  final Rx<ProductionPlan?> currentPlan = Rx<ProductionPlan?>(null);

  // عناصر خطة الإنتاج
  final RxList<ProductionPlanItemWithDetails> planItems =
      <ProductionPlanItemWithDetails>[].obs;

  // فترة خطة الإنتاج
  final Rx<DateTime> startDate = DateTime.now().obs;
  final Rx<DateTime> endDate = DateTime.now().add(const Duration(days: 30)).obs;

  @override
  void onInit() {
    super.onInit();
    loadInitialData();
  }

  // تحميل البيانات الأولية
  Future<void> loadInitialData() async {
    isLoading.value = true;
    try {
      await Future.wait([loadProducts(), loadOrders(), loadActivePlan()]);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل البيانات: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // تحميل المنتجات
  Future<void> loadProducts() async {
    try {
      final productsList = await _productRepository.getAllProducts();
      products.assignAll(productsList);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل المنتجات: $e');
    }
  }

  // تحميل الطلبيات
  Future<void> loadOrders() async {
    try {
      final today = DateTime.now();
      final sixMonthsAgo = today.subtract(const Duration(days: 180));
      final ordersList = await _orderRepository.getOrdersByDateRange(
        sixMonthsAgo,
        today,
      );
      orders.assignAll(ordersList);
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل الطلبيات: $e');
    }
  }

  // تحميل خطة الإنتاج النشطة
  Future<void> loadActivePlan() async {
    try {
      final activePlans =
          await _productionPlanRepository.getActiveProductionPlans();

      if (activePlans.isNotEmpty) {
        currentPlan.value = activePlans.first;
        startDate.value = currentPlan.value!.startDate;
        endDate.value = currentPlan.value!.endDate;

        await _loadPlanItemsWithDetails();
      } else {
        // إذا لم يكن هناك خطة نشطة، نقوم بتحميل أحدث خطة
        final allPlans =
            await _productionPlanRepository.getAllProductionPlans();

        if (allPlans.isNotEmpty) {
          allPlans.sort((a, b) => b.startDate.compareTo(a.startDate));
          currentPlan.value = allPlans.first;
          startDate.value = currentPlan.value!.startDate;
          endDate.value = currentPlan.value!.endDate;

          await _loadPlanItemsWithDetails();
        }
      }
    } catch (e) {
      _showError('حدث خطأ أثناء تحميل خطة الإنتاج: $e');
    }
  }

  // تحميل تفاصيل عناصر خطة الإنتاج
  Future<void> _loadPlanItemsWithDetails() async {
    if (currentPlan.value == null) return;

    final items = <ProductionPlanItemWithDetails>[];

    for (final item in currentPlan.value!.items) {
      final product = await _productRepository.getProductById(item.productId);

      if (product != null) {
        items.add(
          ProductionPlanItemWithDetails(
            id: item.id,
            planId: item.planId,
            productId: item.productId,
            productName: product.name,
            productCode: product.code,
            quantity: item.quantity,
            priority: item.priority,
            plannedStart: item.plannedStart,
            plannedEnd: item.plannedEnd,
            completedQuantity: item.completedQuantity,
          ),
        );
      }
    }

    // ترتيب العناصر حسب الأولوية
    items.sort((a, b) => a.priority.compareTo(b.priority));
    planItems.assignAll(items);
  }

  // إنشاء خطة إنتاج جديدة
  Future<void> createNewPlan() async {
    isSaving.value = true;

    try {
      // إنشاء عناصر خطة الإنتاج
      final items = <ProductionPlanItem>[];

      for (int i = 0; i < planItems.length; i++) {
        final item = planItems[i];

        items.add(
          ProductionPlanItem(
            productId: item.productId,
            quantity: item.quantity,
            priority: item.priority,
            plannedStart: item.plannedStart,
            plannedEnd: item.plannedEnd,
            completedQuantity: item.completedQuantity,
          ),
        );
      }

      // إنشاء خطة الإنتاج
      final plan = ProductionPlan(
        startDate: startDate.value,
        endDate: endDate.value,
        items: items,
        status: ProductionPlanStatus.planned,
      );

      await _productionPlanRepository.createProductionPlan(plan);

      // إعادة تحميل الخطة النشطة
      await loadActivePlan();

      _showSuccess('تم إنشاء خطة الإنتاج بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء إنشاء خطة الإنتاج: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // تحديث خطة الإنتاج الحالية
  Future<void> updateCurrentPlan() async {
    if (currentPlan.value == null) {
      _showError('لا توجد خطة إنتاج حالية لتحديثها');
      return;
    }

    isSaving.value = true;

    try {
      // إنشاء عناصر خطة الإنتاج
      final items = <ProductionPlanItem>[];

      for (int i = 0; i < planItems.length; i++) {
        final item = planItems[i];

        items.add(
          ProductionPlanItem(
            id: item.id,
            planId: item.planId,
            productId: item.productId,
            quantity: item.quantity,
            priority: item.priority,
            plannedStart: item.plannedStart,
            plannedEnd: item.plannedEnd,
            completedQuantity: item.completedQuantity,
          ),
        );
      }

      // تحديث خطة الإنتاج
      final plan = ProductionPlan(
        id: currentPlan.value!.id,
        startDate: startDate.value,
        endDate: endDate.value,
        items: items,
        status: currentPlan.value!.status,
      );

      await _productionPlanRepository.updateProductionPlan(plan);

      // إعادة تحميل الخطة النشطة
      await loadActivePlan();

      _showSuccess('تم تحديث خطة الإنتاج بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث خطة الإنتاج: $e');
    } finally {
      isSaving.value = false;
    }
  }

  // تحديث حالة خطة الإنتاج
  Future<void> updatePlanStatus(String status) async {
    if (currentPlan.value == null) {
      _showError('لا توجد خطة إنتاج حالية لتحديث حالتها');
      return;
    }

    try {
      await _productionPlanRepository.updateProductionPlanStatus(
        currentPlan.value!.id!,
        status,
      );

      // إعادة تحميل الخطة النشطة
      await loadActivePlan();

      _showSuccess('تم تحديث حالة خطة الإنتاج بنجاح');
    } catch (e) {
      _showError('حدث خطأ أثناء تحديث حالة خطة الإنتاج: $e');
    }
  }

  // إضافة منتج إلى خطة الإنتاج
  void addProductToPlan(String productId, int quantity) {
    final product = products.firstWhere(
      (p) => p.id == productId,
      orElse:
          () => Product(
            name: '',
            code: '',
            components: [],
            assemblyTime: 0,
            packagingCost: 0,
          ),
    );

    if (product.id == null) {
      _showError('لم يتم العثور على المنتج');
      return;
    }

    // تحديد الأولوية الافتراضية: آخر أولوية + 1
    int priority = 1;
    if (planItems.isNotEmpty) {
      priority =
          planItems
              .map((item) => item.priority)
              .reduce((max, current) => max > current ? max : current) +
          1;
    }

    // إنشاء عنصر خطة الإنتاج
    final newItem = ProductionPlanItemWithDetails(
      productId: product.id!,
      productName: product.name,
      productCode: product.code,
      quantity: quantity,
      priority: priority,
      plannedStart: startDate.value,
      plannedEnd: null,
      completedQuantity: 0,
    );

    planItems.add(newItem);

    // إعادة ترتيب العناصر حسب الأولوية
    planItems.sort((a, b) => a.priority.compareTo(b.priority));
  }

  // حذف منتج من خطة الإنتاج
  void removeProductFromPlan(String itemId) {
    planItems.removeWhere((item) => item.id == itemId);
  }

  // تحديث أولوية منتج في خطة الإنتاج
  void updateItemPriority(String itemId, int newPriority) {
    final index = planItems.indexWhere((item) => item.id == itemId);

    if (index == -1) return;

    final item = planItems[index];

    // تحديث الأولوية
    planItems[index] = ProductionPlanItemWithDetails(
      id: item.id,
      planId: item.planId,
      productId: item.productId,
      productName: item.productName,
      productCode: item.productCode,
      quantity: item.quantity,
      priority: newPriority,
      plannedStart: item.plannedStart,
      plannedEnd: item.plannedEnd,
      completedQuantity: item.completedQuantity,
    );

    // إعادة ترتيب العناصر حسب الأولوية
    planItems.sort((a, b) => a.priority.compareTo(b.priority));
  }

  // تبديل أولوية منتجين
  void swapItemPriorities(String itemId1, String itemId2) {
    final index1 = planItems.indexWhere((item) => item.id == itemId1);
    final index2 = planItems.indexWhere((item) => item.id == itemId2);

    if (index1 == -1 || index2 == -1) return;

    final priority1 = planItems[index1].priority;
    final priority2 = planItems[index2].priority;

    // تبديل الأولويات
    updateItemPriority(itemId1, priority2);
    updateItemPriority(itemId2, priority1);
  }

  // تحديث كمية منتج في خطة الإنتاج
  void updateItemQuantity(String itemId, int newQuantity) {
    final index = planItems.indexWhere((item) => item.id == itemId);

    if (index == -1) return;

    final item = planItems[index];

    // تحديث الكمية
    planItems[index] = ProductionPlanItemWithDetails(
      id: item.id,
      planId: item.planId,
      productId: item.productId,
      productName: item.productName,
      productCode: item.productCode,
      quantity: newQuantity,
      priority: item.priority,
      plannedStart: item.plannedStart,
      plannedEnd: item.plannedEnd,
      completedQuantity: item.completedQuantity,
    );
  }

  // اختيار تاريخ البداية
  Future<void> selectStartDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: startDate.value,
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (picked != null && picked != startDate.value) {
      startDate.value = picked;

      // التأكد من أن تاريخ البداية لا يتجاوز تاريخ النهاية
      if (startDate.value.isAfter(endDate.value)) {
        endDate.value = startDate.value.add(const Duration(days: 30));
      }
    }
  }

  // اختيار تاريخ النهاية
  Future<void> selectEndDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: endDate.value,
      firstDate: startDate.value,
      lastDate: startDate.value.add(const Duration(days: 365)),
    );

    if (picked != null && picked != endDate.value) {
      endDate.value = picked;
    }
  }

  // إظهار رسالة نجاح
  void _showSuccess(String message) {
    Get.snackbar(
      'نجاح',
      message,
      backgroundColor: Colors.green.shade100,
      colorText: Colors.green.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  // إظهار رسالة خطأ
  void _showError(String message) {
    Get.snackbar(
      'خطأ',
      message,
      backgroundColor: Colors.red.shade100,
      colorText: Colors.red.shade800,
      snackPosition: SnackPosition.BOTTOM,
    );
  }
}

// نموذج لعنصر خطة الإنتاج مع التفاصيل
class ProductionPlanItemWithDetails {
  final String? id;
  final String? planId;
  final String productId;
  final String productName;
  final String productCode;
  final int quantity;
  final int priority;
  final DateTime? plannedStart;
  final DateTime? plannedEnd;
  final int completedQuantity;

  ProductionPlanItemWithDetails({
    this.id,
    this.planId,
    required this.productId,
    required this.productName,
    required this.productCode,
    required this.quantity,
    required this.priority,
    this.plannedStart,
    this.plannedEnd,
    this.completedQuantity = 0,
  });

  // تحويل ProductionPlanItemWithDetails إلى ProductionPlanItem
  ProductionPlanItem toProductionPlanItem() {
    return ProductionPlanItem(
      id: id,
      planId: planId,
      productId: productId,
      quantity: quantity,
      priority: priority,
      plannedStart: plannedStart,
      plannedEnd: plannedEnd,
      completedQuantity: completedQuantity,
    );
  }
}
