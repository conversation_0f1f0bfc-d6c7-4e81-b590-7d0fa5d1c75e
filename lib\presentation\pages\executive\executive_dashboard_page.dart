import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/data/models/executive_dashboard.dart';
import 'package:mostafa_final/presentation/controllers/executive_dashboard_controller.dart';
import 'package:mostafa_final/presentation/widgets/custom_drawer.dart';
import 'package:mostafa_final/presentation/widgets/loading_indicator.dart';

class ExecutiveDashboardPage extends StatelessWidget {
  const ExecutiveDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(ExecutiveDashboardController());

    return Scaffold(
      appBar: AppBar(
        title: const Text('لوحة التحكم التنفيذية'),
        centerTitle: true,
        actions: [
          Obx(() => IconButton(
            icon: controller.isRefreshing.value
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
            onPressed: controller.isRefreshing.value ? null : controller.refreshData,
          )),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSettingsDialog(controller),
          ),
        ],
      ),
      drawer: const CustomDrawer(),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: controller.generateExecutiveReport,
        icon: const Icon(Icons.assessment),
        label: const Text('إنشاء تقرير'),
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const LoadingIndicator(message: 'جاري تحميل لوحة التحكم التنفيذية...');
        }

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildControlPanel(controller),
              const SizedBox(height: 20),
              _buildPerformanceOverview(controller),
              const SizedBox(height: 20),
              _buildKPISection(controller),
              const SizedBox(height: 20),
              _buildChartsSection(controller),
              const SizedBox(height: 20),
              _buildReportsSection(controller),
            ],
          ),
        );
      }),
    );
  }

  Widget _buildControlPanel(ExecutiveDashboardController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'لوحة التحكم',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<ReportPeriod>(
                      decoration: const InputDecoration(
                        labelText: 'فترة التقرير',
                        border: OutlineInputBorder(),
                      ),
                      value: controller.selectedPeriod.value,
                      items: ReportPeriod.values.map((period) {
                        return DropdownMenuItem(
                          value: period,
                          child: Text(period.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.changePeriod(value);
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Obx(
                    () => DropdownButtonFormField<ReportType>(
                      decoration: const InputDecoration(
                        labelText: 'نوع التقرير',
                        border: OutlineInputBorder(),
                      ),
                      value: controller.selectedReportType.value,
                      items: ReportType.values.map((type) {
                        return DropdownMenuItem(
                          value: type,
                          child: Text(type.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.changeReportType(value);
                        }
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                ElevatedButton.icon(
                  onPressed: () => _showDateRangePicker(controller),
                  icon: const Icon(Icons.date_range),
                  label: const Text('تخصيص الفترة'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceOverview(ExecutiveDashboardController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'نظرة عامة على الأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final stats = controller.quickStats;
              final overallScore = stats['overallScore'] as double;
              
              return Column(
                children: [
                  // النتيجة الإجمالية
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          _getScoreColor(overallScore).withValues(alpha: 0.1),
                          _getScoreColor(overallScore).withValues(alpha: 0.3),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: _getScoreColor(overallScore)),
                    ),
                    child: Column(
                      children: [
                        Text(
                          'النتيجة الإجمالية',
                          style: TextStyle(
                            fontSize: 16,
                            color: _getScoreColor(overallScore),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '${overallScore.toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 36,
                            fontWeight: FontWeight.bold,
                            color: _getScoreColor(overallScore),
                          ),
                        ),
                        Text(
                          _getScoreDescription(overallScore),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                  // المؤشرات السريعة
                  Row(
                    children: [
                      Expanded(
                        child: _buildQuickStatCard(
                          'كفاءة الإنتاج',
                          '${(stats['productionEfficiency'] as double? ?? 0).toStringAsFixed(1)}%',
                          Icons.trending_up,
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickStatCard(
                          'معدل التوفر',
                          '${(stats['availability'] as double? ?? 0).toStringAsFixed(1)}%',
                          Icons.check_circle,
                          Colors.green,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickStatCard(
                          'هامش الربح',
                          '${(stats['profitMargin'] as double? ?? 0).toStringAsFixed(1)}%',
                          Icons.attach_money,
                          Colors.orange,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildQuickStatCard(
                          'التقارير',
                          '${stats['totalReports']}',
                          Icons.description,
                          Colors.purple,
                        ),
                      ),
                    ],
                  ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildKPISection(ExecutiveDashboardController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'المؤشرات الرئيسية للأداء',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.kpis.isEmpty) {
                return const Center(
                  child: Text('لا توجد مؤشرات متاحة'),
                );
              }

              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 4,
                  childAspectRatio: 1.2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                ),
                itemCount: controller.kpis.length,
                itemBuilder: (context, index) {
                  final kpi = controller.kpis[index];
                  return _buildKPICard(kpi);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildKPICard(KPI kpi) {
    final isTargetAchieved = kpi.isTargetAchieved;
    final changePercentage = kpi.changePercentage;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isTargetAchieved ? Colors.green.withValues(alpha: 0.1) : Colors.orange.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isTargetAchieved ? Colors.green : Colors.orange,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                _getKPIIcon(kpi),
                color: isTargetAchieved ? Colors.green : Colors.orange,
                size: 24,
              ),
              Icon(
                _getTrendIcon(kpi.trend),
                color: _getTrendColor(kpi.trend),
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${kpi.currentValue.toStringAsFixed(1)}${kpi.unit.symbol}',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: isTargetAchieved ? Colors.green : Colors.orange,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            kpi.name,
            style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          Text(
            'الهدف: ${kpi.targetValue.toStringAsFixed(1)}${kpi.unit.symbol}',
            style: const TextStyle(fontSize: 10, color: Colors.grey),
          ),
          if (changePercentage != 0) ...[
            const SizedBox(height: 4),
            Text(
              '${changePercentage > 0 ? '+' : ''}${changePercentage.toStringAsFixed(1)}%',
              style: TextStyle(
                fontSize: 10,
                color: changePercentage > 0 ? Colors.green : Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildChartsSection(ExecutiveDashboardController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الرسوم البيانية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() {
              final charts = controller.chartData;
              if (charts.isEmpty) {
                return const Center(
                  child: Text('لا توجد بيانات للرسوم البيانية'),
                );
              }

              return Column(
                children: [
                  // رسم بياني لاتجاه الإنتاج
                  if (charts.containsKey('production_trend'))
                    _buildSimpleChart(
                      'اتجاه الإنتاج',
                      charts['production_trend']!,
                      Colors.blue,
                    ),
                  const SizedBox(height: 20),
                  
                  // رسم بياني لكفاءة الماكينات
                  if (charts.containsKey('machine_efficiency'))
                    _buildSimpleChart(
                      'كفاءة الماكينات',
                      charts['machine_efficiency']!,
                      Colors.green,
                    ),
                ],
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildSimpleChart(String title, List<ChartData> data, Color color) {
    if (data.isEmpty) return const SizedBox();
    
    final maxValue = data.map((d) => d.value).reduce((a, b) => a > b ? a : b);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: color.withValues(alpha: 0.3)),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: data.map((item) {
              final height = (item.value / maxValue) * 150;
              return Expanded(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        height: height,
                        decoration: BoxDecoration(
                          color: color,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        item.label,
                        style: const TextStyle(fontSize: 10),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildReportsSection(ExecutiveDashboardController controller) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'التقارير التنفيذية',
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Obx(() => ElevatedButton.icon(
                  onPressed: controller.isGeneratingReport.value
                      ? null
                      : controller.generateExecutiveReport,
                  icon: controller.isGeneratingReport.value
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.add),
                  label: Text(
                    controller.isGeneratingReport.value
                        ? 'جاري الإنشاء...'
                        : 'إنشاء تقرير جديد',
                  ),
                )),
              ],
            ),
            const SizedBox(height: 16),
            Obx(() {
              if (controller.reports.isEmpty) {
                return const Center(
                  child: Text('لا توجد تقارير متاحة'),
                );
              }

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: controller.reports.take(5).length,
                itemBuilder: (context, index) {
                  final report = controller.reports[index];
                  return _buildReportCard(report, controller);
                },
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildReportCard(ExecutiveReport report, ExecutiveDashboardController controller) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getReportTypeColor(report.type),
          child: Icon(
            _getReportTypeIcon(report.type),
            color: Colors.white,
          ),
        ),
        title: Text(report.title),
        subtitle: Text(
          '${report.type.displayName} - ${DateFormat('yyyy-MM-dd HH:mm').format(report.generatedAt)}',
        ),
        trailing: PopupMenuButton(
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'view',
              child: ListTile(
                leading: Icon(Icons.visibility),
                title: Text('عرض'),
              ),
            ),
            const PopupMenuItem(
              value: 'export',
              child: ListTile(
                leading: Icon(Icons.download),
                title: Text('تصدير'),
              ),
            ),
            const PopupMenuItem(
              value: 'delete',
              child: ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('حذف', style: TextStyle(color: Colors.red)),
              ),
            ),
          ],
          onSelected: (value) {
            switch (value) {
              case 'view':
                _showReportDetails(report);
                break;
              case 'export':
                controller.exportReport(report);
                break;
              case 'delete':
                _confirmDeleteReport(report, controller);
                break;
            }
          },
        ),
      ),
    );
  }

  // طرق مساعدة للألوان والأيقونات
  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  String _getScoreDescription(double score) {
    if (score >= 80) return 'أداء ممتاز';
    if (score >= 60) return 'أداء جيد';
    if (score >= 40) return 'أداء متوسط';
    return 'يحتاج تحسين';
  }

  IconData _getKPIIcon(KPI kpi) {
    switch (kpi.id) {
      case 'total_production':
        return Icons.precision_manufacturing;
      case 'production_efficiency':
        return Icons.trending_up;
      case 'capacity_utilization':
        return Icons.donut_large;
      case 'mtbf':
        return Icons.timer;
      case 'mttr':
        return Icons.build;
      case 'availability':
        return Icons.check_circle;
      case 'total_sales':
        return Icons.attach_money;
      case 'production_cost':
        return Icons.account_balance_wallet;
      case 'profit_margin':
        return Icons.trending_up;
      case 'defect_rate':
        return Icons.error_outline;
      case 'first_pass_yield':
        return Icons.verified;
      default:
        return Icons.analytics;
    }
  }

  IconData _getTrendIcon(KPITrend trend) {
    switch (trend) {
      case KPITrend.up:
        return Icons.arrow_upward;
      case KPITrend.down:
        return Icons.arrow_downward;
      case KPITrend.stable:
        return Icons.remove;
      case KPITrend.unknown:
        return Icons.help_outline;
    }
  }

  Color _getTrendColor(KPITrend trend) {
    switch (trend) {
      case KPITrend.up:
        return Colors.green;
      case KPITrend.down:
        return Colors.red;
      case KPITrend.stable:
        return Colors.grey;
      case KPITrend.unknown:
        return Colors.grey;
    }
  }

  Color _getReportTypeColor(ReportType type) {
    switch (type) {
      case ReportType.production:
        return Colors.blue;
      case ReportType.maintenance:
        return Colors.orange;
      case ReportType.financial:
        return Colors.green;
      case ReportType.quality:
        return Colors.purple;
      case ReportType.efficiency:
        return Colors.teal;
      case ReportType.comprehensive:
        return Colors.indigo;
    }
  }

  IconData _getReportTypeIcon(ReportType type) {
    switch (type) {
      case ReportType.production:
        return Icons.precision_manufacturing;
      case ReportType.maintenance:
        return Icons.build;
      case ReportType.financial:
        return Icons.attach_money;
      case ReportType.quality:
        return Icons.verified;
      case ReportType.efficiency:
        return Icons.trending_up;
      case ReportType.comprehensive:
        return Icons.dashboard;
    }
  }

  // نوافذ الحوار
  void _showDateRangePicker(ExecutiveDashboardController controller) async {
    final dateRange = await showDateRangePicker(
      context: Get.context!,
      firstDate: DateTime.now().subtract(const Duration(days: 365)),
      lastDate: DateTime.now(),
      initialDateRange: controller.selectedDateRange.value,
    );
    
    if (dateRange != null) {
      controller.changeDateRange(dateRange);
    }
  }

  void _showSettingsDialog(ExecutiveDashboardController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('إعدادات لوحة التحكم'),
        content: const SizedBox(
          width: 400,
          height: 300,
          child: Center(child: Text('سيتم تطوير إعدادات لوحة التحكم')),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _showReportDetails(ExecutiveReport report) {
    Get.dialog(
      AlertDialog(
        title: Text(report.title),
        content: SizedBox(
          width: 600,
          height: 400,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('الوصف: ${report.description}'),
                const SizedBox(height: 16),
                const Text('الرؤى:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...report.insights.map((insight) => Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $insight'),
                )),
                const SizedBox(height: 16),
                const Text('التوصيات:', style: TextStyle(fontWeight: FontWeight.bold)),
                ...report.recommendations.map((recommendation) => Padding(
                  padding: const EdgeInsets.only(left: 16, top: 4),
                  child: Text('• $recommendation'),
                )),
              ],
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إغلاق'),
          ),
        ],
      ),
    );
  }

  void _confirmDeleteReport(ExecutiveReport report, ExecutiveDashboardController controller) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف التقرير "${report.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () {
              controller.deleteReport(report.id);
              Get.back();
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
