import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mostafa_final/presentation/controllers/backup_controller.dart';
import 'package:file_picker/file_picker.dart';

/// صفحة إدارة النسخ الاحتياطي
class BackupPage extends StatelessWidget {
  final BackupController controller = Get.find<BackupController>();

  BackupPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إدارة النسخ الاحتياطي'),
        centerTitle: true,
      ),
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }

        return Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildLastBackupInfo(),
              const SizedBox(height: 16),
              _buildBackupActions(context),
              const SizedBox(height: 24),
              _buildBackupScheduleSection(context),
              const SizedBox(height: 24),
              _buildBackupsList(),
            ],
          ),
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _createBackup(context),
        tooltip: 'إنشاء نسخة احتياطية',
        child: const Icon(Icons.add),
      ),
    );
  }

  /// بناء معلومات آخر نسخة احتياطية
  Widget _buildLastBackupInfo() {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'آخر نسخة احتياطية',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Obx(() {
              final lastBackup = controller.lastBackupTime.value;
              if (lastBackup != null) {
                final formattedDate = DateFormat(
                  'yyyy-MM-dd HH:mm',
                ).format(lastBackup);
                return Text('تم إنشاء آخر نسخة احتياطية في: $formattedDate');
              } else {
                return const Text('لم يتم إنشاء أي نسخة احتياطية بعد');
              }
            }),
          ],
        ),
      ),
    );
  }

  /// بناء أزرار إجراءات النسخ الاحتياطي
  Widget _buildBackupActions(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: () => _createBackup(context),
          icon: const Icon(Icons.backup),
          label: const Text('إنشاء نسخة احتياطية'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
        ElevatedButton.icon(
          onPressed: () => _selectBackupFolder(context),
          icon: const Icon(Icons.folder),
          label: const Text('تغيير مجلد الحفظ'),
          style: ElevatedButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          ),
        ),
      ],
    );
  }

  /// بناء قسم جدولة النسخ الاحتياطي
  Widget _buildBackupScheduleSection(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'جدولة النسخ الاحتياطي التلقائي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Obx(() => _buildScheduleOptions(context)),
          ],
        ),
      ),
    );
  }

  /// بناء خيارات جدولة النسخ الاحتياطي
  Widget _buildScheduleOptions(BuildContext context) {
    return Column(
      children: [
        RadioListTile<String>(
          title: const Text('يدوي (بدون جدولة)'),
          value: 'manual',
          groupValue: controller.backupSchedule.value,
          onChanged: (value) => controller.setBackupSchedule(value!),
        ),
        RadioListTile<String>(
          title: const Text('يومي (كل 24 ساعة)'),
          value: 'auto_24',
          groupValue: controller.backupSchedule.value,
          onChanged: (value) => controller.setBackupSchedule(value!),
        ),
        RadioListTile<String>(
          title: const Text('أسبوعي (كل 7 أيام)'),
          value: 'auto_168',
          groupValue: controller.backupSchedule.value,
          onChanged: (value) => controller.setBackupSchedule(value!),
        ),
      ],
    );
  }

  /// بناء قائمة النسخ الاحتياطية
  Widget _buildBackupsList() {
    return Expanded(
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'النسخ الاحتياطية المتاحة',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 16),
              Expanded(
                child: Obx(() {
                  if (controller.backups.isEmpty) {
                    return const Center(
                      child: Text('لا توجد نسخ احتياطية متاحة'),
                    );
                  }

                  return ListView.builder(
                    itemCount: controller.backups.length,
                    itemBuilder: (context, index) {
                      final backup = controller.backups[index];
                      return _buildBackupItem(context, backup);
                    },
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء عنصر نسخة احتياطية
  Widget _buildBackupItem(BuildContext context, Map<String, dynamic> backup) {
    final name = backup['name'] as String;
    final size = controller.formatFileSize(backup['size'] as int);
    final modified = controller.formatModifiedDate(
      backup['modified'] as DateTime,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(name),
        subtitle: Text('الحجم: $size\nتاريخ الإنشاء: $modified'),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            IconButton(
              icon: const Icon(Icons.restore),
              tooltip: 'استعادة',
              onPressed:
                  () => _confirmRestore(context, backup['path'] as String),
            ),
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف',
              onPressed:
                  () => _confirmDelete(context, backup['path'] as String),
            ),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  /// إنشاء نسخة احتياطية
  Future<void> _createBackup(BuildContext context) async {
    final TextEditingController nameController = TextEditingController();

    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('إنشاء نسخة احتياطية'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('أدخل اسماً للنسخة الاحتياطية (اختياري):'),
                const SizedBox(height: 8),
                TextField(
                  controller: nameController,
                  decoration: const InputDecoration(
                    hintText: 'اسم النسخة الاحتياطية',
                    border: OutlineInputBorder(),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('إنشاء'),
              ),
            ],
          ),
    );

    if (result == true) {
      final customName =
          nameController.text.isNotEmpty ? nameController.text : null;

      final success = await controller.createBackup(customName: customName);

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم إنشاء النسخة الاحتياطية بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          controller.errorMessage.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تأكيد استعادة نسخة احتياطية
  Future<void> _confirmRestore(BuildContext context, String backupPath) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('استعادة النسخة الاحتياطية'),
            content: const Text(
              'هل أنت متأكد من استعادة قاعدة البيانات من هذه النسخة الاحتياطية؟ سيتم استبدال جميع البيانات الحالية.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('استعادة'),
              ),
            ],
          ),
    );

    if (result == true) {
      final success = await controller.restoreBackup(backupPath);

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم استعادة قاعدة البيانات بنجاح. قد تحتاج إلى إعادة تشغيل التطبيق.',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
          duration: const Duration(seconds: 5),
        );
      } else {
        Get.snackbar(
          'خطأ',
          controller.errorMessage.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// تأكيد حذف نسخة احتياطية
  Future<void> _confirmDelete(BuildContext context, String backupPath) async {
    final result = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('حذف النسخة الاحتياطية'),
            content: const Text('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              ElevatedButton(
                onPressed: () => Navigator.of(context).pop(true),
                style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                child: const Text('حذف'),
              ),
            ],
          ),
    );

    if (result == true) {
      final success = await controller.deleteBackup(backupPath);

      if (success) {
        Get.snackbar(
          'تم بنجاح',
          'تم حذف النسخة الاحتياطية بنجاح',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'خطأ',
          controller.errorMessage.value,
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    }
  }

  /// اختيار مجلد النسخ الاحتياطي
  Future<void> _selectBackupFolder(BuildContext context) async {
    try {
      final result = await FilePicker.platform.getDirectoryPath();

      if (result != null) {
        final success = await controller.setBackupDirectory(result);

        if (success) {
          Get.snackbar(
            'تم بنجاح',
            'تم تعيين مجلد النسخ الاحتياطي بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.green,
            colorText: Colors.white,
          );

          // إعادة تحميل النسخ الاحتياطية
          await controller.loadBackups();
        } else {
          Get.snackbar(
            'خطأ',
            'فشل في تعيين مجلد النسخ الاحتياطي',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: Colors.red,
            colorText: Colors.white,
          );
        }
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار المجلد: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
