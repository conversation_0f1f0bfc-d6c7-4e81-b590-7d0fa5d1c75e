import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mostafa_final/presentation/controllers/sales_controller.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/custom_loading_indicator.dart';
import 'package:mostafa_final/presentation/widgets/responsive_page.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';
import 'package:mostafa_final/utils/format_utils.dart';

class SalesPage extends StatelessWidget {
  const SalesPage({super.key});

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final controller = Get.put(SalesController());

    return ResponsivePage(
      title: 'إدارة المبيعات',
      actions: [
        IconButton(
          icon: Icon(Icons.refresh, size: ScreenSize.isSmallScreen ? 20 : 24),
          onPressed: () => controller.fetchAllData(),
        ),
        IconButton(
          icon: Icon(Icons.print, size: ScreenSize.isSmallScreen ? 20 : 24),
          tooltip: 'طباعة فاتورة',
          onPressed: () {
            if (controller.selectedSale.value != null) {
              controller.printInvoice(controller.selectedSale.value!);
            } else {
              Get.snackbar(
                'تنبيه',
                'يرجى اختيار عملية بيع أولاً',
                backgroundColor: Colors.amber,
                colorText: Colors.black,
              );
            }
          },
        ),
      ],
      body: Obx(() {
        if (controller.isLoading.value) {
          return Center(
            child: CustomLoadingIndicator(
              size: ScreenSize.isSmallScreen ? 40 : 50,
            ),
          );
        }

        // في الشاشات الصغيرة، نعرض قائمة المبيعات أو التفاصيل حسب الاختيار
        if (ScreenSize.isSmallScreen) {
          return controller.selectedSale.value == null
              ? _buildSalesList(context, controller)
              : Column(
                children: [
                  Padding(
                    padding: EdgeInsets.all(ScreenSize.getPadding(8)),
                    child: Row(
                      children: [
                        IconButton(
                          icon: Icon(Icons.arrow_back),
                          onPressed: () => controller.selectedSale.value = null,
                        ),
                        TextUtils.responsiveText(
                          'العودة إلى قائمة المبيعات',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ),
                  Expanded(child: _buildSaleDetails(context, controller)),
                ],
              );
        }

        // في الشاشات الكبيرة، نعرض قائمة المبيعات والتفاصيل جنبًا إلى جنب
        return Row(
          children: [
            // قائمة المبيعات
            Expanded(flex: 1, child: _buildSalesList(context, controller)),

            // تفاصيل المبيعات
            Expanded(flex: 2, child: _buildSaleDetails(context, controller)),
          ],
        );
      }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          _showAddSaleDialog(context, controller);
        },
        tooltip: 'إضافة عملية بيع',
        child: Icon(Icons.add, size: ScreenSize.isSmallScreen ? 20 : 24),
      ),
    );
  }

  // بناء قائمة المبيعات
  Widget _buildSalesList(BuildContext context, SalesController controller) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(8)),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.all(ScreenSize.getPadding(8)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                TextUtils.responsiveText(
                  'المبيعات',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                    fontWeight: FontWeight.bold,
                  ),
                  maxLines: 1,
                ),
                IconButton(
                  icon: Icon(
                    Icons.calendar_today,
                    size: ScreenSize.isSmallScreen ? 20 : 24,
                  ),
                  onPressed: () => _showDatePicker(context, controller),
                  tooltip: 'تصفية حسب التاريخ',
                  constraints: BoxConstraints(
                    minWidth: ScreenSize.getPadding(36),
                    minHeight: ScreenSize.getPadding(36),
                  ),
                  padding: EdgeInsets.all(ScreenSize.getPadding(4)),
                ),
              ],
            ),
          ),
          Divider(height: ScreenSize.getPadding(1)),
          Expanded(
            child:
                controller.sales.isEmpty
                    ? Center(
                      child: TextUtils.responsiveText(
                        'لا توجد مبيعات مسجلة',
                        style: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                          color: Colors.grey.shade600,
                        ),
                        maxLines: 1,
                        textAlign: TextAlign.center,
                      ),
                    )
                    : ListView.separated(
                      itemCount: controller.sales.length,
                      separatorBuilder:
                          (context, index) =>
                              Divider(height: ScreenSize.getPadding(1)),
                      itemBuilder: (context, index) {
                        final sale = controller.sales[index];
                        return ListTile(
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: ScreenSize.getPadding(16),
                            vertical: ScreenSize.getPadding(8),
                          ),
                          title: TextUtils.responsiveText(
                            'فاتورة #${sale.invoiceNumber}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                          ),
                          subtitle: TextUtils.responsiveText(
                            'العميل: ${sale.customerName} | ${FormatUtils.formatDate(sale.date)}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              color: Colors.grey.shade700,
                            ),
                            maxLines: 2,
                          ),
                          trailing: TextUtils.responsiveText(
                            FormatUtils.formatCurrency(sale.totalAmount),
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.green,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                          selected:
                              controller.selectedSale.value?.id == sale.id,
                          selectedTileColor: Colors.blue.withAlpha(25),
                          onTap: () => controller.selectSale(sale),
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  // بناء تفاصيل المبيعات
  Widget _buildSaleDetails(BuildContext context, SalesController controller) {
    return Card(
      margin: EdgeInsets.all(ScreenSize.getPadding(8)),
      child:
          controller.selectedSale.value == null
              ? Center(
                child: TextUtils.responsiveText(
                  'اختر عملية بيع لعرض التفاصيل',
                  style: TextStyle(
                    fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                    color: Colors.grey.shade600,
                  ),
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
              )
              : Padding(
                padding: EdgeInsets.all(ScreenSize.getPadding(16)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: TextUtils.responsiveText(
                            'فاتورة بيع #${controller.selectedSale.value!.invoiceNumber}',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 16 : 20,
                              fontWeight: FontWeight.bold,
                            ),
                            maxLines: 1,
                          ),
                        ),
                        TextUtils.responsiveText(
                          'التاريخ: ${FormatUtils.formatDate(controller.selectedSale.value!.date)}',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 12 : 16,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                    Divider(height: ScreenSize.getPadding(16)),
                    _buildInfoRow(
                      Icons.person,
                      'العميل:',
                      controller.selectedSale.value!.customerName,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    _buildInfoRow(
                      Icons.phone,
                      'رقم الهاتف:',
                      controller.selectedSale.value!.customerPhone,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    _buildInfoRow(
                      Icons.location_on,
                      'العنوان:',
                      controller.selectedSale.value!.shippingAddress,
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    TextUtils.responsiveText(
                      'المنتجات',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    _buildProductsTable(controller),
                    Divider(height: ScreenSize.getPadding(16)),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextUtils.responsiveText(
                          'الإجمالي:',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                        ),
                        TextUtils.responsiveText(
                          FormatUtils.formatCurrency(
                            controller.selectedSale.value!.totalAmount,
                          ),
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: TextUtils.responsiveText(
                            'حالة الشحن: ${controller.selectedSale.value!.shippingStatus}',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                              color: _getStatusColor(
                                controller.selectedSale.value!.shippingStatus,
                              ),
                            ),
                            maxLines: 1,
                          ),
                        ),
                        ElevatedButton.icon(
                          onPressed: () {
                            _showUpdateStatusDialog(context, controller);
                          },
                          icon: Icon(
                            Icons.edit,
                            size: ScreenSize.isSmallScreen ? 16 : 20,
                          ),
                          label: TextUtils.responsiveText(
                            'تعديل الحالة',
                            style: TextStyle(
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                          style: ElevatedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(12),
                              vertical: ScreenSize.getPadding(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        OutlinedButton.icon(
                          onPressed: () {
                            controller.deleteSale(
                              controller.selectedSale.value!.id!,
                            );
                          },
                          icon: Icon(
                            Icons.delete,
                            color: Colors.red,
                            size: ScreenSize.isSmallScreen ? 16 : 20,
                          ),
                          label: TextUtils.responsiveText(
                            'حذف',
                            style: TextStyle(
                              color: Colors.red,
                              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            ),
                            maxLines: 1,
                          ),
                          style: OutlinedButton.styleFrom(
                            padding: EdgeInsets.symmetric(
                              horizontal: ScreenSize.getPadding(12),
                              vertical: ScreenSize.getPadding(8),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
    );
  }

  // بناء صف معلومات
  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Row(
      children: [
        Icon(
          icon,
          size: ScreenSize.isSmallScreen ? 16 : 20,
          color: Colors.grey.shade700,
        ),
        SizedBox(width: ScreenSize.getPadding(8)),
        TextUtils.responsiveText(
          label,
          style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
          maxLines: 1,
        ),
        SizedBox(width: ScreenSize.getPadding(8)),
        Expanded(
          child: TextUtils.responsiveText(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: ScreenSize.isSmallScreen ? 12 : 14,
            ),
            maxLines: 2,
          ),
        ),
      ],
    );
  }

  // عرض جدول المنتجات
  Widget _buildProductsTable(SalesController controller) {
    return Expanded(
      child: ListView.separated(
        itemCount: controller.selectedSale.value!.items.length,
        separatorBuilder:
            (context, index) => Divider(height: ScreenSize.getPadding(1)),
        itemBuilder: (context, index) {
          final item = controller.selectedSale.value!.items[index];

          // في الشاشات الصغيرة، نعرض المنتجات بشكل عمودي
          if (ScreenSize.isSmallScreen) {
            return Card(
              margin: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(4)),
              child: Padding(
                padding: EdgeInsets.all(ScreenSize.getPadding(8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextUtils.responsiveText(
                      item.productName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: ScreenSize.getPadding(4)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextUtils.responsiveText(
                          'الكمية: ${item.quantity}',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                            color: Colors.grey.shade700,
                          ),
                          maxLines: 1,
                        ),
                        TextUtils.responsiveText(
                          'السعر: ${FormatUtils.formatCurrency(item.unitPrice)}',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 11 : 13,
                            color: Colors.grey.shade700,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                    SizedBox(height: ScreenSize.getPadding(4)),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        TextUtils.responsiveText(
                          'الإجمالي: ${FormatUtils.formatCurrency(item.totalPrice)}',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                            color: Colors.green,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          }

          // في الشاشات الكبيرة، نعرض المنتجات بشكل جدول
          return Padding(
            padding: EdgeInsets.symmetric(vertical: ScreenSize.getPadding(8)),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: TextUtils.responsiveText(
                    item.productName,
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: TextUtils.responsiveText(
                    item.quantity.toString(),
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: TextUtils.responsiveText(
                    FormatUtils.formatCurrency(item.unitPrice),
                    style: TextStyle(
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: TextUtils.responsiveText(
                    FormatUtils.formatCurrency(item.totalPrice),
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                    ),
                    maxLines: 1,
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  // لون حالة الشحن
  Color _getStatusColor(String status) {
    switch (status) {
      case 'تم التسليم':
        return Colors.green;
      case 'قيد الشحن':
        return Colors.orange;
      case 'قيد التجهيز':
        return Colors.blue;
      case 'ملغي':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  // عرض منتقي التاريخ
  void _showDatePicker(BuildContext context, SalesController controller) async {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final selectedDate = await showDatePicker(
      context: context,
      initialDate: DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime(2030),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: Theme.of(context).primaryColor),
            textTheme: Theme.of(context).textTheme.copyWith(
              bodyLarge: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
              bodyMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
              ),
              titleMedium: TextStyle(
                fontSize: ScreenSize.isSmallScreen ? 14 : 16,
              ),
            ),
          ),
          child: child!,
        );
      },
    );

    if (selectedDate != null) {
      controller.filterSalesByDate(selectedDate);
    }
  }

  // عرض مربع حوار تحديث الحالة
  void _showUpdateStatusDialog(
    BuildContext context,
    SalesController controller,
  ) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: TextUtils.responsiveText(
            'تعديل حالة الشحن',
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
          ),
          contentPadding: EdgeInsets.symmetric(
            vertical: ScreenSize.getPadding(16),
            horizontal: ScreenSize.getPadding(8),
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatusRadioTile(context, controller, 'قيد التجهيز'),
              _buildStatusRadioTile(context, controller, 'قيد الشحن'),
              _buildStatusRadioTile(context, controller, 'تم التسليم'),
              _buildStatusRadioTile(context, controller, 'ملغي'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenSize.getPadding(16),
                  vertical: ScreenSize.getPadding(8),
                ),
              ),
              child: TextUtils.responsiveText(
                'إلغاء',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                maxLines: 1,
              ),
            ),
            ElevatedButton(
              onPressed: () {
                controller.updateSaleStatus(
                  controller.selectedSale.value!.id!,
                  controller.selectedStatus.value,
                );
                Navigator.pop(context);
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenSize.getPadding(16),
                  vertical: ScreenSize.getPadding(8),
                ),
              ),
              child: TextUtils.responsiveText(
                'حفظ',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                maxLines: 1,
              ),
            ),
          ],
        );
      },
    );
  }

  // بناء عنصر اختيار الحالة
  Widget _buildStatusRadioTile(
    BuildContext context,
    SalesController controller,
    String status,
  ) {
    return ListTile(
      contentPadding: EdgeInsets.symmetric(
        horizontal: ScreenSize.getPadding(8),
        vertical: ScreenSize.getPadding(0),
      ),
      dense: ScreenSize.isSmallScreen,
      title: TextUtils.responsiveText(
        status,
        style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
        maxLines: 1,
      ),
      leading: Radio<String>(
        value: status,
        groupValue: controller.selectedStatus.value,
        onChanged: (value) {
          controller.selectedStatus.value = value!;
        },
        visualDensity: VisualDensity(
          horizontal: ScreenSize.isSmallScreen ? -4 : -2,
          vertical: ScreenSize.isSmallScreen ? -4 : -2,
        ),
      ),
    );
  }

  // عرض مربع حوار إضافة عملية بيع
  void _showAddSaleDialog(BuildContext context, SalesController controller) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    // إعادة تعيين بيانات البيع الجديد
    controller.resetNewSale();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: TextUtils.responsiveText(
            'إضافة عملية بيع جديدة',
            style: TextStyle(
              fontSize: ScreenSize.isSmallScreen ? 16 : 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 1,
          ),
          content: SizedBox(
            width:
                MediaQuery.of(context).size.width *
                (ScreenSize.isSmallScreen ? 0.9 : 0.7),
            height:
                MediaQuery.of(context).size.height *
                (ScreenSize.isSmallScreen ? 0.8 : 0.7),
            child: Padding(
              padding: EdgeInsets.all(ScreenSize.getPadding(16)),
              child: Obx(() {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // بيانات العميل
                    TextUtils.responsiveText(
                      'بيانات العميل',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    TextField(
                      decoration: InputDecoration(
                        labelText: 'اسم العميل',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      onChanged: (value) {
                        controller.newSaleCustomerName.value = value;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    TextField(
                      decoration: InputDecoration(
                        labelText: 'رقم الهاتف',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      onChanged: (value) {
                        controller.newSaleCustomerPhone.value = value;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    TextField(
                      decoration: InputDecoration(
                        labelText: 'عنوان الشحن',
                        border: OutlineInputBorder(),
                        contentPadding: EdgeInsets.symmetric(
                          horizontal: ScreenSize.getPadding(16),
                          vertical: ScreenSize.getPadding(12),
                        ),
                        labelStyle: TextStyle(
                          fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                        ),
                      ),
                      onChanged: (value) {
                        controller.newSaleShippingAddress.value = value;
                      },
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                      ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),

                    // اختيار المنتجات
                    TextUtils.responsiveText(
                      'المنتجات',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),

                    // في الشاشات الصغيرة، نعرض عناصر اختيار المنتج بشكل عمودي
                    ScreenSize.isSmallScreen
                        ? Column(
                          children: [
                            DropdownButtonFormField(
                              decoration: InputDecoration(
                                labelText: 'المنتج',
                                border: OutlineInputBorder(),
                                contentPadding: EdgeInsets.symmetric(
                                  horizontal: ScreenSize.getPadding(16),
                                  vertical: ScreenSize.getPadding(12),
                                ),
                                labelStyle: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                ),
                              ),
                              items:
                                  controller.availableProducts.map((product) {
                                    return DropdownMenuItem(
                                      value: product,
                                      child: TextUtils.responsiveText(
                                        '${product.name} (${product.code})',
                                        style: TextStyle(
                                          fontSize:
                                              ScreenSize.isSmallScreen
                                                  ? 12
                                                  : 14,
                                        ),
                                        maxLines: 1,
                                      ),
                                    );
                                  }).toList(),
                              onChanged: (value) {
                                if (value != null) {
                                  controller.selectedProduct.value = value;
                                }
                              },
                              style: TextStyle(
                                fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                              ),
                              isExpanded: true,
                              icon: Icon(
                                Icons.arrow_drop_down,
                                size: ScreenSize.isSmallScreen ? 20 : 24,
                              ),
                            ),
                            SizedBox(height: ScreenSize.getPadding(8)),
                            Row(
                              children: [
                                Expanded(
                                  child: TextField(
                                    decoration: InputDecoration(
                                      labelText: 'العدد',
                                      border: OutlineInputBorder(),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: ScreenSize.getPadding(16),
                                        vertical: ScreenSize.getPadding(12),
                                      ),
                                      labelStyle: TextStyle(
                                        fontSize:
                                            ScreenSize.isSmallScreen ? 12 : 14,
                                      ),
                                    ),
                                    keyboardType: TextInputType.number,
                                    onChanged: (value) {
                                      controller.selectedQuantity.value =
                                          int.tryParse(value) ?? 1;
                                    },
                                    style: TextStyle(
                                      fontSize:
                                          ScreenSize.isSmallScreen ? 12 : 14,
                                    ),
                                  ),
                                ),
                                SizedBox(width: ScreenSize.getPadding(8)),
                                ElevatedButton(
                                  onPressed: () {
                                    controller.addProductToSale();
                                  },
                                  style: ElevatedButton.styleFrom(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: ScreenSize.getPadding(16),
                                      vertical: ScreenSize.getPadding(12),
                                    ),
                                  ),
                                  child: TextUtils.responsiveText(
                                    'إضافة',
                                    style: TextStyle(
                                      fontSize:
                                          ScreenSize.isSmallScreen ? 12 : 14,
                                    ),
                                    maxLines: 1,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        )
                        // في الشاشات الكبيرة، نعرض عناصر اختيار المنتج بشكل أفقي
                        : Row(
                          children: [
                            Expanded(
                              child: DropdownButtonFormField(
                                decoration: InputDecoration(
                                  labelText: 'المنتج',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: ScreenSize.getPadding(16),
                                    vertical: ScreenSize.getPadding(12),
                                  ),
                                  labelStyle: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                items:
                                    controller.availableProducts.map((product) {
                                      return DropdownMenuItem(
                                        value: product,
                                        child: TextUtils.responsiveText(
                                          '${product.name} (${product.code})',
                                          style: TextStyle(
                                            fontSize:
                                                ScreenSize.isSmallScreen
                                                    ? 12
                                                    : 14,
                                          ),
                                          maxLines: 1,
                                        ),
                                      );
                                    }).toList(),
                                onChanged: (value) {
                                  if (value != null) {
                                    controller.selectedProduct.value = value;
                                  }
                                },
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                ),
                              ),
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            SizedBox(
                              width: 100,
                              child: TextField(
                                decoration: InputDecoration(
                                  labelText: 'العدد',
                                  border: OutlineInputBorder(),
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: ScreenSize.getPadding(16),
                                    vertical: ScreenSize.getPadding(12),
                                  ),
                                  labelStyle: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                  ),
                                ),
                                keyboardType: TextInputType.number,
                                onChanged: (value) {
                                  controller.selectedQuantity.value =
                                      int.tryParse(value) ?? 1;
                                },
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                ),
                              ),
                            ),
                            SizedBox(width: ScreenSize.getPadding(8)),
                            ElevatedButton(
                              onPressed: () {
                                controller.addProductToSale();
                              },
                              style: ElevatedButton.styleFrom(
                                padding: EdgeInsets.symmetric(
                                  horizontal: ScreenSize.getPadding(16),
                                  vertical: ScreenSize.getPadding(12),
                                ),
                              ),
                              child: TextUtils.responsiveText(
                                'إضافة',
                                style: TextStyle(
                                  fontSize: ScreenSize.isSmallScreen ? 12 : 14,
                                ),
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                    SizedBox(height: ScreenSize.getPadding(16)),

                    // قائمة المنتجات المضافة
                    TextUtils.responsiveText(
                      'المنتجات المضافة',
                      style: TextStyle(
                        fontSize: ScreenSize.isSmallScreen ? 14 : 16,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 1,
                    ),
                    SizedBox(height: ScreenSize.getPadding(8)),
                    Expanded(
                      child:
                          controller.newSaleItems.isEmpty
                              ? Center(
                                child: TextUtils.responsiveText(
                                  'لم يتم إضافة منتجات بعد',
                                  style: TextStyle(
                                    fontSize:
                                        ScreenSize.isSmallScreen ? 12 : 14,
                                    color: Colors.grey.shade600,
                                  ),
                                  maxLines: 1,
                                  textAlign: TextAlign.center,
                                ),
                              )
                              : ListView.separated(
                                itemCount: controller.newSaleItems.length,
                                separatorBuilder:
                                    (context, index) => Divider(
                                      height: ScreenSize.getPadding(1),
                                    ),
                                itemBuilder: (context, index) {
                                  final item = controller.newSaleItems[index];
                                  return ListTile(
                                    contentPadding: EdgeInsets.symmetric(
                                      horizontal: ScreenSize.getPadding(8),
                                      vertical: ScreenSize.getPadding(4),
                                    ),
                                    title: TextUtils.responsiveText(
                                      item.productName,
                                      style: TextStyle(
                                        fontSize:
                                            ScreenSize.isSmallScreen ? 12 : 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      maxLines: 1,
                                    ),
                                    subtitle: TextUtils.responsiveText(
                                      'العدد: ${item.quantity}',
                                      style: TextStyle(
                                        fontSize:
                                            ScreenSize.isSmallScreen ? 11 : 13,
                                        color: Colors.grey.shade700,
                                      ),
                                      maxLines: 1,
                                    ),
                                    trailing: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        TextUtils.responsiveText(
                                          FormatUtils.formatCurrency(
                                            item.totalPrice,
                                          ),
                                          style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize:
                                                ScreenSize.isSmallScreen
                                                    ? 11
                                                    : 13,
                                          ),
                                          maxLines: 1,
                                        ),
                                        IconButton(
                                          icon: Icon(
                                            Icons.delete,
                                            color: Colors.red,
                                            size:
                                                ScreenSize.isSmallScreen
                                                    ? 18
                                                    : 20,
                                          ),
                                          constraints: BoxConstraints(
                                            minWidth: ScreenSize.getPadding(36),
                                            minHeight: ScreenSize.getPadding(
                                              36,
                                            ),
                                          ),
                                          padding: EdgeInsets.all(
                                            ScreenSize.getPadding(4),
                                          ),
                                          onPressed: () {
                                            controller.removeProductFromSale(
                                              index,
                                            );
                                          },
                                        ),
                                      ],
                                    ),
                                  );
                                },
                              ),
                    ),
                    SizedBox(height: ScreenSize.getPadding(16)),

                    // الإجمالي
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        TextUtils.responsiveText(
                          'الإجمالي:',
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.bold,
                          ),
                          maxLines: 1,
                        ),
                        TextUtils.responsiveText(
                          FormatUtils.formatCurrency(
                            controller.calculateTotalAmount(),
                          ),
                          style: TextStyle(
                            fontSize: ScreenSize.isSmallScreen ? 16 : 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green,
                          ),
                          maxLines: 1,
                        ),
                      ],
                    ),
                  ],
                );
              }),
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              style: TextButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenSize.getPadding(16),
                  vertical: ScreenSize.getPadding(8),
                ),
              ),
              child: TextUtils.responsiveText(
                'إلغاء',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                maxLines: 1,
              ),
            ),
            ElevatedButton(
              onPressed: () {
                if (controller.validateNewSale()) {
                  controller.saveSale();
                  Navigator.pop(context);
                } else {
                  // عرض رسالة خطأ
                  Get.snackbar(
                    'خطأ',
                    'يرجى التأكد من إدخال جميع البيانات المطلوبة وإضافة منتج واحد على الأقل',
                    backgroundColor: Colors.red,
                    colorText: Colors.white,
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                padding: EdgeInsets.symmetric(
                  horizontal: ScreenSize.getPadding(16),
                  vertical: ScreenSize.getPadding(8),
                ),
              ),
              child: TextUtils.responsiveText(
                'حفظ',
                style: TextStyle(fontSize: ScreenSize.isSmallScreen ? 12 : 14),
                maxLines: 1,
              ),
            ),
          ],
        );
      },
    );
  }
}
