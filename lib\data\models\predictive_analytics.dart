/// نماذج البيانات للتحليل التنبؤي والذكاء الاصطناعي
library;

/// نموذج التنبؤ بالأعطال
class FailurePrediction {
  final String id;
  final String machineId;
  final String machineName;
  final DateTime predictedFailureDate;
  final double failureProbability; // احتمالية الفشل (0-100%)
  final FailureType predictedFailureType;
  final int daysUntilFailure;
  final double confidenceLevel; // مستوى الثقة في التنبؤ (0-100%)
  final List<String> contributingFactors; // العوامل المساهمة
  final String recommendedAction;
  final DateTime createdAt;
  final PredictionStatus status;

  FailurePrediction({
    required this.id,
    required this.machineId,
    required this.machineName,
    required this.predictedFailureDate,
    required this.failureProbability,
    required this.predictedFailureType,
    required this.daysUntilFailure,
    required this.confidenceLevel,
    required this.contributingFactors,
    required this.recommendedAction,
    required this.createdAt,
    required this.status,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'machineId': machineId,
      'machineName': machineName,
      'predictedFailureDate': predictedFailureDate.toIso8601String(),
      'failureProbability': failureProbability,
      'predictedFailureType': predictedFailureType.index,
      'daysUntilFailure': daysUntilFailure,
      'confidenceLevel': confidenceLevel,
      'contributingFactors': contributingFactors.join('|'),
      'recommendedAction': recommendedAction,
      'createdAt': createdAt.toIso8601String(),
      'status': status.index,
    };
  }

  // إنشاء من Map
  factory FailurePrediction.fromMap(Map<String, dynamic> map) {
    return FailurePrediction(
      id: map['id'],
      machineId: map['machineId'],
      machineName: map['machineName'],
      predictedFailureDate: DateTime.parse(map['predictedFailureDate']),
      failureProbability: map['failureProbability'],
      predictedFailureType: FailureType.values[map['predictedFailureType']],
      daysUntilFailure: map['daysUntilFailure'],
      confidenceLevel: map['confidenceLevel'],
      contributingFactors: (map['contributingFactors'] as String).split('|'),
      recommendedAction: map['recommendedAction'],
      createdAt: DateTime.parse(map['createdAt']),
      status: PredictionStatus.values[map['status']],
    );
  }
}

/// أنواع الأعطال المتوقعة
enum FailureType {
  mechanical, // ميكانيكي
  electrical, // كهربائي
  hydraulic, // هيدروليكي
  software, // برمجي
  wear, // تآكل
  overheating, // ارتفاع حرارة
  vibration, // اهتزاز
  lubrication, // تشحيم
}

/// حالة التنبؤ
enum PredictionStatus {
  active, // نشط
  resolved, // تم حله
  falsePositive, // إنذار كاذب
  expired, // منتهي الصلاحية
}

/// نموذج التوقع المستقبلي
class FutureForecast {
  final String id;
  final ForecastType type;
  final String title;
  final String description;
  final DateTime forecastDate;
  final int forecastDays; // عدد الأيام المتوقعة
  final Map<String, double> predictions; // التوقعات بالقيم
  final double accuracy; // دقة التوقع (0-100%)
  final List<String> assumptions; // الافتراضات
  final List<String> recommendations; // التوصيات
  final DateTime createdAt;

  FutureForecast({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.forecastDate,
    required this.forecastDays,
    required this.predictions,
    required this.accuracy,
    required this.assumptions,
    required this.recommendations,
    required this.createdAt,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'title': title,
      'description': description,
      'forecastDate': forecastDate.toIso8601String(),
      'forecastDays': forecastDays,
      'predictions': _encodePredictions(predictions),
      'accuracy': accuracy,
      'assumptions': assumptions.join('|'),
      'recommendations': recommendations.join('|'),
      'createdAt': createdAt.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory FutureForecast.fromMap(Map<String, dynamic> map) {
    return FutureForecast(
      id: map['id'],
      type: ForecastType.values[map['type']],
      title: map['title'],
      description: map['description'],
      forecastDate: DateTime.parse(map['forecastDate']),
      forecastDays: map['forecastDays'],
      predictions: _decodePredictions(map['predictions']),
      accuracy: map['accuracy'],
      assumptions: (map['assumptions'] as String).split('|'),
      recommendations: (map['recommendations'] as String).split('|'),
      createdAt: DateTime.parse(map['createdAt']),
    );
  }

  // تشفير التوقعات
  static String _encodePredictions(Map<String, double> predictions) {
    return predictions.entries.map((e) => '${e.key}:${e.value}').join(',');
  }

  // فك تشفير التوقعات
  static Map<String, double> _decodePredictions(String encoded) {
    final predictions = <String, double>{};
    if (encoded.isNotEmpty) {
      final pairs = encoded.split(',');
      for (var pair in pairs) {
        final keyValue = pair.split(':');
        if (keyValue.length == 2) {
          predictions[keyValue[0]] = double.tryParse(keyValue[1]) ?? 0.0;
        }
      }
    }
    return predictions;
  }
}

/// أنواع التوقعات
enum ForecastType {
  production, // توقعات الإنتاج
  maintenance, // توقعات الصيانة
  financial, // توقعات مالية
  quality, // توقعات الجودة
  efficiency, // توقعات الكفاءة
  demand, // توقعات الطلب
}

/// نموذج تحليل الاتجاه
class TrendAnalysis {
  final String id;
  final String metricName;
  final TrendDirection direction;
  final double trendStrength; // قوة الاتجاه (0-100%)
  final double changeRate; // معدل التغيير اليومي/الأسبوعي
  final List<DataPoint> historicalData;
  final List<DataPoint> predictedData;
  final DateTime analysisDate;
  final int analysisWindow; // نافزة التحليل بالأيام

  TrendAnalysis({
    required this.id,
    required this.metricName,
    required this.direction,
    required this.trendStrength,
    required this.changeRate,
    required this.historicalData,
    required this.predictedData,
    required this.analysisDate,
    required this.analysisWindow,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'metricName': metricName,
      'direction': direction.index,
      'trendStrength': trendStrength,
      'changeRate': changeRate,
      'historicalData': historicalData.map((d) => d.toMap()).toList(),
      'predictedData': predictedData.map((d) => d.toMap()).toList(),
      'analysisDate': analysisDate.toIso8601String(),
      'analysisWindow': analysisWindow,
    };
  }

  // إنشاء من Map
  factory TrendAnalysis.fromMap(Map<String, dynamic> map) {
    return TrendAnalysis(
      id: map['id'],
      metricName: map['metricName'],
      direction: TrendDirection.values[map['direction']],
      trendStrength: map['trendStrength'],
      changeRate: map['changeRate'],
      historicalData:
          (map['historicalData'] as List)
              .map((d) => DataPoint.fromMap(d))
              .toList(),
      predictedData:
          (map['predictedData'] as List)
              .map((d) => DataPoint.fromMap(d))
              .toList(),
      analysisDate: DateTime.parse(map['analysisDate']),
      analysisWindow: map['analysisWindow'],
    );
  }
}

/// اتجاه الترند
enum TrendDirection {
  increasing, // متزايد
  decreasing, // متناقص
  stable, // مستقر
  volatile, // متقلب
}

/// نقطة بيانات
class DataPoint {
  final DateTime date;
  final double value;
  final Map<String, dynamic>? metadata;

  DataPoint({required this.date, required this.value, this.metadata});

  Map<String, dynamic> toMap() {
    return {
      'date': date.toIso8601String(),
      'value': value,
      'metadata': metadata,
    };
  }

  factory DataPoint.fromMap(Map<String, dynamic> map) {
    return DataPoint(
      date: DateTime.parse(map['date']),
      value: map['value'],
      metadata: map['metadata'],
    );
  }
}

/// نموذج التحليل الإحصائي
class StatisticalAnalysis {
  final String id;
  final String datasetName;
  final int sampleSize;
  final double mean; // المتوسط
  final double median; // الوسيط
  final double standardDeviation; // الانحراف المعياري
  final double variance; // التباين
  final double min; // القيمة الصغرى
  final double max; // القيمة الكبرى
  final double skewness; // الالتواء
  final double kurtosis; // التفلطح
  final List<double> quartiles; // الأرباع
  final DateTime analysisDate;

  StatisticalAnalysis({
    required this.id,
    required this.datasetName,
    required this.sampleSize,
    required this.mean,
    required this.median,
    required this.standardDeviation,
    required this.variance,
    required this.min,
    required this.max,
    required this.skewness,
    required this.kurtosis,
    required this.quartiles,
    required this.analysisDate,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'datasetName': datasetName,
      'sampleSize': sampleSize,
      'mean': mean,
      'median': median,
      'standardDeviation': standardDeviation,
      'variance': variance,
      'min': min,
      'max': max,
      'skewness': skewness,
      'kurtosis': kurtosis,
      'quartiles': quartiles,
      'analysisDate': analysisDate.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory StatisticalAnalysis.fromMap(Map<String, dynamic> map) {
    return StatisticalAnalysis(
      id: map['id'],
      datasetName: map['datasetName'],
      sampleSize: map['sampleSize'],
      mean: map['mean'],
      median: map['median'],
      standardDeviation: map['standardDeviation'],
      variance: map['variance'],
      min: map['min'],
      max: map['max'],
      skewness: map['skewness'],
      kurtosis: map['kurtosis'],
      quartiles: List<double>.from(map['quartiles']),
      analysisDate: DateTime.parse(map['analysisDate']),
    );
  }
}

/// نموذج الارتباط بين المتغيرات
class CorrelationAnalysis {
  final String id;
  final String variable1;
  final String variable2;
  final double correlationCoefficient; // معامل الارتباط (-1 إلى 1)
  final CorrelationStrength strength;
  final CorrelationType type;
  final double pValue; // القيمة الاحتمالية
  final bool isSignificant; // هل الارتباط معنوي؟
  final int sampleSize;
  final DateTime analysisDate;

  CorrelationAnalysis({
    required this.id,
    required this.variable1,
    required this.variable2,
    required this.correlationCoefficient,
    required this.strength,
    required this.type,
    required this.pValue,
    required this.isSignificant,
    required this.sampleSize,
    required this.analysisDate,
  });

  // تحويل إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'variable1': variable1,
      'variable2': variable2,
      'correlationCoefficient': correlationCoefficient,
      'strength': strength.index,
      'type': type.index,
      'pValue': pValue,
      'isSignificant': isSignificant,
      'sampleSize': sampleSize,
      'analysisDate': analysisDate.toIso8601String(),
    };
  }

  // إنشاء من Map
  factory CorrelationAnalysis.fromMap(Map<String, dynamic> map) {
    return CorrelationAnalysis(
      id: map['id'],
      variable1: map['variable1'],
      variable2: map['variable2'],
      correlationCoefficient: map['correlationCoefficient'],
      strength: CorrelationStrength.values[map['strength']],
      type: CorrelationType.values[map['type']],
      pValue: map['pValue'],
      isSignificant: map['isSignificant'],
      sampleSize: map['sampleSize'],
      analysisDate: DateTime.parse(map['analysisDate']),
    );
  }
}

/// قوة الارتباط
enum CorrelationStrength {
  veryWeak, // ضعيف جداً (0-0.2)
  weak, // ضعيف (0.2-0.4)
  moderate, // متوسط (0.4-0.6)
  strong, // قوي (0.6-0.8)
  veryStrong, // قوي جداً (0.8-1.0)
}

/// نوع الارتباط
enum CorrelationType {
  positive, // إيجابي
  negative, // سلبي
  noCorrelation, // لا يوجد ارتباط
}

/// إضافات مساعدة
extension FailureTypeExtension on FailureType {
  String get displayName {
    switch (this) {
      case FailureType.mechanical:
        return 'ميكانيكي';
      case FailureType.electrical:
        return 'كهربائي';
      case FailureType.hydraulic:
        return 'هيدروليكي';
      case FailureType.software:
        return 'برمجي';
      case FailureType.wear:
        return 'تآكل';
      case FailureType.overheating:
        return 'ارتفاع حرارة';
      case FailureType.vibration:
        return 'اهتزاز';
      case FailureType.lubrication:
        return 'تشحيم';
    }
  }
}

extension ForecastTypeExtension on ForecastType {
  String get displayName {
    switch (this) {
      case ForecastType.production:
        return 'توقعات الإنتاج';
      case ForecastType.maintenance:
        return 'توقعات الصيانة';
      case ForecastType.financial:
        return 'توقعات مالية';
      case ForecastType.quality:
        return 'توقعات الجودة';
      case ForecastType.efficiency:
        return 'توقعات الكفاءة';
      case ForecastType.demand:
        return 'توقعات الطلب';
    }
  }
}

extension TrendDirectionExtension on TrendDirection {
  String get displayName {
    switch (this) {
      case TrendDirection.increasing:
        return 'متزايد';
      case TrendDirection.decreasing:
        return 'متناقص';
      case TrendDirection.stable:
        return 'مستقر';
      case TrendDirection.volatile:
        return 'متقلب';
    }
  }
}

extension CorrelationStrengthExtension on CorrelationStrength {
  String get displayName {
    switch (this) {
      case CorrelationStrength.veryWeak:
        return 'ضعيف جداً';
      case CorrelationStrength.weak:
        return 'ضعيف';
      case CorrelationStrength.moderate:
        return 'متوسط';
      case CorrelationStrength.strong:
        return 'قوي';
      case CorrelationStrength.veryStrong:
        return 'قوي جداً';
    }
  }
}

extension CorrelationTypeExtension on CorrelationType {
  String get displayName {
    switch (this) {
      case CorrelationType.positive:
        return 'إيجابي';
      case CorrelationType.negative:
        return 'سلبي';
      case CorrelationType.noCorrelation:
        return 'لا يوجد ارتباط';
    }
  }
}

/// نموذج التنبيه التنبؤي
class PredictiveNotification {
  final String id;
  final PredictiveNotificationType type;
  final String title;
  final String message;
  final NotificationSeverity severity;
  final Map<String, dynamic> relatedData;
  final String actionRequired;
  final DateTime createdAt;
  final bool isRead;
  final bool isActionTaken;

  PredictiveNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.severity,
    required this.relatedData,
    required this.actionRequired,
    required this.createdAt,
    this.isRead = false,
    this.isActionTaken = false,
  });

  PredictiveNotification copyWith({
    String? id,
    PredictiveNotificationType? type,
    String? title,
    String? message,
    NotificationSeverity? severity,
    Map<String, dynamic>? relatedData,
    String? actionRequired,
    DateTime? createdAt,
    bool? isRead,
    bool? isActionTaken,
  }) {
    return PredictiveNotification(
      id: id ?? this.id,
      type: type ?? this.type,
      title: title ?? this.title,
      message: message ?? this.message,
      severity: severity ?? this.severity,
      relatedData: relatedData ?? this.relatedData,
      actionRequired: actionRequired ?? this.actionRequired,
      createdAt: createdAt ?? this.createdAt,
      isRead: isRead ?? this.isRead,
      isActionTaken: isActionTaken ?? this.isActionTaken,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.index,
      'title': title,
      'message': message,
      'severity': severity.index,
      'relatedData': relatedData,
      'actionRequired': actionRequired,
      'createdAt': createdAt.toIso8601String(),
      'isRead': isRead,
      'isActionTaken': isActionTaken,
    };
  }

  factory PredictiveNotification.fromMap(Map<String, dynamic> map) {
    return PredictiveNotification(
      id: map['id'],
      type: PredictiveNotificationType.values[map['type']],
      title: map['title'],
      message: map['message'],
      severity: NotificationSeverity.values[map['severity']],
      relatedData: Map<String, dynamic>.from(map['relatedData']),
      actionRequired: map['actionRequired'],
      createdAt: DateTime.parse(map['createdAt']),
      isRead: map['isRead'] ?? false,
      isActionTaken: map['isActionTaken'] ?? false,
    );
  }
}

/// أنواع التنبيهات التنبؤية
enum PredictiveNotificationType {
  failurePrediction, // تنبؤ بالأعطال
  trendAlert, // تنبيه الاتجاهات
  correlationInsight, // رؤى الارتباطات
  forecastAlert, // تنبيه التوقعات
  dailySummary, // ملخص يومي
  weeklyReport, // تقرير أسبوعي
  maintenanceReminder, // تذكير صيانة
  performanceReview, // مراجعة أداء
}

/// شدة التنبيه
enum NotificationSeverity {
  critical, // حرج
  warning, // تحذير
  info, // معلومات
  success, // نجح
}

/// نموذج التنبيه المجدول
class ScheduledNotification {
  final String id;
  final String name;
  final ScheduledNotificationType type;
  final String description;
  final NotificationSchedule schedule;
  final DateTime startDate;
  final DateTime? endDate;
  final bool isActive;
  final Map<String, dynamic> parameters;
  DateTime nextExecutionTime;

  ScheduledNotification({
    required this.id,
    required this.name,
    required this.type,
    required this.description,
    required this.schedule,
    required this.startDate,
    this.endDate,
    this.isActive = true,
    this.parameters = const {},
    required this.nextExecutionTime,
  });

  void updateNextExecution() {
    switch (schedule) {
      case NotificationSchedule.immediate:
        // لا يحتاج تحديث
        break;
      case NotificationSchedule.daily:
        nextExecutionTime = nextExecutionTime.add(const Duration(days: 1));
        break;
      case NotificationSchedule.weekly:
        nextExecutionTime = nextExecutionTime.add(const Duration(days: 7));
        break;
      case NotificationSchedule.monthly:
        nextExecutionTime = DateTime(
          nextExecutionTime.year,
          nextExecutionTime.month + 1,
          nextExecutionTime.day,
          nextExecutionTime.hour,
          nextExecutionTime.minute,
        );
        break;
      case NotificationSchedule.custom:
        // يحتاج تحديد مخصص
        break;
    }
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.index,
      'description': description,
      'schedule': schedule.index,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'isActive': isActive,
      'parameters': parameters,
      'nextExecutionTime': nextExecutionTime.toIso8601String(),
    };
  }

  factory ScheduledNotification.fromMap(Map<String, dynamic> map) {
    return ScheduledNotification(
      id: map['id'],
      name: map['name'],
      type: ScheduledNotificationType.values[map['type']],
      description: map['description'],
      schedule: NotificationSchedule.values[map['schedule']],
      startDate: DateTime.parse(map['startDate']),
      endDate: map['endDate'] != null ? DateTime.parse(map['endDate']) : null,
      isActive: map['isActive'] ?? true,
      parameters: Map<String, dynamic>.from(map['parameters'] ?? {}),
      nextExecutionTime: DateTime.parse(map['nextExecutionTime']),
    );
  }
}

/// أنواع التنبيهات المجدولة
enum ScheduledNotificationType {
  dailySummary, // ملخص يومي
  weeklyReport, // تقرير أسبوعي
  maintenanceReminder, // تذكير صيانة
  performanceReview, // مراجعة أداء
}

/// جدولة التنبيهات
enum NotificationSchedule {
  immediate, // فوري
  daily, // يومي
  weekly, // أسبوعي
  monthly, // شهري
  custom, // مخصص
}

/// نموذج قاعدة التنبيه المخصصة
class CustomNotificationRule {
  final String id;
  final String name;
  final String description;
  final bool isEnabled;
  final List<NotificationCondition> conditions;
  final List<NotificationAction> actions;
  final NotificationSchedule schedule;
  final Map<String, dynamic> parameters;

  CustomNotificationRule({
    required this.id,
    required this.name,
    this.description = '',
    this.isEnabled = true,
    required this.conditions,
    required this.actions,
    required this.schedule,
    this.parameters = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'isEnabled': isEnabled,
      'conditions': conditions.map((c) => c.toMap()).toList(),
      'actions': actions.map((a) => a.index).toList(),
      'schedule': schedule.index,
      'parameters': parameters,
    };
  }

  factory CustomNotificationRule.fromMap(Map<String, dynamic> map) {
    return CustomNotificationRule(
      id: map['id'],
      name: map['name'],
      description: map['description'] ?? '',
      isEnabled: map['isEnabled'] ?? true,
      conditions:
          (map['conditions'] as List)
              .map((c) => NotificationCondition.fromMap(c))
              .toList(),
      actions:
          (map['actions'] as List)
              .map((a) => NotificationAction.values[a])
              .toList(),
      schedule: NotificationSchedule.values[map['schedule']],
      parameters: Map<String, dynamic>.from(map['parameters'] ?? {}),
    );
  }
}

/// شرط التنبيه
class NotificationCondition {
  final String parameter;
  final ConditionOperator operator;
  final double value;
  final String? stringValue;

  NotificationCondition({
    required this.parameter,
    required this.operator,
    required this.value,
    this.stringValue,
  });

  Map<String, dynamic> toMap() {
    return {
      'parameter': parameter,
      'operator': operator.index,
      'value': value,
      'stringValue': stringValue,
    };
  }

  factory NotificationCondition.fromMap(Map<String, dynamic> map) {
    return NotificationCondition(
      parameter: map['parameter'],
      operator: ConditionOperator.values[map['operator']],
      value: map['value'],
      stringValue: map['stringValue'],
    );
  }
}

/// عوامل المقارنة
enum ConditionOperator {
  equals, // يساوي
  notEquals, // لا يساوي
  greaterThan, // أكبر من
  lessThan, // أقل من
  greaterEqual, // أكبر من أو يساوي
  lessEqual, // أقل من أو يساوي
  contains, // يحتوي على
  notContains, // لا يحتوي على
}

/// إجراءات التنبيه
enum NotificationAction {
  sendNotification, // إرسال تنبيه
  sendEmail, // إرسال إيميل
  sendSms, // إرسال رسالة نصية
  createTask, // إنشاء مهمة
  logEvent, // تسجيل حدث
}

/// إضافات مساعدة للتنبيهات التنبؤية
extension PredictiveNotificationTypeExtension on PredictiveNotificationType {
  String get displayName {
    switch (this) {
      case PredictiveNotificationType.failurePrediction:
        return 'تنبؤ بالأعطال';
      case PredictiveNotificationType.trendAlert:
        return 'تنبيه الاتجاهات';
      case PredictiveNotificationType.correlationInsight:
        return 'رؤى الارتباطات';
      case PredictiveNotificationType.forecastAlert:
        return 'تنبيه التوقعات';
      case PredictiveNotificationType.dailySummary:
        return 'ملخص يومي';
      case PredictiveNotificationType.weeklyReport:
        return 'تقرير أسبوعي';
      case PredictiveNotificationType.maintenanceReminder:
        return 'تذكير صيانة';
      case PredictiveNotificationType.performanceReview:
        return 'مراجعة أداء';
    }
  }
}

extension NotificationSeverityExtension on NotificationSeverity {
  String get displayName {
    switch (this) {
      case NotificationSeverity.critical:
        return 'حرج';
      case NotificationSeverity.warning:
        return 'تحذير';
      case NotificationSeverity.info:
        return 'معلومات';
      case NotificationSeverity.success:
        return 'نجح';
    }
  }
}
