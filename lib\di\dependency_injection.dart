import 'package:get/get.dart';
import 'package:mostafa_final/data/repositories/inventory_repository.dart';
import 'package:mostafa_final/data/repositories/machine_repository.dart';
import 'package:mostafa_final/data/repositories/mold_change_history_repository.dart';
import 'package:mostafa_final/data/repositories/mold_repository.dart';
import 'package:mostafa_final/data/repositories/production_repository.dart';
import 'package:mostafa_final/data/repositories/raw_material_repository.dart';
import 'package:mostafa_final/data/repositories/order_repository.dart';
import 'package:mostafa_final/data/repositories/sales_repository.dart';
import 'package:mostafa_final/data/repositories/product_repository.dart';
import 'package:mostafa_final/data/repositories/assembly_repository.dart';
import 'package:mostafa_final/data/repositories/injection_cost_repository.dart';
import 'package:mostafa_final/data/repositories/production_plan_repository.dart';
import 'package:mostafa_final/data/repositories/worker_repository.dart';
import 'package:mostafa_final/data/repositories/operation_cost_repository.dart';
import 'package:mostafa_final/presentation/controllers/cost_calculation_controller.dart';
import 'package:mostafa_final/presentation/controllers/backup_controller.dart';
import 'package:mostafa_final/presentation/controllers/performance_test_controller.dart';

class DependencyInjection {
  static void init() {
    // تسجيل المستودعات
    Get.lazyPut<RawMaterialRepository>(
      () => RawMaterialRepository(),
      fenix: true,
    );
    Get.lazyPut<MachineRepository>(() => MachineRepository(), fenix: true);
    Get.lazyPut<MoldRepository>(() => MoldRepository(), fenix: true);
    Get.lazyPut<MoldChangeHistoryRepository>(
      () => MoldChangeHistoryRepository(),
      fenix: true,
    );
    Get.lazyPut<ProductionRepository>(
      () => ProductionRepository(),
      fenix: true,
    );
    Get.lazyPut<InventoryRepository>(() => InventoryRepository(), fenix: true);
    Get.lazyPut<OrderRepository>(() => OrderRepository(), fenix: true);
    Get.lazyPut<SalesRepository>(() => SalesRepository(), fenix: true);
    Get.lazyPut<ProductRepository>(() => ProductRepository(), fenix: true);
    Get.lazyPut<AssemblyRepository>(() => AssemblyRepository(), fenix: true);
    Get.lazyPut<InjectionCostRepository>(
      () => InjectionCostRepository(),
      fenix: true,
    );
    Get.lazyPut<ProductionPlanRepository>(
      () => ProductionPlanRepository(),
      fenix: true,
    );
    Get.lazyPut<WorkerRepository>(() => WorkerRepository(), fenix: true);
    Get.lazyPut<OperationCostRepository>(
      () => OperationCostRepository(),
      fenix: true,
    );

    // تسجيل وحدات التحكم
    Get.lazyPut<CostCalculationController>(
      () => CostCalculationController(),
      fenix: true,
    );

    // تسجيل وحدة تحكم النسخ الاحتياطي
    Get.lazyPut<BackupController>(() => BackupController(), fenix: true);

    // تسجيل وحدة تحكم اختبارات الأداء
    Get.lazyPut<PerformanceTestController>(
      () => PerformanceTestController(),
      fenix: true,
    );

    // يمكن إضافة المزيد من التبعيات هنا (وحدات التحكم، الخدمات، إلخ)
  }
}
