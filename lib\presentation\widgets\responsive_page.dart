import 'package:flutter/material.dart';
import 'package:mostafa_final/presentation/theme/screen_size.dart';
import 'package:mostafa_final/presentation/widgets/text_utils.dart';

/// صفحة متجاوبة يمكن استخدامها كأساس لجميع صفحات التطبيق
class ResponsivePage extends StatelessWidget {
  final String title;
  final Widget body;
  final Widget? drawer;
  final Widget? floatingActionButton;
  final List<Widget>? actions;
  final bool centerTitle;
  final bool automaticallyImplyLeading;
  final Widget? leading;
  final Color? backgroundColor;
  final bool resizeToAvoidBottomInset;
  final bool extendBodyBehindAppBar;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final bool useSafeArea;
  final EdgeInsetsGeometry? padding;

  const ResponsivePage({
    super.key,
    required this.title,
    required this.body,
    this.drawer,
    this.floatingActionButton,
    this.actions,
    this.centerTitle = true,
    this.automaticallyImplyLeading = true,
    this.leading,
    this.backgroundColor,
    this.resizeToAvoidBottomInset = true,
    this.extendBodyBehindAppBar = false,
    this.appBar,
    this.bottomNavigationBar,
    this.useSafeArea = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    // تهيئة أحجام الشاشة
    ScreenSize.init(context);

    final Widget content = Padding(
      padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
      child: body,
    );

    final Widget scaffold = Scaffold(
      appBar:
          appBar ??
          AppBar(
            title: TextUtils.responsiveText(
              title,
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            centerTitle: centerTitle,
            automaticallyImplyLeading: automaticallyImplyLeading,
            leading: leading,
            actions: actions,
          ),
      drawer: drawer,
      body: useSafeArea ? SafeArea(child: content) : content,
      floatingActionButton: floatingActionButton,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
      bottomNavigationBar: bottomNavigationBar,
    );

    return scaffold;
  }

  /// إنشاء صفحة متجاوبة مع شريط تمرير
  static Widget withScrollView({
    required String title,
    required List<Widget> children,
    Widget? drawer,
    Widget? floatingActionButton,
    List<Widget>? actions,
    bool centerTitle = true,
    bool automaticallyImplyLeading = true,
    Widget? leading,
    Color? backgroundColor,
    bool resizeToAvoidBottomInset = true,
    bool extendBodyBehindAppBar = false,
    PreferredSizeWidget? appBar,
    Widget? bottomNavigationBar,
    bool useSafeArea = true,
    EdgeInsetsGeometry? padding,
    ScrollPhysics? physics,
    bool reverse = false,
    ScrollController? controller,
  }) {
    return ResponsivePage(
      title: title,
      drawer: drawer,
      floatingActionButton: floatingActionButton,
      actions: actions,
      centerTitle: centerTitle,
      automaticallyImplyLeading: automaticallyImplyLeading,
      leading: leading,
      backgroundColor: backgroundColor,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
      appBar: appBar,
      bottomNavigationBar: bottomNavigationBar,
      useSafeArea: useSafeArea,
      padding: EdgeInsets.zero, // سيتم تطبيق الحشو داخل SingleChildScrollView
      body: SingleChildScrollView(
        physics: physics,
        reverse: reverse,
        controller: controller,
        child: Padding(
          padding: padding ?? EdgeInsets.all(ScreenSize.getPadding(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: children,
          ),
        ),
      ),
    );
  }
}
